using UnityEngine;
using System;

namespace Workshop.UXLab.Data
{
    [Serializable]
    public abstract class CardCutoutData
    {
        [Tooltip("Position of the cutout center relative to the card center (X, Y) in meters.")]
        public Vector2 Position;

        // Abstract method to define the type or handle instantiation/drawing later
        // public abstract void ApplyCutout(someMeshGenerationContext context);

        protected CardCutoutData()
        {
            Position = Vector2.zero;
        }

        /// <summary>
        /// Creates a deep copy of this cutout data.
        /// </summary>
        public abstract CardCutoutData Clone();
    }
} 
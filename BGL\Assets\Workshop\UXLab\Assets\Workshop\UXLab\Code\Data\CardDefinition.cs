using UnityEngine;

namespace Workshop.UXLab.Data
{
    [CreateAssetMenu(fileName = "NewCardDefinition", menuName = "UX Lab/Card Definition")]
    public class CardDefinition : ScriptableObject
    {
        [Tooltip("User-friendly name for this card type (e.g., Poker, Tarot).")]
        public string CardName = "New Card";

        [TextArea]
        [Tooltip("Optional description.")]
        public string Description;

        [Tooltip("Physical dimensions and properties of the card.")]
        public CardSizeData SizeData = new CardSizeData();

        [Tooltip("The layout template defining the elements on this card.")]
        public CardLayoutData LayoutData = new CardLayoutData();
    }
}

using UnityEngine;
using System;
using System.Collections.Generic;

namespace Workshop.UXLab.Data
{
    [Serializable]
    public abstract class CardElementData
    {
        [Tooltip("Unique identifier or name for this element within the layout.")]
        public string ElementName;

        // --- Position & Anchor ---
        [Tooltip("X position offset relative to the specified Anchor point.")]
        public LengthValue PositionX;

        [Tooltip("Y position offset relative to the specified Anchor point.")]
        public LengthValue PositionY;

        [Tooltip("Anchor point on the card used as the origin for this element's Position.")]
        public CardAnchorPoint Anchor = CardAnchorPoint.TopLeft;

        // --- Rotation ---
        [<PERSON>lt<PERSON>("Rotation in degrees around the Z axis relative to the card.")]
        public float RotationDegrees;

        // --- Size ---
        [Toolt<PERSON>("Width of the element.")]
        public LengthValue Width;

        [Tooltip("Height of the element.")]
        public LengthValue Height;

        // --- Other ---
        [<PERSON>lt<PERSON>("Is this element initially visible?")]
        public bool IsVisible;

        // Abstract method or property to define the type, useful for instantiation logic
        public abstract Type GetElementType();

        // Constructor updated for mm defaults
        protected CardElementData(string name = "New Element", float width = 10f, float height = 10f) // Default 10mm size
        {
            ElementName = name;
            PositionX = LengthValue.FromMillimeters(0f);
            PositionY = LengthValue.FromMillimeters(0f);
            Anchor = CardAnchorPoint.TopLeft; // Changed default
            RotationDegrees = 0f;
            Width = LengthValue.FromMillimeters(width);
            Height = LengthValue.FromMillimeters(height);
            IsVisible = true;
        }

        // --- Legacy Compatibility Properties ---
        // These properties help with migration and backward compatibility

        /// <summary>
        /// Legacy compatibility property for Position vector.
        /// </summary>
        [System.Obsolete("Use PositionX and PositionY instead")]
        public Vector3 Position
        {
            get => new Vector3(PositionX.Value, PositionY.Value, 0.0f);
            set
            {
                PositionX = LengthValue.FromMillimeters(value.x);
                PositionY = LengthValue.FromMillimeters(value.y);
            }
        }

        /// <summary>
        /// Legacy compatibility property for Rotation vector.
        /// </summary>
        [System.Obsolete("Use RotationDegrees instead")]
        public Vector3 Rotation
        {
            get => new Vector3(0f, 0f, RotationDegrees);
            set => RotationDegrees = value.z;
        }
    }
}

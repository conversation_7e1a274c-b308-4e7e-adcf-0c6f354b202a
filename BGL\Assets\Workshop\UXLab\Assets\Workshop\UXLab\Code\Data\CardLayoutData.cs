using System;
using UnityEngine;
using System.Collections.Generic;

namespace Workshop.UXLab.Data
{
    [Serializable]
    public class CardLayoutData
    {
        [Toolt<PERSON>("User-friendly name for this layout template.")]
        public string LayoutName = "Default Layout";

        [TextArea]
        [Tooltip("Optional description for this layout.")]
        public string Description;

        // Using [SerializeReference] allows storing derived types (ImageElementData, TextElementData)
        [SerializeReference]
        [Tooltip("List of element definitions for this layout.")]
        public List<CardElementData> Elements = new List<CardElementData>();

        // TODO: Potentially add fields for safety margins here?
        // public float PrintSafetyMarginTop;
        // public float PrintSafetyMarginBottom;
        // etc. (in meters or maybe percentage?)
    }
}

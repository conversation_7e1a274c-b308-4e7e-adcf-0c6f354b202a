using UnityEngine;
using System; // Needed for [Serializable]
using System.Collections.Generic; // Added for List

namespace Workshop.UXLab.Data
{
    [Serializable]
    public class CardSizeData
    {
        // --- Dimensions and Corners stored in MILLIMETERS --- 
        [Tooltip("Width of the card in millimeters (mm).")]
        public float Width = 63f; // Poker width

        [Tooltip("Height of the card in millimeters (mm).")]
        public float Height = 88f; // Poker height

        [Tooltip("Thickness of the card stock in millimeters (mm).")]
        public float Thickness = 0.3f;

        [Tooltip("Use a single radius for all corners?")]
        public bool UseUniformCorners = true;

        [Tooltip("Radius for all corners in millimeters (mm) (if UseUniformCorners is true).")]
        public float UniformCornerRadius = 3f;

        [Tooltip("Top-left corner radius in millimeters (mm) (if UseUniformCorners is false).")]
        public float TopLeftCornerRadius = 3f;

        [Tooltip("Top-right corner radius in millimeters (mm) (if UseUniformCorners is false).")]
        public float TopRightCornerRadius = 3f;

        [Tooltip("Bottom-right corner radius in millimeters (mm) (if UseUniformCorners is false).")]
        public float BottomRightCornerRadius = 3f;

        [Tooltip("Bottom-left corner radius in millimeters (mm) (if UseUniformCorners is false).")]
        public float BottomLeftCornerRadius = 3f;

        [SerializeReference] // Allows storing CircleCutoutData, RectangleCutoutData etc.
        [Tooltip("List of cutouts (holes) to be applied to the card. Cutout dimensions/positions should also be in millimeters (mm).")]
        public List<CardCutoutData> Cutouts;

        // Constructor with default Poker size in mm
        public CardSizeData(float width = 63f, float height = 88f, float thickness = 0.3f, float cornerRadius = 3f)
        {
            Width = width;
            Height = height;
            Thickness = thickness;
            UseUniformCorners = true;
            UniformCornerRadius = cornerRadius;
            TopLeftCornerRadius = cornerRadius;
            TopRightCornerRadius = cornerRadius;
            BottomRightCornerRadius = cornerRadius;
            BottomLeftCornerRadius = cornerRadius;
            Cutouts = new List<CardCutoutData>(); // Initialize the list
        }

        /// <summary>
        /// Creates a new CardSizeData instance with standard Poker card dimensions.
        /// </summary>
        public static CardSizeData CreatePokerSize()
        {
            // Approx 2.5 x 3.5 inches = 63.5 x 88.9 mm, common radius ~3mm
            return new CardSizeData(63.5f, 88.9f, 0.3f, 3f);
        }

        /// <summary>
        /// Creates a deep copy of this CardSizeData instance.
        /// </summary>
        public CardSizeData Clone()
        {
            CardSizeData clone = new CardSizeData(
                this.Width,
                this.Height,
                this.Thickness,
                this.UseUniformCorners ? this.UniformCornerRadius : 3f // Use uniform or default if separate
            );
            clone.UseUniformCorners = this.UseUniformCorners;
            // Only copy separate radii if not uniform
            if (!this.UseUniformCorners) 
            { 
                clone.UniformCornerRadius = this.UniformCornerRadius; // Keep original uniform value just in case
                clone.TopLeftCornerRadius = this.TopLeftCornerRadius;
                clone.TopRightCornerRadius = this.TopRightCornerRadius;
                clone.BottomRightCornerRadius = this.BottomRightCornerRadius;
                clone.BottomLeftCornerRadius = this.BottomLeftCornerRadius;
            }

            // Deep copy cutouts if the list exists
            if (this.Cutouts != null) 
            {
                clone.Cutouts = new List<CardCutoutData>();
                foreach (var cutout in this.Cutouts)
                {
                    if (cutout != null)
                    {
                        // Assuming CardCutoutData has a Clone() method
                        clone.Cutouts.Add(cutout.Clone()); 
                    }
                }
            }
            else
            {
                clone.Cutouts = new List<CardCutoutData>();
            }
            
            return clone;
        }
    }
} 
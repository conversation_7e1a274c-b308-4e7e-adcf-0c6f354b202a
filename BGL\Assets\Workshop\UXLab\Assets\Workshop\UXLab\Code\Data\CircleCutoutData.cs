using UnityEngine;
using System;

namespace Workshop.UXLab.Data
{
    [Serializable]
    public class CircleCutoutData : CardCutoutData
    {
        [Tooltip("Radius of the circular cutout in meters.")]
        public float Radius = 0.005f; // Default 5mm radius

        public CircleCutoutData() : base()
        {
            Radius = 0.005f;
        }

        public override CardCutoutData Clone()
        {
            CircleCutoutData clone = new CircleCutoutData();
            clone.Position = this.Position; // Vector2 is struct, safe to copy directly
            clone.Radius = this.Radius;
            return clone;
        }
    }
} 
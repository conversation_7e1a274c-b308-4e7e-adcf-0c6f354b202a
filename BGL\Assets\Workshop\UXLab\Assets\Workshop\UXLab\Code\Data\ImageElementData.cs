using UnityEngine;
using System;

namespace Workshop.UXLab.Data
{
    [Serializable]
    public class ImageElementData : CardElementData
    {
        [Tooltip("Asset GUID of the Texture2D asset.")]
        public string TextureGuid; // More robust than Resources path

        [Tooltip("Tint color for the image.")]
        public Color TintColor = Color.white;

        public override Type GetElementType() => typeof(CardImageElement);

        public ImageElementData(string name = "New Image", float width = 50f, float height = 50f)
            : base(name, width, height)
        {
            TextureGuid = null; // Default to no texture
            TintColor = Color.white;
        }
    }
}

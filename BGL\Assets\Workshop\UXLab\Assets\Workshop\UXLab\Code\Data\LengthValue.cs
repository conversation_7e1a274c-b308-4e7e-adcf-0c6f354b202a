using System;
using UnityEngine;

namespace Workshop.UXLab.Data
{
    /// <summary>
    /// Represents a length measurement that can be in millimeters or as a percentage.
    /// Used for positions and sizes in card elements.
    /// </summary>
    [Serializable]
    public struct LengthValue
    {
        [Tooltip("The numeric value of the measurement.")]
        public float Value;

        [Toolt<PERSON>("The unit type for this measurement.")]
        public LengthUnit Unit;

        /// <summary>
        /// Creates a new LengthValue with the specified value and unit.
        /// </summary>
        /// <param name="value">The numeric value.</param>
        /// <param name="unit">The unit type (defaults to Millimeters).</param>
        public LengthValue(float value, LengthUnit unit = LengthUnit.Millimeters)
        {
            Value = value;
            Unit = unit;
        }

        /// <summary>
        /// Converts this length value to millimeters.
        /// </summary>
        /// <param name="referenceSize">The reference size in mm when converting from percentage.</param>
        /// <returns>The value in millimeters.</returns>
        public float ToMillimeters(float referenceSize)
        {
            return Unit == LengthUnit.Percentage ? Value * referenceSize : Value;
        }

        /// <summary>
        /// Converts this length value to a percentage.
        /// </summary>
        /// <param name="referenceSize">The reference size in mm when converting to percentage.</param>
        /// <returns>The value as a percentage (0-1).</returns>
        public float ToPercentage(float referenceSize)
        {
            return Unit == LengthUnit.Percentage ? Value : (referenceSize > 0 ? Value / referenceSize : 0);
        }

        /// <summary>
        /// Gets the value in the specified target unit.
        /// </summary>
        /// <param name="targetUnit">The desired unit.</param>
        /// <param name="referenceSize">The reference size in mm for percentage conversions.</param>
        /// <returns>The value in the target unit.</returns>
        public float GetValueInUnit(LengthUnit targetUnit, float referenceSize)
        {
            if (Unit == targetUnit)
                return Value;

            return targetUnit == LengthUnit.Millimeters 
                ? ToMillimeters(referenceSize) 
                : ToPercentage(referenceSize);
        }

        /// <summary>
        /// Creates a LengthValue from millimeters.
        /// </summary>
        public static LengthValue FromMillimeters(float value)
        {
            return new LengthValue(value, LengthUnit.Millimeters);
        }

        /// <summary>
        /// Creates a LengthValue from percentage (0-1).
        /// </summary>
        public static LengthValue FromPercentage(float value)
        {
            return new LengthValue(value, LengthUnit.Percentage);
        }

        /// <summary>
        /// Implicit conversion from float (assumes millimeters).
        /// </summary>
        public static implicit operator LengthValue(float value)
        {
            return new LengthValue(value, LengthUnit.Millimeters);
        }

        /// <summary>
        /// Returns a string representation of this length value.
        /// </summary>
        public override string ToString()
        {
            string unitSuffix = Unit == LengthUnit.Millimeters ? "mm" : "%";
            return $"{Value:F2}{unitSuffix}";
        }

        /// <summary>
        /// Checks if two LengthValue instances are equal.
        /// </summary>
        public override bool Equals(object obj)
        {
            if (!(obj is LengthValue)) return false;
            LengthValue other = (LengthValue)obj;
            return Mathf.Approximately(Value, other.Value) && Unit == other.Unit;
        }

        /// <summary>
        /// Gets the hash code for this instance.
        /// </summary>
        public override int GetHashCode()
        {
            return Value.GetHashCode() ^ Unit.GetHashCode();
        }

        /// <summary>
        /// Equality operator.
        /// </summary>
        public static bool operator ==(LengthValue left, LengthValue right)
        {
            return left.Equals(right);
        }

        /// <summary>
        /// Inequality operator.
        /// </summary>
        public static bool operator !=(LengthValue left, LengthValue right)
        {
            return !left.Equals(right);
        }
    }

    /// <summary>
    /// Defines the unit types for length measurements.
    /// </summary>
    public enum LengthUnit
    {
        /// <summary>
        /// Value is in millimeters (absolute units).
        /// </summary>
        Millimeters,

        /// <summary>
        /// Value is as a percentage (0-1) of a reference size.
        /// </summary>
        Percentage
    }
} 
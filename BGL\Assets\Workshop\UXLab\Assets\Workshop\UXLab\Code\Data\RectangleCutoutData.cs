using UnityEngine;
using System;

namespace Workshop.UXLab.Data
{
    [Serializable]
    public class RectangleCutoutData : CardCutoutData
    {
        [Tooltip("Width of the rectangular cutout in meters.")]
        public float Width = 0.01f; // Default 1cm width

        [Tooltip("Height of the rectangular cutout in meters.")]
        public float Height = 0.01f; // Default 1cm height

        [Tooltip("Rotation of the rectangle in degrees.")]
        public float Rotation = 0f;

        public RectangleCutoutData() : base()
        {
            Width = 0.01f;
            Height = 0.01f;
            Rotation = 0f;
        }

        public override CardCutoutData Clone()
        {
            RectangleCutoutData clone = new RectangleCutoutData();
            clone.Position = this.Position; // Vector2 is struct, safe to copy directly
            clone.Width = this.Width;
            clone.Height = this.Height;
            clone.Rotation = this.Rotation;
            return clone;
        }
    }
} 
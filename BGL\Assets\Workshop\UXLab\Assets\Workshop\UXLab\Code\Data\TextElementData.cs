using UnityEngine;
using System;

namespace Workshop.UXLab.Data
{
    [Serializable]
    public class TextElementData : CardElementData
    {
        [TextArea(3, 10)]
        [Tooltip("The text content.")]
        public string TextContent = "Text";

        [Tooltip("Font name to use for the text.")]
        public string FontName;

        [<PERSON><PERSON><PERSON>("Font size.")]
        public float FontSize = 10f;

        [<PERSON>lt<PERSON>("Font color.")]
        public Color FontColor = Color.black;

        [<PERSON>lt<PERSON>("Text alignment.")]
        public TextAnchor Alignment = TextAnchor.MiddleCenter;

        [Tooltip("Whether to automatically fit text to the element's bounds.")]
        public bool AutoFit = true; // Enable by default

        public override Type GetElementType() => typeof(CardTextElement); // Point to the actual component type

        public TextElementData(string name = "New Text", float width = 40f, float height = 20f)
            : base(name, width, height) // Default size in millimeters (4cm x 2cm)
        {
            TextContent = name;
            FontName = null; // Default to no specific font (TMP default)
            FontSize = 12f; // Slightly larger default font size
            FontColor = Color.black;
            Alignment = TextAnchor.MiddleCenter;
        }
    }
}

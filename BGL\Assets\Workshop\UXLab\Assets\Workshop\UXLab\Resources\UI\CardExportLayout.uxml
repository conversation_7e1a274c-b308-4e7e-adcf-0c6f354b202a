<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:appui="Unity.AppUI.UI" xmlns:editor="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" appui="Unity.AppUI.UI" noNamespaceSchemaLocation="../../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <ui:VisualElement name="export-container" class="export-container">
        <appui:Label name="export-header" class="export-header" text="Export Card" />
        
        <ui:VisualElement name="resolution-container" class="export-row">
            <appui:Label text="Resolution:" class="export-label" />
            <appui:Dropdown name="resolution-dropdown" class="export-dropdown" />
        </ui:VisualElement>
        
        <ui:VisualElement name="export-type-container" class="export-row">
            <appui:Label text="Export As:" class="export-label" />
            <appui:Dropdown name="export-type-dropdown" class="export-dropdown" />
        </ui:VisualElement>
        
        <ui:VisualElement name="open-container" class="export-row">
            <appui:Checkbox name="open-checkbox" value="true" />
            <appui:Label text="Open after export" class="export-label" />
        </ui:VisualElement>
        
        <appui:Button name="export-button" text="Export" class="export-button" />
    </ui:VisualElement>
</ui:UXML> 
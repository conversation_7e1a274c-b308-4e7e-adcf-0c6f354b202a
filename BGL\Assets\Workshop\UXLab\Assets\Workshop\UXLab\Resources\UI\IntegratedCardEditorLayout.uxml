<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:appui="Unity.AppUI.UI" xmlns:engine="UnityEngine.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" editor-extension-mode="False"
         xmlns:local="Workshop.UXLab">
    <Style src="IntegratedCardEditorStyles.uss" />
    <!-- Main Draggable Container -->
    <local:DraggablePanel name="integrated-card-editor-container" class="hidden">
        <ui:VisualElement name="editor-content" style="flex-grow: 1;">
            <!-- Add ScrollView here to wrap the main content -->
            <ui:ScrollView name="editor-scrollview" mode="Vertical" vertical-scroller-visibility="Auto">

                <!-- Layout Accordion -->
                <appui:Accordion name="layout-accordion" is-exclusive="false">
                    <!-- Card Size Item -->
                    <appui:AccordionItem name="size-accordion-item" title="Size" value="true">
                        <ui:VisualElement name="size-content" class="accordion-item-content">
                            <appui:Vector2Field name="card-size-field" label="Card Size" />
                            <appui:Dropdown name="unit-dropdown" label="Units" />
                        </ui:VisualElement>
                    </appui:AccordionItem>

                    <!-- Layout Details Item -->
                    <appui:AccordionItem name="layout-details-accordion-item" title="Layout Details">
                         <ui:VisualElement name="layout-details-content" class="accordion-item-content">
                            <appui:Toggle name="safe-zone-toggle" label="Safe Zone" />
                            <appui:FloatField name="safe-zone-distance-field" label="Distance" />
                            <appui:Toggle name="background-toggle" label="Background" />
                            <!-- Add more layout controls based on image... -->
                        </ui:VisualElement>
                    </appui:AccordionItem>
                </appui:Accordion>

                <!-- Elements Accordion -->
                <appui:Accordion name="elements-accordion" is-exclusive="false">
                    <appui:AccordionItem name="elements-list-accordion-item" title="Elements" value="true">
                         <ui:VisualElement name="elements-list-content" class="accordion-item-content">
                            <!-- Element List -->
                            <ui:ListView name="element-list" fixed-item-height="30" reorderable="true" reorder-mode="Simple" show-border="true" style="flex-grow: 1; min-height: 150px;"/>

                            <!-- Element Action Buttons -->
                            <ui:VisualElement name="element-actions" style="flex-direction: row; justify-content: flex-end; margin-top: 5px;">
                                <appui:Button name="add-element-button" text="Add" class="action-button" />
                                <appui:Button name="remove-element-button" text="Remove" class="action-button" disabled="true" />
                            </ui:VisualElement>
                        </ui:VisualElement>
                    </appui:AccordionItem>
                </appui:Accordion>

                 <!-- Selected Element Properties Section -->
                 <ui:VisualElement name="element-properties-section" class="section-container" style="display: none;"> <!-- Hidden by default -->
                     <appui:Text text="Element Properties" class="section-header" />
                     <!-- Placeholder: Fields will be populated dynamically -->
                     <ui:VisualElement name="element-properties-content" />
                 </ui:VisualElement>

            </ui:ScrollView> <!-- Close ScrollView -->

        </ui:VisualElement>
    </local:DraggablePanel>
</ui:UXML> 
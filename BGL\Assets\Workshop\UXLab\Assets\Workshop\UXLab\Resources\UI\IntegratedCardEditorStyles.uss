/* Integrated Card Editor Styles */

/* Overall Container (DraggablePanel) */
#integrated-card-editor-container {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 320px; /* Increased width */
    min-height: 400px; /* Increased min-height */
    max-height: 700px; /* Increased max-height, adjust as needed */
    /* Consider setting a fixed height instead if max-height isn't desired: */
    /* height: 600px; */
    transition: opacity 0.3s ease-in-out, visibility 0.3s;
    opacity: 0;
    visibility: hidden;
    /* Inherits background/shadow from DraggablePanel base styles */
    /* Use AppUI variables for consistency? e.g., border-color: var(--appui-color-border); */
}

#integrated-card-editor-container.visible {
    opacity: 1;
    visibility: visible;
}

/* Main content area *inside* the DraggablePanel */
#editor-content {
    padding: 8px; /* Slightly reduce padding */
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    /* Let the AppUI Panel handle background color */
}

/* --- Removed Foldout Styles --- */
/* Styles for .unity-foldout, .unity-foldout__toggle, .unity-foldout__label, .unity-foldout__content removed */

/* --- Accordion Styling --- */
/* Target the Accordion container itself if needed */
Accordion {
    /* Add spacing between accordions if desired */
    margin-bottom: 8px;
}

/* AccordionItem Header Styling (Overrides default AppUI if needed) */
AccordionItem > .appui-accordionitem__header {
    /* Customize header background, padding, border etc. */
    background-color: rgba(255, 255, 255, 0.05);
    padding: 6px 8px;
    margin: 0; /* Reset potential default margins */
    /* Replace border-bottom/top shorthand */
    border-bottom-width: 1px;
    border-top-width: 1px;
    border-bottom-color: rgba(255, 255, 255, 0.1);
    border-top-color: rgba(255, 255, 255, 0.1);
    /* border-bottom: 1px solid rgba(255, 255, 255, 0.1); Removed */
    /* border-top: 1px solid rgba(255, 255, 255, 0.1); Removed */
}

/* AccordionItem Header Text Styling */
AccordionItem > .appui-accordionitem__header > .appui-accordionitem__headertext {
    -unity-font-style: bold;
    font-size: 13px;
    flex-grow: 1; /* Allow text to take available space */
    margin-left: 4px; /* Space between potential leading icon and text */
    margin-right: 4px; /* Space between text and indicator/trailing icon */
}

/* AccordionItem Indicator Styling (Chevron) */
AccordionItem > .appui-accordionitem__header > .appui-accordionitem__indicator {
    /* Adjust size or color if needed */
     width: 16px;
     height: 16px;
}

/* AccordionItem Content Parent (Container for animation) */
AccordionItem > .appui-accordionitem__content-parent {
    /* Ensure smooth transition */
    transition: height 0.2s ease-in-out;
    overflow: hidden;
}

/* AccordionItem Content Area (Where user content goes) */
/* Add a class in UXML to target specific content areas like <ui:VisualElement name="size-content" class="accordion-item-content"> */
.accordion-item-content {
    padding: 8px 5px 5px 5px; /* Add padding inside the content area */
    background-color: rgba(0, 0, 0, 0.1); /* Slightly different background for content */
    /* Ensure content area uses flex column layout */
    display: flex;
    flex-direction: column;
    /* Prevent shrinking */
    flex-shrink: 0;
}

/* Style the input fields within the accordion content */
.accordion-item-content > .unity-base-field {
    margin-bottom: 4px; /* Add some space between fields */
    /* flex-grow: 0; Removed - Let fields take vertical space if needed */
    flex-shrink: 0; /* Prevent fields from shrinking too much */
    min-height: 24px; /* Explicit minimum height */
    max-height: 24px; /* Explicit maximum height - prevent excessive chunkiness */
}

/* Allow Vector2Field and Dropdown specifically to take needed space */
.accordion-item-content > Vector2Field,
.accordion-item-content > Dropdown {
    /* min-height might be needed if they collapse vertically */
}

/* Ensure labels inside fields don't cause issues */
.accordion-item-content > .unity-base-field > .unity-label {
    min-width: 60px; /* Reduce min-width slightly */
    /* max-width: 100px; Removed */
    flex-shrink: 0; /* Don't allow label itself to shrink */
    margin-right: 5px; /* Ensure space between label and input */
}

/* Ensure input part of fields can grow */
.accordion-item-content > .unity-base-field > .unity-base-field__input {
    flex-grow: 1;
    min-width: 0; /* Allow input to shrink if needed */
}

/* Specific styling for Vector2Field internal fields inside accordion */
.accordion-item-content > Vector2Field > .unity-base-field__input > FloatField {
    flex-grow: 1;
    min-width: 40px; /* Give internal fields a minimum width */
}

.accordion-item-content > Vector2Field > .unity-base-field__input > FloatField:first-of-type {
    margin-right: 2px; /* Space between X and Y fields */
}

/* --- End Accordion Styling --- */

/* General Section Styling (Less needed if using Accordions as main sections) */
.section-container {
    margin-bottom: 8px;
    padding: 0; /* Remove padding/border if foldout content handles it */
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 11px;
}

.section-header {
    font-size: 12px;
    -unity-font-style: bold;
    margin-bottom: 6px;
    color: rgba(255, 255, 255, 0.7);
    /* padding-bottom: 3px; */
    /* border-bottom: 1px solid rgba(255, 255, 255, 0.1); */
}

/* Input Row Container */
.input-container {
    flex-direction: row;
    align-items: center; /* Center items vertically */
    margin-bottom: 4px; /* Reduce spacing */
    min-height: 24px; /* Adjust height */
}

.property-label {
    width: 100px; /* Adjust label width */
    margin-right: 5px;
    white-space: nowrap;
    font-size: 11px; /* Smaller label font */
    color: rgba(255, 255, 255, 0.8);
}

.property-field {
    flex-grow: 1;
    min-width: 0; /* Allow fields to shrink */
}

/* --- Element List Styling --- */
#element-list {
    margin-bottom: 5px;
    /* min-height set in UXML */
    /* Let ListView default border/background work? Or style explicitly */
     background-color: rgba(0, 0, 0, 0.2);
     border-radius: 3px;
}

/* Individual List Item Row */
.element-list-item {
    flex-direction: row;
    align-items: center;
    padding: 4px 6px; /* Adjust padding */
    /* Replace border-bottom shorthand */
    border-bottom-width: 1px;
    border-bottom-color: rgba(255, 255, 255, 0.08);
    /* border-bottom: 1px solid rgba(255, 255, 255, 0.08); Removed */ /* Subtle separator */
}

.element-list-item:last-child {
    border-bottom-width: 0; /* No border on last item */
}

/* Use AppUI variables for selection? */
.element-list-item:selected {
    background-color: var(--appui-color-accent-background-hover);
    color: var(--appui-color-accent-foreground);
}

/* Visibility Toggle in List Item */
.element-list-item__visibility-toggle {
    width: auto; /* Let toggle size itself */
    height: auto;
    margin-right: 6px;
    /* Align toggle better */
    margin-top: 0;
    margin-bottom: 0;
}

/* Label in List Item */
.element-list-item__label {
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 11px;
}

/* Element Action Buttons */
#element-actions {
    padding-top: 5px;
    /* border-top: 1px solid rgba(255, 255, 255, 0.1); */ /* Removed border, handled by accordion item */
}

#element-actions .action-button {
    margin-left: 5px;
    /* Use AppUI Size enum? Or fixed size */
    height: 24px;
    min-width: 60px;
    font-size: 11px;
    /* Explicitly set text color using AppUI variable */
    color: var(--appui-color-foreground);
    /* Add some padding inside the button */
    padding-left: 8px;
    padding-right: 8px;
    /* Ensure content isn't clipped */
    overflow: visible;
}

/* Element Property Fields Container */
#element-properties-content {
    padding-top: 5px;
}

/* Adjust labels and fields specifically within the properties section */
#element-properties-content .property-label {
    min-width: 50px; /* Reduced min width */
    flex-basis: 60px; /* Reduced base size */
    flex-shrink: 0;
    margin-right: 5px;
    /* Inherits color/font-size from default label styles or set explicitly */
}

#element-properties-content .property-field {
    flex-grow: 1; /* Allow field input to take remaining space */
    min-width: 0; /* Allow field to shrink */
}

/* Adjust internal fields for Vector2Field specifically within properties */
#element-properties-content Vector2Field FloatField {
}
/* --- Specific AppUI Field Adjustments --- */

/* Dropdown */
Dropdown {
    flex-grow: 1;
    min-width: 0;
}

/* Vector2Field */
Vector2Field .unity-base-field__input {
    flex-direction: row;
}

/* Make internal FloatFields in Vector2Field share space */
Vector2Field FloatField {
    flex-grow: 1;
    min-width: 30px; /* Prevent becoming too small */
}

Vector2Field {
  flex-shrink: 1;
}

Vector2Field FloatField:first-of-type {
     margin-right: 2px;
}

/* FloatField (standalone) */
FloatField {
    min-width: 40px;
}

/* Toggle */
Toggle .unity-toggle__input {
    /* Make toggle circle smaller? */
     height: 16px;
     width: 16px;
     margin-top: 2px; /* Align better */
}

/* Align toggle label */
Toggle .unity-label {
    margin-left: 5px;
    font-size: 11px;
    color: rgba(255, 255, 255, 0.8);
}

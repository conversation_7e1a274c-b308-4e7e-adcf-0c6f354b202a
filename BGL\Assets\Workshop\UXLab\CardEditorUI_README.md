# Card Editor UI System

This system provides a simple UI for editing card properties using Unity's UI Toolkit. The UI appears when a card is selected and disappears when it's deselected.

## Features

- Edit card dimensions (width, height)
- Adjust corner radius
- Convert between different units (meters, centimeters, millimeters, inches)
- Automatic UI visibility based on card selection
- Consistent UI appearance using shared panel settings

## Setup Instructions

1. Make sure you have a `CardEditorSystem` instance in your scene or as a ScriptableObject reference.
2. Assign a PanelSettings asset to the TableManager's UIPanelSettings field.
3. The UI is automatically created and set up when the CardEditorSystem is initialized.
4. You can toggle the UI creation with the `m_EnableCardEditorUI` checkbox in the CardEditorSystem inspector.

## Component Overview

- **CardEditorSystem**: Manages card selection and creates the UI
- **CardEditorUIControl**: Handles the UI interaction and card property editing
- **CardMeshGenerator**: The component being edited, holds card dimension properties
- **TableManager**: Provides shared UI panel settings for consistent appearance

## How It Works

1. The UI is hidden by default.
2. When the CardEditorSystem detects a card selection (GameObject with a `CardMeshGenerator` component):
   - It calls `SetActiveCard()` on the CardEditorUIControl
   - The UI becomes visible
   - Input fields are populated with the card's current values
   - The unit dropdown is set to the card's current unit

3. When the user updates any value in the UI:
   - The card is updated in real-time
   - All dimensions maintain proper unit conversion

4. When a card is deselected, the CardEditorSystem calls `ClearActiveCard()` and the UI is hidden.

## Customization

- The UI layout is defined in `Resources/UI/CardEditorUI.uxml`
- The UI styling is defined in `Resources/UI/CardEditorUI.uss`

You can modify these files to change the appearance of the UI without changing the functionality.

## Design Notes

- The CardEditorUIControl is designed to be independent of the CardEditorSystem
- It has a simple API with `SetActiveCard()` and `ClearActiveCard()` methods
- This separation of concerns allows for cleaner code and easier testing
- Direct method calls are used instead of events for tightly coupled components

## Requirements

- Unity's UI Toolkit
- Unity version with UI Builder support 
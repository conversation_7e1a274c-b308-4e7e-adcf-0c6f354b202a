# Card Interaction System

The CardInteractionSystem provides basic card interaction functionality for selecting, dragging, and dropping cards in the 3D space. It's designed to work with cards on the XY plane, using the Z axis for depth.

## Features

- Card selection with visual highlighting
- Card dragging in 3D space
- Smooth drop animation when releasing cards
- Events for other systems to respond to card interactions

## Setup

1. Make sure you have a `TableManager` instance in your scene.
2. Add the `CardInteractionSystem` asset to the TableManager's Systems list.
3. The system will automatically initialize when the TableManager initializes.

## Usage

### In the Editor

1. Select the CardInteractionSystem asset to adjust settings:
   - Hover highlight color
   - Selection highlight color
   - Drag height (depth offset when dragging, using negative Z axis)
   - Drag start delay
   - Drop animation duration and curve

### In Runtime

- Click on a card to select it
- Click and hold on a selected card to drag it
- Release the mouse button to drop the card at its current position
- Click on an empty area to deselect the current card

### Scripting

You can subscribe to the following events to respond to card interactions:

```csharp
// Get reference to the system
CardInteractionSystem interactionSystem = tableManager.GetSystem<CardInteractionSystem>();

// Subscribe to events
interactionSystem.OnCardSelected += HandleCardSelected;
interactionSystem.OnCardDeselected += HandleCardDeselected;
interactionSystem.OnCardDragStarted += HandleCardDragStarted;
interactionSystem.OnCardDragEnded += HandleCardDragEnded;
interactionSystem.OnCardMoved += HandleCardMoved;
```

## Integration with Other Systems

The CardInteractionSystem is designed to work alongside other systems like the CardEditorSystem. When both systems are active:

- CardInteractionSystem handles basic selection and movement
- CardEditorSystem handles editing card properties and elements

You can control which system responds to card interactions by enabling/disabling them as needed.

## Extending

To extend the CardInteractionSystem with additional functionality:

1. Create a new system that inherits from TableSystem
2. Get a reference to the CardInteractionSystem using `tableManager.GetSystem<CardInteractionSystem>()`
3. Subscribe to the CardInteractionSystem's events
4. Implement your custom logic in the event handlers

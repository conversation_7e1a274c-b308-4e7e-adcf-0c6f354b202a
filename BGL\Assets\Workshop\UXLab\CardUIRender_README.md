# Card UI Render System

This system provides an alternative approach to rendering card elements using Unity's UI Toolkit. Instead of creating 3D objects for each card element, this system renders UI elements to a texture that is applied to the card mesh.

## Benefits

- **Simplified Layout**: Use UI Toolkit's layout system instead of manually positioning 3D objects
- **Rich Text Support**: Better text rendering with full UI Toolkit text features
- **Improved Performance**: Fewer GameObjects and components in the scene
- **Easier Styling**: Use USS styles for consistent appearance
- **Better Responsiveness**: UI elements automatically adjust to card size changes

## Components

- **CardRenderSystem**: TableSystem that manages render targets and UIDocuments
- **CardRenderTarget**: Handles RenderTexture creation and UIDocument setup for a specific card
- **CardUIElement**: Base class for all UI elements that can be added to a card
- **CardUIImageElement**: Displays an image on a card
- **CardUITextElement**: Displays text on a card
- **CardUIRenderTest**: Test component to demonstrate the system

## Setup Instructions

1. Create a `CardRenderSystem` ScriptableObject asset (Create > Workshop > UXLab > Systems > CardRenderSystem)
2. Add the CardRenderSystem to your TableManager's Systems list
3. Assign a PanelSettings asset to the `m_PanelSettingsTemplate` field (optional)
4. For each card you want to render UI elements on:
   - Get a render target from the CardRenderSystem using `GetRenderTarget(cardMesh)`
   - Create UI elements using the CardUIImageElement and CardUITextElement classes
   - Add the elements to the render target

## Quick Start

The easiest way to test the system is to use the `CardUIRenderTest` component:

1. Add the `CardUIRenderTest` component to a GameObject with a `CardMeshGenerator`
2. Use the "Create Render System" button in the inspector to create a CardRenderSystem ScriptableObject
3. Use the "Auto-Assign Card Mesh" button to find and assign a CardMeshGenerator
4. Use the "Add System to TableManager" button to add the system to your TableManager
5. Assign a test image and font in the inspector (optional)
6. Play the scene to see the UI elements rendered on the card

## Example Code

```csharp
// Get the CardRenderSystem from the TableManager
CardRenderSystem cardRenderSystem = TableManager.Instance.GetSystem<CardRenderSystem>();

// Get a render target for a card
CardRenderTarget renderTarget = cardRenderSystem.GetRenderTarget(cardMesh);

// Create an image element
CardUIImageElement imageElement = new CardUIImageElement("MyImage", renderTarget, myTexture);
imageElement.WidthMM = 50;
imageElement.HeightMM = 50;
imageElement.PositionMM = new Vector2(0, 0); // Center of the card
imageElement.Update();

// Create a text element
CardUITextElement textElement = new CardUITextElement("MyText", renderTarget, "Hello World");
textElement.WidthMM = 80;
textElement.HeightMM = 20;
textElement.PositionMM = new Vector2(0, 30); // 30mm below center
textElement.FontSize = 18;
textElement.Bold = true;
textElement.Update();
```

## Integration with Existing Systems

This system is designed to work alongside the existing card element system. You can use both approaches in the same project, or gradually migrate from one to the other.

To integrate with the CardEditorSystem:

1. Modify the CardEditorSystem to create UI elements instead of 3D objects
2. Update the IntegratedCardEditorControl to work with UI elements
3. Add support for editing UI element properties in the editor UI

## Customization

You can extend the system by creating your own UI element classes that inherit from CardUIElement. For example:

- CardUIButtonElement: A clickable button element
- CardUISliderElement: A slider control
- CardUIToggleElement: A toggle/checkbox control

## Resolution and Scaling

The system uses a pixels-per-millimeter approach to control the resolution of the rendered card:

- The `m_DefaultPixelsPerMm` setting in CardRenderSystem determines the default resolution
- Higher values create larger textures with more detail but require more memory and processing power
- UI elements are positioned and sized by converting millimeters to pixels using the pixels-per-mm value
- This ensures consistent proportions regardless of the texture resolution
- You can adjust the resolution at runtime using the `UpdateRenderTargetResolution` method

### Resolution Guidelines:
- **Low Resolution**: 5-8 pixels/mm (suitable for distant cards or low-end devices)
- **Medium Resolution**: 10-15 pixels/mm (good balance of quality and performance)
- **High Resolution**: 20-30 pixels/mm (high quality for close-up viewing)

The CardUIRenderTest component provides controls to experiment with different resolutions and see the results in real-time.

## Limitations

- UI elements are rendered to a texture, so they cannot be interacted with directly
- Changes to the UI elements require re-rendering the texture
- The system does not support 3D elements or effects that require depth

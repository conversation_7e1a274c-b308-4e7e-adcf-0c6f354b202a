# Changelog

## [Unreleased]

### Added
- Created DraggablePanel component with shadow effect and content container
- Added DraggablePanelLayout.uxml template for panel structure
- Added DraggablePanelStylesheet.uss for styling the draggable panel
- Implemented box-shadow using CSS variables for customization
- Added new `ToolbarSystem` and basic UXML/USS for editor toolbars.
- Refactored Dimension, Layers, and Element editor controls to use UI Toolkit `VisualElement` instead of `MonoBehaviour`.
- Added `ToolbarSystem` for managing editor toolbars.
- Fixed element Z-ordering in `CardLayout`.
- Standardized element size handling using Width/Height properties on `CardElement` subclasses.
- Added unit display and decimal precision formatting to `<PERSON>loat<PERSON>ield`s in Dimension and Element editors.
- Added `ZoomControlSystem` with UI (Slider + Label) to control camera zoom.
- Added `GetNormalizedZoom` method to `CameraController`.
- Implement Box Mode: Draw bounding boxes around CardElements when Box Mode is active.
- Added a CardDeck component for managing visually stacked card collections.
- Dynamic deck creation when dropping a card on another compatible card.
- Dynamically created decks now dissolve when only one card remains, releasing the card and resetting its rotation. The deck GameObject is then destroyed.

### Changed
- Dynamic deck creation logic in `CardInteractionSystem` updated:
    - `DoCardsOverlap` now performs a 2D (XY) bounding box check.
    - `GetObjectUnderCard` first raycasts for a card under the mouse; if none, it checks for 2D overlap with other cards on the table.
- Updated CardEditorUI to use the new DraggablePanel component
- Moved draggable panel styles from CardEditorUI.uss to dedicated stylesheet
- Moved background styling from panel to container element
- Made container the default drag handle instead of the panel
- Increased card editor width to accommodate padding
- Updated Box Mode to use runtime `LineRenderer` instead of Editor-only `Gizmos`.
- Changed Mode Toolbar to allow multiple selections instead of single selection.
- Changed CardDeck stacking to use cumulative offsets/rotation per card.
- Refactored `CardInteractionSystem.StopCurrentAnimation` to `StopAnimationOnCard(GameObject card)` for clarity.
- Simplified dynamic deck creation: removed the optional default card prefab assignment, as dynamically created decks will group existing cards and not spawn new ones by default.

### Fixed
- Fixed issue with Add method override by using contentContainer property override instead
- Fixed resource loading by using distinct names for UXML and USS files
- Fixed element Z-ordering in `CardLayout` to ensure elements appear above the card and stack correctly based on layer order.

## Card Editor UI System

- Added UI for editing card properties (width, height, corner radius)
- Implemented unit conversion between meters, centimeters, millimeters, and inches
- UI automatically shows when a card is selected and hides when deselected
- Integrated UI setup directly into CardEditorSystem
- Updated variable naming to follow project coding conventions

*   Started refactoring card editing UI into a single integrated panel.
- Configured AppUI Panel in CardEditorSystem for proper layering and input passthrough.
- Refined visual stacking in CardDeck to use thickness and biased randomness.

### Deprecated

### Removed

### Security 
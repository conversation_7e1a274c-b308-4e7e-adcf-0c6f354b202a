using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;

namespace Workshop.UXLab
{
    [CreateAssetMenu(fileName = "AnimationSystem", menuName = "Workshop/UXLab/Systems/AnimationSystem")]
    public class AnimationSystem : TableSystem
    {
        // Animation parameters
        [Header("Card Elevation")]
        [SerializeField] private float m_ElevationHeight = 0.05f;
        [SerializeField] private float m_ElevationDuration = 0.3f;
        [SerializeField] private AnimationCurve m_ElevationCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

        [Header("Elevation Scaling (Optional)")]
        [SerializeField] private bool m_EnableElevationScaling = false;
        [SerializeField] private float m_ElevationScaleFactor = 1.1f; // e.g., 1.1 for 10% larger
        [SerializeField] private float m_ElevationScaleDuration = 0.3f;

        [Header("Card Rotation")]
        [SerializeField] private float m_RotationDuration = 0.3f;
        [SerializeField] private AnimationCurve m_RotationCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

        [Header("Card Flip")]
        [SerializeField] private float m_FlipRotationDuration = 0.5f;
        [SerializeField] private AnimationCurve m_FlipAnimationCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        [SerializeField] private float m_FlipElevationHeight = 0.1f;
        [SerializeField] private float m_FlipElevationDuration = 0.2f;
        [SerializeField] private float m_FlipDropDuration = 0.2f;
        [SerializeField] private float m_FlipRotationDelay = 0.1f;
        [SerializeField] private float m_FlipDropDelay = 0.3f;

        // Animation tracking
        private HashSet<GameObject> m_AnimatingObjects = new HashSet<GameObject>();
        private HashSet<GameObject> m_RequestedCancellations = new HashSet<GameObject>();

        // Animation queuing
        private Dictionary<GameObject, Queue<AnimationRequest>> m_AnimationQueues = new Dictionary<GameObject, Queue<AnimationRequest>>();
        private Dictionary<GameObject, bool> m_ProcessingQueues = new Dictionary<GameObject, bool>();

        // Events
        public delegate void CardAnimationHandler(GameObject cardObject, string animationType);
        public event CardAnimationHandler OnAnimationStarted;
        public event CardAnimationHandler OnAnimationCompleted;
        public event CardAnimationHandler OnAnimationCancelled;

        // Animation request class
        private class AnimationRequest
        {
            public string AnimationType { get; set; }
            public object[] Parameters { get; set; }
            public TaskCompletionSource<bool> CompletionSource { get; set; }

            public AnimationRequest(string type, TaskCompletionSource<bool> completionSource, params object[] parameters)
            {
                AnimationType = type;
                Parameters = parameters;
                CompletionSource = completionSource;
            }
        }

        #region Public Animation Shortcuts

        /// <summary>
        /// Taps a card by rotating it 90 degrees counterclockwise around the Z axis
        /// </summary>
        public Task<bool> TapCard(GameObject card, bool queue = false)
        {
            if (card == null) return Task.FromResult(false);
            object[] animParams = { new Vector3(0, 0, -90), m_RotationDuration, m_RotationCurve };
            return ExecuteAnimation("Rotation", card, animParams, queue);
        }

        /// <summary>
        /// Untaps a card by rotating it 90 degrees clockwise around the Z axis
        /// </summary>
        public Task<bool> UntapCard(GameObject card, bool queue = false)
        {
            if (card == null) return Task.FromResult(false);
            object[] animParams = { new Vector3(0, 0, 90), m_RotationDuration, m_RotationCurve };
            return ExecuteAnimation("Rotation", card, animParams, queue);
        }

        /// <summary>
        /// Flips a card by rotating it 180 degrees around the Y axis with elevation if needed
        /// </summary>
        public Task<bool> FlipCard(GameObject card, bool isFlipped, bool queue = false)
        {
            if (card == null) return Task.FromResult(false);
            object[] animParams = { isFlipped };
            return ExecuteAnimation("FlipSequence", card, animParams, queue);
        }

        /// <summary>
        /// Elevates a card above the table
        /// </summary>
        public Task<bool> ElevateCard(GameObject card, float height = -1, bool queue = false)
        {
            if (card == null) return Task.FromResult(false);
            if (height < 0) height = m_ElevationHeight;
            object[] animParams = { true, height, m_ElevationDuration, m_ElevationCurve };
            return ExecuteAnimation("Elevation", card, animParams, queue);
        }

        /// <summary>
        /// Drops a card back to the table
        /// </summary>
        public Task<bool> DropCard(GameObject card, bool queue = false)
        {
            if (card == null) return Task.FromResult(false);
            object[] animParams = { false, m_ElevationHeight, m_ElevationDuration, m_ElevationCurve };
            return ExecuteAnimation("Elevation", card, animParams, queue);
        }

        /// <summary>
        /// Moves a card to a specific position
        /// </summary>
        public Task<bool> MoveCard(GameObject card, Vector3 targetPosition, float duration, bool queue = false)
        {
            if (card == null) return Task.FromResult(false);
            object[] animParams = { targetPosition, duration, m_ElevationCurve };
            return ExecuteAnimation("Position", card, animParams, queue);
        }

        /// <summary>
        /// Animates a card's position and rotation simultaneously.
        /// </summary>
        public Task<bool> AnimateCardPositionAndRotation(GameObject card, Vector3 targetPosition, Quaternion targetRotation, float duration, AnimationCurve curve, bool queue = false, Vector3? targetAbsoluteScale = null)
        {
            if (card == null) return Task.FromResult(false);
            object[] animParams = { targetPosition, targetRotation, duration, curve, targetAbsoluteScale };
            return ExecuteAnimation("PositionAndRotation", card, animParams, queue);
        }

        #endregion

        #region Core Animation Methods (now just wrappers around ExecuteAnimation)

        public Task<bool> AnimateCardRotation(GameObject card, Vector3 rotationAngles, float duration, AnimationCurve curve, bool queue = false)
        {
            if (card == null) return Task.FromResult(false);
            object[] animParams = { rotationAngles, duration, curve };
            return ExecuteAnimation("Rotation", card, animParams, queue);
        }

        public Task<bool> AnimateCardElevation(GameObject card, bool elevate, float height, float duration, AnimationCurve curve, bool queue = false)
        {
            if (card == null) return Task.FromResult(false);
            object[] animParams = { elevate, height, duration, curve };
            return ExecuteAnimation("Elevation", card, animParams, queue);
        }

        public Task<bool> AnimateCardPosition(GameObject card, Vector3 targetPosition, float duration, AnimationCurve curve, bool queue = false)
        {
            if (card == null) return Task.FromResult(false);
            object[] animParams = { targetPosition, duration, curve };
            return ExecuteAnimation("Position", card, animParams, queue);
        }

        public Task<bool> AnimateCardFlipSequence(GameObject card, bool isFlipped, bool queue = false)
        {
            if (card == null) return Task.FromResult(false);
            object[] animParams = { isFlipped };
            return ExecuteAnimation("FlipSequence", card, animParams, queue);
        }

        #endregion

        /// <summary>
        /// Centralized method to execute or queue an animation.
        /// </summary>
        private Task<bool> ExecuteAnimation(string animationType, GameObject card, object[] animParams, bool queue)
        {
            var completionSource = new TaskCompletionSource<bool>();
            var request = new AnimationRequest(animationType, completionSource, animParams);

            if (queue)
            {
                QueueAnimation(card, request);
            }
            else
            {
                if (IsObjectAnimating(card))
                {
                    Debug.LogWarning($"AnimationSystem: Card {card.name} ({animationType}) is already animating. Cancelling current and queueing new animation.", this);
                    CancelAnimations(card); // Request cancellation of current animation
                    QueueAnimation(card, request); // Queue the new request
                }
                else
                {
                    // Define a local async function to run the animation and set the completion source.
                    // This ensures it runs on the main thread if ExecuteAnimation is called from the main thread.
                    async Task StartAndCompleteAnimation() 
                    {
                        bool success = await ProcessActualAnimationAsync(card, animationType, animParams);
                        completionSource.TrySetResult(success);
                    }
                    _ = StartAndCompleteAnimation(); // Fire and forget the local async method
                }
            }
            return completionSource.Task;
        }

        #region Animation Queue Management

        private void QueueAnimation(GameObject obj, AnimationRequest request)
        {
            // Initialize queue if it doesn't exist
            if (!m_AnimationQueues.ContainsKey(obj))
            {
                m_AnimationQueues[obj] = new Queue<AnimationRequest>();
            }

            // Add the request to the queue
            m_AnimationQueues[obj].Enqueue(request);

            // Start processing the queue if it's not already being processed
            if (!m_ProcessingQueues.ContainsKey(obj) || !m_ProcessingQueues[obj])
            {
                _ = ProcessAnimationQueue(obj);
            }
        }

        private async Task ProcessAnimationQueue(GameObject obj)
        {
            if (obj == null) return;

            // Mark this queue as being processed
            m_ProcessingQueues[obj] = true;

            // Process all requests in the queue
            while (m_AnimationQueues.ContainsKey(obj) && m_AnimationQueues[obj].Count > 0)
            {
                // Get the next request
                var request = m_AnimationQueues[obj].Peek();

                // Process the request
                bool success = await ProcessActualAnimationAsync(obj, request.AnimationType, request.Parameters);

                // Remove the request from the queue
                if (m_AnimationQueues.ContainsKey(obj) && m_AnimationQueues[obj].Count > 0)
                {
                    m_AnimationQueues[obj].Dequeue();
                }

                // If the animation failed, we might want to skip the rest of the queue
                if (!success && obj != null)
                {
                    // Clear the queue and ensure their TCSs are completed
                    if (m_AnimationQueues.ContainsKey(obj))
                    {
                        // Dequeueing here and setting TCS is already handled by ClearAnimationQueue
                        ClearAnimationQueue(obj);
                    }
                    break;
                }
            }

            // Mark this queue as no longer being processed
            if (obj != null && m_ProcessingQueues.ContainsKey(obj))
            {
                m_ProcessingQueues[obj] = false;
            }
        }

        /// <summary>
        /// Handles the actual execution of a single animation operation, including state management.
        /// </summary>
        /// <param name="obj">The GameObject to animate.</param>
        /// <param name="animationType">The type of animation.</param>
        /// <param name="parameters">Parameters for the animation.</param>
        /// <returns>True if the animation logic completed successfully, false otherwise (including cancellation).</returns>
        private async Task<bool> ProcessActualAnimationAsync(GameObject obj, string animationType, object[] parameters)
        {
            if (obj == null) return false; // Cannot animate null object

            // Pre-cancellation check: if a cancellation was requested before this animation even started
            if (m_RequestedCancellations.Contains(obj))
            {
                m_RequestedCancellations.Remove(obj); // Consume the cancellation token
                OnAnimationCancelled?.Invoke(obj, animationType);
                return false; // Animation will not run
            }

            bool animationLogicCompletedSuccessfully = false;
            try
            {
                m_AnimatingObjects.Add(obj);
                OnAnimationStarted?.Invoke(obj, animationType);

                switch (animationType)
                {
                    case "Elevation":
                        animationLogicCompletedSuccessfully = await AnimateElevationInternal(obj,
                                                               (bool)parameters[0],
                                                               (float)parameters[1],
                                                               (float)parameters[2],
                                                               (AnimationCurve)parameters[3]);
                        break;
                    case "Rotation":
                        animationLogicCompletedSuccessfully = await AnimateRotationInternal(obj,
                                                              (Vector3)parameters[0],
                                                              (float)parameters[1],
                                                              (AnimationCurve)parameters[2]);
                        break;
                    case "Position":
                        animationLogicCompletedSuccessfully = await AnimatePositionInternal(obj,
                                                              (Vector3)parameters[0],
                                                              (float)parameters[1],
                                                              (AnimationCurve)parameters[2]);
                        break;
                    case "PositionAndRotation":
                        animationLogicCompletedSuccessfully = await AnimatePositionAndRotationInternal(obj,
                                                              (Vector3)parameters[0],    // targetPosition
                                                              (Quaternion)parameters[1], // targetRotation
                                                              (float)parameters[2],    // duration
                                                              (AnimationCurve)parameters[3], // curve
                                                              parameters.Length > 4 ? (Vector3?)parameters[4] : null); // targetAbsoluteScale
                        break;
                    case "FlipSequence":
                        animationLogicCompletedSuccessfully = await AnimateFlipSequenceInternal(obj, (bool)parameters[0]);
                        break;
                    default:
                        Debug.LogError($"Unknown animation type: {animationType} for object {obj.name}", this);
                        animationLogicCompletedSuccessfully = false; // Explicitly set to false for unknown type
                        break;
                }

                if (animationLogicCompletedSuccessfully)
                {
                    OnAnimationCompleted?.Invoke(obj, animationType);
                }
                // If it failed logically within Animate...Internal, no specific event other than returning false.
            }
            catch (System.OperationCanceledException) // Specifically catch cancellations from Animate...Internal
            {
                OnAnimationCancelled?.Invoke(obj, animationType);
                animationLogicCompletedSuccessfully = false; // Cancelled means not completed successfully
            }
            catch (Exception ex) // Catch other unexpected exceptions
            {
                Debug.LogError($"Error during animation for {obj.name} ({animationType}): {ex.Message}\n{ex.StackTrace}", this);
                OnAnimationCancelled?.Invoke(obj, animationType); // Treat other errors as cancellations for now
                animationLogicCompletedSuccessfully = false; // Failed
            }
            finally
            {
                if (obj != null)
                {
                    m_AnimatingObjects.Remove(obj);
                }
            }
            return animationLogicCompletedSuccessfully;
        }

        #endregion

        #region Internal Animation Implementations

        private async Task<bool> AnimateElevationInternal(GameObject card, bool elevate, float height, float duration, AnimationCurve curve)
        {
            if (card == null) return false;

            Vector3 startPosition = card.transform.position;
            Vector3 targetPosition = startPosition;
            Vector3 startScale = card.transform.localScale;
            Vector3 targetScale = startScale;

            if (elevate)
            {
                targetPosition.z = startPosition.z - height;
                if (m_EnableElevationScaling)
                {
                    targetScale *= m_ElevationScaleFactor;
                }
            }
            else
            {
                float originalZ = startPosition.z + height;
                targetPosition.z = originalZ;
                // When dropping, target scale is its original scale (inverse of elevation scale if applied)
                // This assumes that an object is always elevated before it's dropped with scaling.
                // If an object could be scaled independently and then dropped, this logic would need adjustment.
                if (m_EnableElevationScaling)
                {
                    // To revert to original scale, we can't just divide by m_ElevationScaleFactor
                    // if the card was already scaled. We need to know its "base" scale.
                    // For simplicity now, let's assume we always scale from a base of 'startScale' when elevating,
                    // and revert to 'startScale' when dropping, if scaling was enabled for the elevation.
                    // This might need a more robust solution if cards can have persistent scale changes.
                    // However, if 'startScale' is captured when AnimateElevationInternal is called for 'drop',
                    // and that 'startScale' is already the elevated scale, then dividing works.
                    // Let's assume 'startScale' is the current scale when the function is called.
                    // If elevated, 'startScale' IS the already scaled version.
                    // So, to return to normal, we'd divide by m_ElevationScaleFactor.
                    // This is getting complicated. For now, let's define targetScale as the base scale before elevation.
                    // This means we need to store the original scale before any elevation scaling occurs.

                    // Simpler approach for now: assume drop will always revert to original scale before elevation.
                    // This means 'targetScale' remains 'startScale' if not elevating, or if scaling is off.
                    // If we are dropping (elevate = false) AND scaling was enabled, the 'startScale'
                    // *is* the scaled-up version. We want to return to the scale *before* it was scaled up.
                    // So, targetScale should be startScale / m_ElevationScaleFactor.
                    targetScale = startScale / m_ElevationScaleFactor; 
                }
            }

            float startTime = Time.time;
            // Use the longer of position or scale duration if scaling is enabled
            float actualDuration = m_EnableElevationScaling ? Mathf.Max(duration, m_ElevationScaleDuration) : duration;
            float scaleAnimDuration = m_EnableElevationScaling ? m_ElevationScaleDuration : duration; // Duration for scale part
            float posAnimDuration = duration; // Duration for position part

            float endTime = startTime + actualDuration;

            while (Time.time < endTime)
            {
                if (m_RequestedCancellations.Contains(card))
                {
                    m_RequestedCancellations.Remove(card); // Consume the token
                    // Restore original scale if cancelled during scaling
                    if (m_EnableElevationScaling) card.transform.localScale = startScale; 
                    throw new System.OperationCanceledException("Elevation animation cancelled.");
                }

                float normalizedTimeOverall = (Time.time - startTime) / actualDuration;
                
                // Position interpolation
                float normalizedTimePos = (Time.time - startTime) / posAnimDuration;
                if (Time.time < startTime + posAnimDuration) {
                    float curveValuePos = curve.Evaluate(Mathf.Clamp01(normalizedTimePos));
                    card.transform.position = Vector3.Lerp(startPosition, targetPosition, curveValuePos);
                } else {
                    card.transform.position = targetPosition; // Ensure position is exact if its anim is done
                }

                // Scale interpolation
                if (m_EnableElevationScaling)
                {
                    float normalizedTimeScale = (Time.time - startTime) / scaleAnimDuration;
                    if (Time.time < startTime + scaleAnimDuration) {
                        float curveValueScale = curve.Evaluate(Mathf.Clamp01(normalizedTimeScale)); // Can use same curve or a dedicated one
                        card.transform.localScale = Vector3.Lerp(startScale, targetScale, curveValueScale);
                    } else {
                        card.transform.localScale = targetScale; // Ensure scale is exact if its anim is done
                    }
                }

                await System.Threading.Tasks.Task.Yield();
            }

            card.transform.position = targetPosition;
            if (m_EnableElevationScaling) // Ensure final scale is set
            {
                card.transform.localScale = targetScale;
            }
            return true;
        }

        private async Task<bool> AnimateRotationInternal(GameObject card, Vector3 rotationAngles, float duration, AnimationCurve curve)
        {
            if (card == null) return false;

            Quaternion startRotation = card.transform.rotation;
            Quaternion targetRotation = startRotation * Quaternion.Euler(rotationAngles);

            float startTime = Time.time;
            float endTime = startTime + duration;

            while (Time.time < endTime)
            {
                if (m_RequestedCancellations.Contains(card))
                {
                    m_RequestedCancellations.Remove(card); // Consume the token
                    throw new System.OperationCanceledException("Rotation animation cancelled.");
                }

                float normalizedTime = (Time.time - startTime) / duration;
                float curveValue = curve.Evaluate(normalizedTime);
                card.transform.rotation = Quaternion.Slerp(startRotation, targetRotation, curveValue);
                await System.Threading.Tasks.Task.Yield();
            }

            card.transform.rotation = targetRotation;
            return true;
        }

        private async Task<bool> AnimatePositionInternal(GameObject card, Vector3 targetPosition, float duration, AnimationCurve curve)
        {
            if (card == null) return false;

            Vector3 startPosition = card.transform.position;
            float startTime = Time.time;
            float endTime = startTime + duration;

            while (Time.time < endTime)
            {
                if (m_RequestedCancellations.Contains(card))
                {
                    m_RequestedCancellations.Remove(card); // Consume the token
                    throw new System.OperationCanceledException("Position animation cancelled.");
                }

                float normalizedTime = (Time.time - startTime) / duration;
                float curveValue = curve.Evaluate(normalizedTime);
                card.transform.position = Vector3.Lerp(startPosition, targetPosition, curveValue);
                await System.Threading.Tasks.Task.Yield();
            }

            card.transform.position = targetPosition;
            return true;
        }

        private async Task<bool> AnimatePositionAndRotationInternal(GameObject card, Vector3 targetPosition, Quaternion targetAbsoluteRotation, float duration, AnimationCurve curve, Vector3? targetAbsoluteScale = null)
        {
            if (card == null) return false;

            Vector3 startPosition = card.transform.position;
            Quaternion startRotation = card.transform.rotation;
            Vector3 startScale = card.transform.localScale;
            Vector3 animTargetScale = startScale; // Renamed from targetScale to avoid confusion with targetAbsoluteScale

            bool isElevating = false;
            bool isDropping = false;

            if (targetAbsoluteScale.HasValue)
            {
                animTargetScale = targetAbsoluteScale.Value;
                // If absolute scale is given, we don't use Z-based elevation/drop scaling detection for scale.
                // We still might want to know if it's an elevation/drop for duration adjustment.
                if (m_EnableElevationScaling) {
                    float deltaZ = startPosition.z - targetPosition.z;
                    float elevationThreshold = m_ElevationHeight * 0.9f;
                    if (deltaZ > elevationThreshold) isElevating = true;
                    else if (deltaZ < -elevationThreshold) isDropping = true;
                }
            }
            else if (m_EnableElevationScaling) // Only use Z-based scaling if no absolute target scale is provided
            {
                float deltaZ = startPosition.z - targetPosition.z;
                float elevationThreshold = m_ElevationHeight * 0.9f;

                if (deltaZ > elevationThreshold) // Elevating
                {
                    isElevating = true;
                    animTargetScale = startScale * m_ElevationScaleFactor;
                }
                else if (deltaZ < -elevationThreshold) // Dropping
                {
                    isDropping = true;
                    animTargetScale = startScale / m_ElevationScaleFactor;
                }
            }

            float startTime = Time.time;
            float actualDuration = duration;
            float scaleAnimDuration = duration; 
            float posRotAnimDuration = duration;

            // Adjust duration only if scaling is actively happening due to elevation/drop detection OR an absolute scale implies change
            bool applyScalingEffects = (m_EnableElevationScaling && (isElevating || isDropping)) || (targetAbsoluteScale.HasValue && targetAbsoluteScale.Value != startScale);

            if (applyScalingEffects)
            {
                scaleAnimDuration = m_EnableElevationScaling ? m_ElevationScaleDuration : duration; // Use specific scale duration if Z-based scaling
                if (targetAbsoluteScale.HasValue && m_EnableElevationScaling) { // If absolute scale provided, allow override of duration with m_ElevationScaleDuration
                     scaleAnimDuration = m_ElevationScaleDuration;
                } else if (targetAbsoluteScale.HasValue) { // If abs scale but no elevation scaling, scale over main duration
                     scaleAnimDuration = duration;
                }
                actualDuration = Mathf.Max(posRotAnimDuration, scaleAnimDuration);
            }
            
            float endTime = startTime + actualDuration;

            while (Time.time < endTime)
            {
                if (m_RequestedCancellations.Contains(card))
                {
                    m_RequestedCancellations.Remove(card);
                    // Reset to startScale on cancellation, regardless of how targetScale was determined
                    card.transform.localScale = startScale; 
                    throw new System.OperationCanceledException("PositionAndRotation animation cancelled.");
                }

                float currentTime = Time.time;

                // Position and Rotation Interpolation
                if (currentTime < startTime + posRotAnimDuration)
                {
                    float normalizedTimePosRot = (currentTime - startTime) / posRotAnimDuration;
                    float curveValuePosRot = curve.Evaluate(Mathf.Clamp01(normalizedTimePosRot));
                    card.transform.position = Vector3.Lerp(startPosition, targetPosition, curveValuePosRot);
                    card.transform.rotation = Quaternion.Slerp(startRotation, targetAbsoluteRotation, curveValuePosRot);
                }
                else
                {
                    card.transform.position = targetPosition;
                    card.transform.rotation = targetAbsoluteRotation;
                }

                // Scale Interpolation
                if (applyScalingEffects)
                {
                    if (currentTime < startTime + scaleAnimDuration)
                    {
                        float normalizedTimeScale = (currentTime - startTime) / scaleAnimDuration;
                        float curveValueScale = curve.Evaluate(Mathf.Clamp01(normalizedTimeScale)); 
                        card.transform.localScale = Vector3.Lerp(startScale, animTargetScale, curveValueScale);
                    }
                    else
                    {
                        card.transform.localScale = animTargetScale;
                    }
                }

                await System.Threading.Tasks.Task.Yield();
            }

            card.transform.position = targetPosition;
            card.transform.rotation = targetAbsoluteRotation;
            if (applyScalingEffects)
            {
                card.transform.localScale = animTargetScale;
            }
            return true;
        }

        private async Task<bool> AnimateFlipSequenceInternal(GameObject card, bool isFlipped)
        {
            if (card == null) return false;

            Vector3 startPosition = card.transform.position;
            Quaternion startRotation = card.transform.rotation;
            Vector3 startScale = card.transform.localScale; // Capture start scale

            bool shouldPerformFullElevationSequence = true; 
            CardInteractionSystem cardInteractionSystem = m_TableManager.GetSystem<CardInteractionSystem>();
            if (cardInteractionSystem != null)
            {
                if (cardInteractionSystem.IsCardVisuallyLevitated(card))
                {
                    shouldPerformFullElevationSequence = false; // Card is already up, just rotate it.
                }
            }

            float originalZ = startPosition.z;
            Vector3 elevatedPosition = startPosition;
            if (shouldPerformFullElevationSequence)
            {
                elevatedPosition.z = startPosition.z - m_FlipElevationHeight;
            }
            
            Vector3 targetElevatedScale = startScale;
            if (m_EnableElevationScaling && shouldPerformFullElevationSequence)
            {
                targetElevatedScale = startScale * m_ElevationScaleFactor;
            }

            Quaternion targetRotation = isFlipped ? startRotation * Quaternion.Euler(0, 180, 0) : startRotation * Quaternion.Euler(0, -180, 0);
            Vector3 finalPosition = shouldPerformFullElevationSequence ? new Vector3(startPosition.x, startPosition.y, originalZ) : startPosition;
            Vector3 finalScale = startScale; // Final scale should be the original scale


            float startTime = Time.time;
            float elevationStartTime = startTime;
            float rotationStartTime = startTime; // Rotation always starts immediately
            float dropStartTime = startTime;     // Will be adjusted if full sequence
            
            float currentFlipElevationDuration = m_FlipElevationDuration;
            float currentFlipDropDuration = m_FlipDropDuration;
            float currentFlipRotationDelay = 0f; // No artificial delay if already up
            float currentFlipDropDelay = m_FlipRotationDuration; // Drop starts after rotation if already up

            if (shouldPerformFullElevationSequence)
            {
                // Use configured delays and durations for the full sequence
                rotationStartTime = startTime + m_FlipRotationDelay;
                dropStartTime = rotationStartTime + m_FlipDropDelay; // This was m_FlipDropDelay relative to startTime, should be relative to rotation start or a fixed point.
                                                                 // Let's make drop delay relative to rotation start for clarity.
                dropStartTime = rotationStartTime + (m_FlipRotationDuration - m_FlipDropDelay > 0 ? m_FlipRotationDuration - m_FlipDropDelay : 0.1f); // Simplified: drop starts near end of rotation
                // A more robust way would be: dropStartTime = rotationStartTime + m_FlipRotationDuration - m_FlipDropDuration + some_overlap_or_small_delay;
                // For now, keep it based on original logic structure with delays from start time
                rotationStartTime = startTime + m_FlipRotationDelay;
                dropStartTime = startTime + m_FlipDropDelay; // Revert to original delay logic structure for minimal change from before
                currentFlipRotationDelay = m_FlipRotationDelay; // Not used directly in loop, but sets context
                currentFlipDropDelay = m_FlipDropDelay;         // Not used directly in loop
            }
            else // Card is already visually levitated, timings are simpler
            {
                currentFlipElevationDuration = 0; // No elevation phase
                currentFlipDropDuration = 0;    // No drop phase
                // rotationStartTime is already Time.time
                dropStartTime = startTime + m_FlipRotationDuration; // End of rotation is effectively end of animation
            }

            float elevationEndTime = elevationStartTime + currentFlipElevationDuration;
            float rotationEndTime = rotationStartTime + m_FlipRotationDuration;
            float dropEndTime = shouldPerformFullElevationSequence ? dropStartTime + currentFlipDropDuration : rotationEndTime; 
            // If not full sequence, dropEndTime is same as rotationEndTime (no separate drop)
            
            float animationEndTime = Mathf.Max(elevationEndTime, rotationEndTime, dropEndTime);
            
            if (!shouldPerformFullElevationSequence) {
                // If card is already elevated, we effectively skip discrete elevation/drop animation phases.
                // The card flips at its current height.
                elevationEndTime = elevationStartTime; // No duration for elevation phase
                dropEndTime = rotationEndTime;       // Drop phase effectively has no duration / coincides with rotation end
                animationEndTime = rotationEndTime;  // Animation ends when rotation ends
            }

            while (Time.time < animationEndTime)
            {
                if (m_RequestedCancellations.Contains(card))
                {
                    m_RequestedCancellations.Remove(card); 
                    if (m_EnableElevationScaling) card.transform.localScale = startScale; // Restore original scale on cancel
                    throw new System.OperationCanceledException("Flip sequence animation cancelled.");
                }

                float currentTime = Time.time;

                // Elevation part (only if performing full sequence)
                if (shouldPerformFullElevationSequence && currentTime >= elevationStartTime && currentTime <= elevationEndTime)
                {
                    float normalizedTime = Mathf.Clamp01((currentTime - elevationStartTime) / m_FlipElevationDuration); 
                    float curveValue = m_FlipAnimationCurve.Evaluate(normalizedTime);
                    Vector3 currentElevationPos = Vector3.Lerp(startPosition, elevatedPosition, curveValue);
                    
                    if (m_EnableElevationScaling)
                    {
                        Vector3 currentElevatedScale = Vector3.Lerp(startScale, targetElevatedScale, curveValue);
                        card.transform.localScale = currentElevatedScale;
                    }

                    if (currentTime < dropStartTime) card.transform.position = currentElevationPos;
                }
                
                // Rotation part
                if (currentTime >= rotationStartTime && currentTime <= rotationEndTime)
                {
                    float normalizedTime = Mathf.Clamp01((currentTime - rotationStartTime) / m_FlipRotationDuration);
                    float curveValue = m_FlipAnimationCurve.Evaluate(normalizedTime);
                    card.transform.rotation = Quaternion.Slerp(startRotation, targetRotation, curveValue);

                    // If not doing the full elevation sequence (i.e., card was already up),
                    // and it's a selected card, it might finish rotation early (original early exit logic)
                    if (!shouldPerformFullElevationSequence && normalizedTime >= 0.99f) 
                    { 
                        // This early exit was for selected cards, but now `shouldPerformFullElevationSequence` covers that.
                        // card.transform.rotation = targetRotation;
                        // return true; 
                        // Let animation complete to targetRotation to avoid snap if 0.99 is not 1.
                    }
                }

                // Drop part (only if performing full sequence)
                if (shouldPerformFullElevationSequence && currentTime >= dropStartTime && currentTime <= dropEndTime)
                {
                    float normalizedTime = Mathf.Clamp01((currentTime - dropStartTime) / m_FlipDropDuration); 
                    float curveValue = m_FlipAnimationCurve.Evaluate(normalizedTime);
                    Vector3 currentPosition = Vector3.Lerp(elevatedPosition, finalPosition, curveValue);
                    card.transform.position = currentPosition;

                    if (m_EnableElevationScaling)
                    {
                        // When dropping, scale from targetElevatedScale back to finalScale (original startScale)
                        Vector3 currentDroppedScale = Vector3.Lerp(targetElevatedScale, finalScale, curveValue);
                        card.transform.localScale = currentDroppedScale;
                    }
                }
                await System.Threading.Tasks.Task.Yield();
            }

            card.transform.position = finalPosition; // Ensure final position is set
            card.transform.rotation = targetRotation;
            if (m_EnableElevationScaling) // Ensure final scale is set to original
            {
                card.transform.localScale = finalScale; 
            }
            return true;
        }

        #endregion

        #region Public Helper Methods

        /// <summary>
        /// Checks if an object is currently being animated
        /// </summary>
        public bool IsObjectAnimating(GameObject obj) => obj != null && m_AnimatingObjects.Contains(obj);

        /// <summary>
        /// Checks if an object has pending animations in its queue
        /// </summary>
        public bool HasPendingAnimations(GameObject obj)
        {
            return obj != null &&
                   m_AnimationQueues.ContainsKey(obj) &&
                   m_AnimationQueues[obj].Count > 0;
        }

        /// <summary>
        /// Clears all pending animations for an object
        /// </summary>
        public void ClearAnimationQueue(GameObject obj)
        {
            if (obj != null && m_AnimationQueues.ContainsKey(obj))
            {
                Queue<AnimationRequest> queue = m_AnimationQueues[obj];
                while (queue.Count > 0)
                {
                    AnimationRequest clearedRequest = queue.Dequeue();
                    // TrySetResult to avoid exception if already set (e.g. by a completed animation)
                    clearedRequest.CompletionSource.TrySetResult(false); 
                }
                // The queue is now empty, m_AnimationQueues[obj].Clear() is implicitly done.
            }
        }

        /// <summary>
        /// Cancels all animations for an object
        /// </summary>
        public void CancelAnimations(GameObject obj)
        {
            if (obj == null) return;

            // Clear any pending animations in the queue for this object
            ClearAnimationQueue(obj);

            // If an animation for this object is currently running (i.e., it's in m_AnimatingObjects),
            // add it to m_RequestedCancellations. The running animation's internal loop will
            // detect this, throw OperationCanceledException, which ProcessAnimationRequest will catch.
            if (m_AnimatingObjects.Contains(obj))
            {
                m_RequestedCancellations.Add(obj);
                // The OnAnimationCancelled event will be invoked by ProcessAnimationRequest
                // when the OperationCanceledException is caught.
            }
        }

        #endregion
    }
}

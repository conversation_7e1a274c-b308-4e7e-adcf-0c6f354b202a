using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;

namespace Workshop.UXLab
{
    [CreateAssetMenu(fileName = "BoxSelectionSystem", menuName = "Workshop/UXLab/Systems/BoxSelectionSystem")]
    public class BoxSelectionSystem : TableSystem
    {
        [Header("Box Selection Settings")]
        [SerializeField] private Color m_BoxSelectionColor = new Color(0.2f, 0.6f, 1.0f, 0.3f);
        [SerializeField] private Color m_BoxSelectionBorderColor = new Color(0.2f, 0.6f, 1.0f, 0.8f);
        [SerializeField] private float m_BoxSelectionBorderWidth = 2f;

        // References
        private BoxSelectionUI m_BoxSelectionUI;
        private CardInteractionSystem m_CardInteractionSystem;
        private GameObject m_UIHostObject;

        // Box selection state
        private bool m_IsBoxSelecting = false;
        private Vector2 m_BoxStartPosition;
        private Vector2 m_BoxCurrentPosition;
        private List<GameObject> m_SelectedCards = new List<GameObject>();

        // Events
        public delegate void CardsSelectedHandler(List<GameObject> selectedCards);
        public event CardsSelectedHandler OnCardsSelected;

        public override void Init(TableManager tableManager)
        {
            base.Init(tableManager);

            // Get reference to the CardInteractionSystem
            m_CardInteractionSystem = m_TableManager.GetSystem<CardInteractionSystem>();
            if (m_CardInteractionSystem == null)
            {
                Debug.LogError("BoxSelectionSystem: CardInteractionSystem not found. Box selection will not function properly.", this);
            }

            // Subscribe to TableManager events
            m_TableManager.OnBlankClicked += HandleBlankClicked;
            m_TableManager.OnUpdate += ProcessBoxSelectionUpdate;

            // Initialize the UI
            InitializeUI();
        }

        public override void Shutdown()
        {
            if (!m_IsInitialized)
                return;

            // Unsubscribe from events
            if (m_TableManager != null)
            {
                m_TableManager.OnBlankClicked -= HandleBlankClicked;
                m_TableManager.OnUpdate -= ProcessBoxSelectionUpdate;
            }

            // Clean up UI
            if (m_UIHostObject != null)
            {
                UnityEngine.Object.Destroy(m_UIHostObject);
                m_UIHostObject = null;
            }

            base.Shutdown();
        }

        private void InitializeUI()
        {
            // Create the UI helper
            m_BoxSelectionUI = new BoxSelectionUI(m_TableManager.UIPanelSettings);
            m_UIHostObject = m_BoxSelectionUI.UIHostObject;

            // Configure the box selection appearance
            m_BoxSelectionUI.SetBoxSelectionColor(m_BoxSelectionColor);
            m_BoxSelectionUI.SetBoxSelectionBorderColor(m_BoxSelectionBorderColor);
            m_BoxSelectionUI.SetBoxSelectionBorderWidth(m_BoxSelectionBorderWidth);

            // Hide the box initially
            m_BoxSelectionUI.HideSelectionBox();
        }

        private void HandleBlankClicked()
        {
            // Start box selection when clicking on blank space
            if (Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift))
            {
                StartBoxSelection(Input.mousePosition);
            }
        }

        private void ProcessBoxSelectionUpdate()
        {
            // Continue box selection if active
            if (m_IsBoxSelecting)
            {
                UpdateBoxSelection(Input.mousePosition);

                // End box selection when mouse button is released
                if (Input.GetMouseButtonUp(0))
                {
                    EndBoxSelection();
                }
            }

            // Start box selection when clicking on blank space with shift held
            if (Input.GetMouseButtonDown(0) && (Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift)))
            {
                // Check if mouse is over UI
                bool isOverUI = UnityEngine.EventSystems.EventSystem.current != null &&
                                UnityEngine.EventSystems.EventSystem.current.IsPointerOverGameObject();

                // Only start box selection if not over UI and not over a card
                if (!isOverUI && m_TableManager.CurrentHoveredComponent == null)
                {
                    StartBoxSelection(Input.mousePosition);
                }
            }
        }

        private void StartBoxSelection(Vector2 screenPosition)
        {
            m_IsBoxSelecting = true;
            m_BoxStartPosition = screenPosition;
            m_BoxCurrentPosition = screenPosition;

            // Show the selection box
            m_BoxSelectionUI.ShowSelectionBox();
            m_BoxSelectionUI.UpdateSelectionBox(m_BoxStartPosition, m_BoxCurrentPosition);
        }

        private void UpdateBoxSelection(Vector2 screenPosition)
        {
            m_BoxCurrentPosition = screenPosition;

            // Update the selection box UI
            m_BoxSelectionUI.UpdateSelectionBox(m_BoxStartPosition, m_BoxCurrentPosition);
        }

        private void EndBoxSelection()
        {
            m_IsBoxSelecting = false;

            // Hide the selection box
            m_BoxSelectionUI.HideSelectionBox();

            // Find all cards within the selection box
            List<GameObject> selectedCards = GetCardsInSelectionBox();

            // Notify listeners about the selected cards
            if (selectedCards.Count > 0)
            {
                OnCardsSelected?.Invoke(selectedCards);

                // Use the multi-selection functionality in CardInteractionSystem
                if (m_CardInteractionSystem != null)
                {
                    if (selectedCards.Count == 1)
                    {
                        // If only one card is selected, use the single selection method
                        m_CardInteractionSystem.SelectCard(selectedCards[0]);
                    }
                    else
                    {
                        // If multiple cards are selected, use the multi-selection method
                        m_CardInteractionSystem.SelectMultipleCards(selectedCards);
                    }
                }
            }

            m_SelectedCards = selectedCards;
        }

        private List<GameObject> GetCardsInSelectionBox()
        {
            List<GameObject> cardsInBox = new List<GameObject>();

            // Get screen bounds of the selection box
            Rect selectionRect = GetSelectionRect();

            // Check all table components to see if they're cards and within the selection box
            foreach (GameObject component in m_TableManager.TableComponents)
            {
                if (component == null) continue;

                // Check if it's a card
                if (component.GetComponent<CardMeshGenerator>() != null)
                {
                    // Convert card position to screen space
                    Vector2 screenPos = GetScreenPosition(component.transform.position);

                    // Check if the card is within the selection box
                    if (selectionRect.Contains(screenPos))
                    {
                        cardsInBox.Add(component);
                    }
                }
            }

            return cardsInBox;
        }

        private Rect GetSelectionRect()
        {
            float minX = Mathf.Min(m_BoxStartPosition.x, m_BoxCurrentPosition.x);
            float minY = Mathf.Min(m_BoxStartPosition.y, m_BoxCurrentPosition.y);
            float width = Mathf.Abs(m_BoxCurrentPosition.x - m_BoxStartPosition.x);
            float height = Mathf.Abs(m_BoxCurrentPosition.y - m_BoxStartPosition.y);

            return new Rect(minX, minY, width, height);
        }

        private Vector2 GetScreenPosition(Vector3 worldPosition)
        {
            Camera camera = m_TableManager.CameraController.Camera;
            return camera.WorldToScreenPoint(worldPosition);
        }
    }
}

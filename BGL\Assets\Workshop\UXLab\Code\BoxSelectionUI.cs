using UnityEngine;
using UnityEngine.UIElements;

namespace Workshop.UXLab
{
    /// <summary>
    /// Helper class to manage the UI for box selection
    /// </summary>
    public class BoxSelectionUI
    {
        // Constants
        private const string k_SelectionBoxName = "box-selection";

        // UI Elements
        private UIDocument m_Document;
        private VisualElement m_SelectionBox;
        private VisualElement m_RootElement;

        // Properties
        public GameObject UIHostObject { get; private set; }

        /// <summary>
        /// Constructor that creates the UI elements for box selection
        /// </summary>
        /// <param name="panelSettings">The panel settings to use for the UI</param>
        public BoxSelectionUI(PanelSettings panelSettings)
        {
            // Create a GameObject to host the UI
            UIHostObject = new GameObject("BoxSelection_UIHost");
            m_Document = UIHostObject.AddComponent<UIDocument>();

            // Set up the panel settings
            m_Document.panelSettings = panelSettings;

            // Create the root element
            m_RootElement = m_Document.rootVisualElement;

            // Create the selection box element
            CreateSelectionBox();

            // Add the stylesheet
            AddStylesheet();
        }

        /// <summary>
        /// Creates the selection box visual element
        /// </summary>
        private void CreateSelectionBox()
        {
            // Create the selection box element
            m_SelectionBox = new VisualElement
            {
                name = k_SelectionBoxName,
                pickingMode = PickingMode.Ignore // Ignore input events
            };

            // Add the selection box to the root element
            m_RootElement.Add(m_SelectionBox);

            // Add the class for styling
            m_SelectionBox.AddToClassList("box-selection");

            // Hide the selection box initially
            m_SelectionBox.style.display = DisplayStyle.None;
        }

        /// <summary>
        /// Adds the stylesheet for the selection box
        /// </summary>
        private void AddStylesheet()
        {
            // Load the stylesheet from Resources
            var styleSheet = Resources.Load<StyleSheet>("UI/BoxSelectionStylesheet");

            if (styleSheet != null)
            {
                // Add the stylesheet to the document
                m_RootElement.styleSheets.Add(styleSheet);
            }
            else
            {
                Debug.LogError("BoxSelectionUI: Could not load stylesheet from Resources/UI/BoxSelectionStylesheet");

                // Add default styles programmatically as fallback
                /*m_SelectionBox.style.position = Position.Absolute;
                m_SelectionBox.style.backgroundColor = new Color(0.2f, 0.6f, 1.0f, 0.3f);
                m_SelectionBox.style.borderWidth = 2;
                m_SelectionBox.style.borderColor = new Color(0.2f, 0.6f, 1.0f, 0.8f);*/
            }
        }

        /// <summary>
        /// Shows the selection box
        /// </summary>
        public void ShowSelectionBox()
        {
            m_SelectionBox.style.display = DisplayStyle.Flex;
        }

        /// <summary>
        /// Hides the selection box
        /// </summary>
        public void HideSelectionBox()
        {
            m_SelectionBox.style.display = DisplayStyle.None;
        }

        /// <summary>
        /// Updates the selection box position and size
        /// </summary>
        /// <param name="startPosition">The start position of the selection box in screen coordinates</param>
        /// <param name="currentPosition">The current position of the selection box in screen coordinates</param>
        public void UpdateSelectionBox(Vector2 startPosition, Vector2 currentPosition)
        {
            // Calculate the position and size of the selection box
            float minX = Mathf.Min(startPosition.x, currentPosition.x);
            float minY = Mathf.Min(startPosition.y, currentPosition.y);
            float width = Mathf.Abs(currentPosition.x - startPosition.x);
            float height = Mathf.Abs(currentPosition.y - startPosition.y);

            // Convert screen coordinates to UI coordinates (invert Y)
            float screenHeight = Screen.height;
            float uiMinY = screenHeight - minY - height;

            // Update the selection box position and size
            m_SelectionBox.style.left = minX;
            m_SelectionBox.style.top = uiMinY;
            m_SelectionBox.style.width = width;
            m_SelectionBox.style.height = height;
        }

        /// <summary>
        /// Sets the color of the selection box
        /// </summary>
        /// <param name="color">The color to set</param>
        public void SetBoxSelectionColor(Color color)
        {
            m_SelectionBox.style.backgroundColor = color;
        }

        /// <summary>
        /// Sets the border color of the selection box
        /// </summary>
        /// <param name="color">The color to set</param>
        public void SetBoxSelectionBorderColor(Color color)
        {
            //m_SelectionBox.style.borderColor = color;
        }

        /// <summary>
        /// Sets the border width of the selection box
        /// </summary>
        /// <param name="width">The width to set</param>
        public void SetBoxSelectionBorderWidth(float width)
        {
            //m_SelectionBox.style.borderWidth = width;
        }
    }
}

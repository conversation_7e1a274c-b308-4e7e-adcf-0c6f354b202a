using UnityEngine;
using System.Collections;
using System;
using UnityEngine.EventSystems; // Add this for UI checks

namespace Workshop.UXLab
{
    /// <summary>
    /// Controls camera movement for the table view
    /// Camera always looks at an imaginary focal point on the table
    /// </summary>
    public class CameraController : MonoBehaviour
    {
        [Header("Camera Settings")]
        [SerializeField] private float m_CameraAngle = 0f; // Default camera angle (0 looks forward along Z)
        [SerializeField] private bool m_OrthographicMode = false; // Toggle for orthographic projection
        [SerializeField] private float m_MinOrthographicSize = 1f; // Minimum orthographic size when zoomed in
        [SerializeField] private float m_MaxOrthographicSize = 10f; // Maximum orthographic size when zoomed out
        [SerializeField] private KeyCode m_ToggleProjectionKey = KeyCode.O; // Key to toggle between orthographic and perspective

        // Internally, 0 = farthest, 1 = nearest.
        // Public accessors (Get/SetNormalizedZoom) will reflect this interpretation.
        [Range(0f, 1f)]
        [SerializeField] private float m_NormalizedZoomInternal = 0.9f; // Default closer to nearest (zoomed in)

        [Header("Zoom Settings")]
        [SerializeField] private float m_ZoomSpeed = 0.2f;
        [SerializeField] private float m_PerspectiveZoomMultiplier = 1.5f; // Makes perspective zoom faster
        [SerializeField] private float m_MinZoomDistance = 0.25f;
        [SerializeField] private float m_MaxZoomDistance = 10.0f;
        [SerializeField] private float m_ZoomSmoothTime = 0.1f;

        [Header("Pan Settings")]
        [SerializeField] private float m_PanSpeed = 0.5f;
        [SerializeField] private float m_MousePanSpeed = 0.001f;
        [SerializeField] private bool m_InvertMousePan = false;

        [Header("Focus Settings")]
        [SerializeField] private float m_FocusDuration = 0.5f; // Duration of the focus animation
        [SerializeField] private AnimationCurve m_FocusEaseCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f); // Easing for focus
        [SerializeField] private float m_DefaultFocusZoom = 3f; // Default zoom distance when focusing

        [Header("Boundaries")]
        [SerializeField] private Vector2 m_XYBoundary = new Vector2(5f, 5f); // X and Y boundaries for the look target
        [SerializeField] private float m_InteractionPlaneZ = 0f; // The Z position of the interaction plane

        [Header("UI Interaction")]
        [SerializeField] private bool m_BlockInputWhenOverUI = true; // Block input when mouse is over UI

        private bool m_InputEnabled = true; // Flag to completely enable/disable input

        // The imaginary object the camera always looks at (on the XY plane)
        private Vector3 m_LookTarget;
        private float m_CurrentDistance;
        private float m_TargetDistance;
        private float m_ZoomVelocity;
        private float m_CurrentOrthographicSize;
        private float m_TargetOrthographicSize;
        private float m_OrthoSizeVelocity;

        private Camera m_MainCamera;
        private bool m_IsDragging = false;
        private Vector3 m_LastMousePosition;
        private bool m_IsFocusing = false; // Flag to indicate if a focus animation is in progress
        private bool m_MousePanEnabled = true; // Flag to control mouse panning

        // Event invoked when the normalized zoom level changes.
        // Provides the new normalized zoom (0=farthest, 1=nearest).
        public event Action<float> OnZoomChanged;

        /// <summary>
        /// Get the main camera component
        /// </summary>
        public Camera Camera => m_MainCamera;

        private void Awake()
        {
            m_MainCamera = GetComponent<Camera>();

            // Initialize camera angle (now likely 0 for forward view)
            transform.rotation = Quaternion.Euler(m_CameraAngle, 0, 0);

            // Set initial zoom values based on normalized zoom
            SetZoomFromPublicNormalized(m_NormalizedZoomInternal);

            // Set initial target on the interaction plane
            m_LookTarget = new Vector3(0, 0, m_InteractionPlaneZ);

            // Set initial projection mode
            m_MainCamera.orthographic = m_OrthographicMode;
            if (m_OrthographicMode)
            {
                m_MainCamera.orthographicSize = m_CurrentOrthographicSize;
            }

            // Position the camera
            PositionCamera();
        }

        private void Update()
        {
            // Toggle projection mode
            if (Input.GetKeyDown(m_ToggleProjectionKey))
            {
                ToggleOrthographicMode();
            }

            // Only handle input if not currently in a focus animation
            if (!m_IsFocusing)
            {
                HandleInput();
            }

            UpdateCamera();
        }

        // Convert a value from one range to another
        private float ConvertRange(float value, float oldMin, float oldMax, float newMin, float newMax)
        {
            float normalizedValue = Mathf.InverseLerp(oldMin, oldMax, value);
            return Mathf.Lerp(newMin, newMax, normalizedValue);
        }

        // Get normalized zoom value (0-1: 0=farthest, 1=nearest) from current zoom settings
        // This calculates based on current distance/size and returns the user-facing interpretation.
        private float CalculatePublicNormalizedZoom()
        {
            float rawNormalizedZoom;
            if (m_OrthographicMode)
            {
                // Raw: 0 = min size (nearest), 1 = max size (farthest)
                rawNormalizedZoom = Mathf.InverseLerp(m_MinOrthographicSize, m_MaxOrthographicSize, m_CurrentOrthographicSize);
            }
            else
            {
                // Raw: 0 = min dist (nearest), 1 = max dist (farthest)
                rawNormalizedZoom = Mathf.InverseLerp(m_MinZoomDistance, m_MaxZoomDistance, m_CurrentDistance);
            }
            // Invert for internal/public use (0 = farthest, 1 = nearest)
            return 1f - rawNormalizedZoom; 
        }

        // Set internal zoom values based on the PUBLIC normalized zoom (0=farthest, 1=nearest)
        private void SetZoomFromPublicNormalized(float publicNormalizedValue)
        {
            publicNormalizedValue = Mathf.Clamp01(publicNormalizedValue);
            m_NormalizedZoomInternal = publicNormalizedValue; // Store the direct public value

            // Lerp from Max to Min based on the normalized value (0 -> Max, 1 -> Min)
            m_CurrentDistance = m_TargetDistance = Mathf.Lerp(m_MaxZoomDistance, m_MinZoomDistance, m_NormalizedZoomInternal);
            m_CurrentOrthographicSize = m_TargetOrthographicSize = Mathf.Lerp(m_MaxOrthographicSize, m_MinOrthographicSize, m_NormalizedZoomInternal);

            // Update the inspector range slider if needed (it reflects m_NormalizedZoomInternal)
            #if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(this);
            #endif

            // Notify listeners
            OnZoomChanged?.Invoke(m_NormalizedZoomInternal);
        }

        private void SetCameraProjectionMode(bool orthographic)
        {
            // Store the current PUBLIC normalized zoom
            float publicNormalizedZoom = GetNormalizedZoom();

            // Change projection mode
            m_OrthographicMode = orthographic;
            m_MainCamera.orthographic = orthographic;

            // Apply the same PUBLIC normalized zoom to the new projection mode
            if (orthographic)
            {
                // Lerp from Max to Min
                m_CurrentOrthographicSize = m_TargetOrthographicSize = Mathf.Lerp(m_MaxOrthographicSize, m_MinOrthographicSize, publicNormalizedZoom);
                m_MainCamera.orthographicSize = m_CurrentOrthographicSize;
            }
            else
            {
                // Lerp from Max to Min
                m_CurrentDistance = m_TargetDistance = Mathf.Lerp(m_MaxZoomDistance, m_MinZoomDistance, publicNormalizedZoom);
            }

            // Update the internal normalized zoom state to match the current reality
            m_NormalizedZoomInternal = publicNormalizedZoom;
        }

        private void HandleInput()
        {
            // Skip all input handling if input is disabled
            if (!m_InputEnabled)
                return;

            // Check if mouse is over UI and we should block input
            bool pointerOverUI = IsPointerOverUIElement();

            // Handle zoom with mouse wheel
            float scrollDelta = Input.mouseScrollDelta.y;
            if (scrollDelta != 0 && (!m_BlockInputWhenOverUI || !pointerOverUI))
            {
                if (m_OrthographicMode)
                {
                    // In orthographic mode, adjust orthographic size
                    m_TargetOrthographicSize -= scrollDelta * m_ZoomSpeed;
                    m_TargetOrthographicSize = Mathf.Clamp(m_TargetOrthographicSize, m_MinOrthographicSize, m_MaxOrthographicSize);
                    
                    // Update internal normalized zoom based on the new target size
                    // Target size range: Min (nearest) to Max (farthest)
                    // Internal zoom range: 1 (nearest) to 0 (farthest)
                    float rawZoom = Mathf.InverseLerp(m_MinOrthographicSize, m_MaxOrthographicSize, m_TargetOrthographicSize);
                    m_NormalizedZoomInternal = 1f - rawZoom;
                    
                    // Also update perspective distance based on internal normalized zoom (0=farthest, 1=nearest)
                    m_TargetDistance = Mathf.Lerp(m_MaxZoomDistance, m_MinZoomDistance, m_NormalizedZoomInternal);
                }
                else
                {
                    // In perspective mode, adjust distance with speed multiplier
                    m_TargetDistance -= scrollDelta * m_ZoomSpeed * m_PerspectiveZoomMultiplier;
                    m_TargetDistance = Mathf.Clamp(m_TargetDistance, m_MinZoomDistance, m_MaxZoomDistance);
                    
                    // Update internal normalized zoom based on the new target distance
                    // Target distance range: Min (nearest) to Max (farthest)
                    // Internal zoom range: 1 (nearest) to 0 (farthest)
                    float rawZoom = Mathf.InverseLerp(m_MinZoomDistance, m_MaxZoomDistance, m_TargetDistance);
                    m_NormalizedZoomInternal = 1f - rawZoom;
                    
                    // Also update orthographic size based on internal normalized zoom (0=farthest, 1=nearest)
                    m_TargetOrthographicSize = Mathf.Lerp(m_MaxOrthographicSize, m_MinOrthographicSize, m_NormalizedZoomInternal);
                }
            }

            // Handle keyboard movement (WASD for XY plane)
            Vector3 moveDirection = Vector3.zero;
            if (Input.GetKey(KeyCode.W)) moveDirection.y += 1f; // W -> +Y (Up)
            if (Input.GetKey(KeyCode.S)) moveDirection.y -= 1f; // S -> -Y (Down)
            if (Input.GetKey(KeyCode.A)) moveDirection.x -= 1f; // A -> -X (Left)
            if (Input.GetKey(KeyCode.D)) moveDirection.x += 1f; // D -> +X (Right)

            if (moveDirection != Vector3.zero)
            {
                // Calculate movement in world space (already aligned with XY if camera angle is 0)
                // If camera has Y rotation, adjust accordingly
                Quaternion yRotation = Quaternion.Euler(0, transform.eulerAngles.y, 0);
                Vector3 movement = yRotation * moveDirection.normalized * (m_PanSpeed * Time.deltaTime);

                // Only keep XY movement, ignore Z for look target
                movement.z = 0;

                // Move the look target
                MoveLookTarget(movement);
            }

            // Handle mouse drag input (right or middle mouse button)
            if (m_MousePanEnabled)
            {
                // First, check if a mouse button was released. If so, stop dragging.
                // This must happen regardless of UI state to prevent m_IsDragging getting stuck.
                if (Input.GetMouseButtonUp(1) || Input.GetMouseButtonUp(2))
                {
                    m_IsDragging = false;
                }

                // Then, check if a new drag is being initiated.
                // This should only happen if not over UI (if blocking is enabled).
                bool canStartDrag = !m_BlockInputWhenOverUI || !pointerOverUI;
                if (canStartDrag)
                {
                    if (Input.GetMouseButtonDown(1) || Input.GetMouseButtonDown(2))
                    {
                        m_IsDragging = true;
                        m_LastMousePosition = Input.mousePosition;
                    }
                }

                // If currently dragging (m_IsDragging is true), process the mouse movement.
                // m_IsDragging would have been set to false above if the button was released.
                if (m_IsDragging)
                {
                    Vector3 mouseDelta = Input.mousePosition - m_LastMousePosition;

                    if (mouseDelta.magnitude > 0.01f)
                    {
                        // Movement based on screen delta mapped to world XY
                        float invertFactor = m_InvertMousePan ? 1 : -1;

                        // Directly use camera's right and up vectors projected onto XY plane (should be world X/Y if camera angle=0)
                        Vector3 right = transform.right; // Assumes camera does not roll (Z rotation)
                        Vector3 up = transform.up;     // Assumes camera does not roll (Z rotation)

                        Vector3 movement = (right * mouseDelta.x + up * mouseDelta.y) * m_MousePanSpeed * invertFactor;

                        // Only keep XY movement for the look target
                        movement.z = 0;

                        // Move the look target
                        MoveLookTarget(movement);
                    }

                    m_LastMousePosition = Input.mousePosition;
                }
            }
        }

        private void MoveLookTarget(Vector3 movement)
        {
            // Move the look target
            Vector3 newPosition = m_LookTarget + movement;

            // Enforce XY boundaries
            newPosition.x = Mathf.Clamp(newPosition.x, -m_XYBoundary.x, m_XYBoundary.x);
            newPosition.y = Mathf.Clamp(newPosition.y, -m_XYBoundary.y, m_XYBoundary.y);
            newPosition.z = m_InteractionPlaneZ; // Keep at interaction plane depth

            m_LookTarget = newPosition;
        }

        private void UpdateCamera()
        {
            if (!m_IsFocusing)
            {
                if (m_OrthographicMode)
                {
                    // Smooth orthographic size for orthographic mode
                    m_CurrentOrthographicSize = Mathf.SmoothDamp(m_CurrentOrthographicSize, m_TargetOrthographicSize, ref m_OrthoSizeVelocity, m_ZoomSmoothTime);
                    m_MainCamera.orthographicSize = m_CurrentOrthographicSize;
                    
                    // Keep perspective value in sync for seamless transitions (using inverted Lerp)
                    m_CurrentDistance = Mathf.Lerp(m_MaxZoomDistance, m_MinZoomDistance, m_NormalizedZoomInternal);
                }
                else
                {
                    // Smooth zoom for perspective mode
                    m_CurrentDistance = Mathf.SmoothDamp(m_CurrentDistance, m_TargetDistance, ref m_ZoomVelocity, m_ZoomSmoothTime);
                    
                    // Keep orthographic value in sync for seamless transitions (using inverted Lerp)
                    m_CurrentOrthographicSize = Mathf.Lerp(m_MaxOrthographicSize, m_MinOrthographicSize, m_NormalizedZoomInternal);
                }

                // If smoothing caused a significant change, notify listeners
                float previousZoomInternal = m_NormalizedZoomInternal; 
                if (m_OrthographicMode) {
                    // Calculate raw zoom (0=nearest, 1=farthest)
                    float rawZoom = Mathf.InverseLerp(m_MinOrthographicSize, m_MaxOrthographicSize, m_CurrentOrthographicSize);
                    m_NormalizedZoomInternal = 1f - rawZoom; // Invert for internal state
                } else {
                     // Calculate raw zoom (0=nearest, 1=farthest)
                    float rawZoom = Mathf.InverseLerp(m_MinZoomDistance, m_MaxZoomDistance, m_CurrentDistance);
                    m_NormalizedZoomInternal = 1f - rawZoom; // Invert for internal state
                }
                
                if (Mathf.Abs(previousZoomInternal - m_NormalizedZoomInternal) > 0.001f)
                {
                    OnZoomChanged?.Invoke(m_NormalizedZoomInternal);
                }
            }

            // Update camera position based on look target and distance
            PositionCamera();
        }

        private void PositionCamera()
        {
            // Calculate camera offset based on angle and distance
            // If angle is 0 (looking forward), offset is purely along negative Z
            // If angle is != 0, calculate offset in the camera's local XY plane (rotated by CameraAngle around X-axis)

            // Rotation based purely on the camera angle around the X axis
            Quaternion angleRotation = Quaternion.Euler(m_CameraAngle, 0, 0);

            // The direction from the look target to the camera, initially straight back (along -Z)
            Vector3 baseDirection = Vector3.back * m_CurrentDistance; // Use back instead of forward because camera looks AT target

            // Rotate this direction by the camera angle
            Vector3 rotatedDirection = angleRotation * baseDirection;

            // Position camera relative to the look target
            transform.position = m_LookTarget + rotatedDirection;

            // Make sure camera is looking at the target
            // Use LookAt for simplicity, it handles the orientation correctly
            transform.LookAt(m_LookTarget);
        }

        /// <summary>
        /// Focus the camera on a specific GameObject with smooth animation
        /// </summary>
        /// <param name="target">The GameObject to focus on</param>
        /// <param name="zoom">Optional zoom level (distance from target)</param>
        public void FocusOn(GameObject target, float? zoom = null)
        {
            if (target == null) return;

            // Use the provided zoom or the default
            float targetZoom = zoom.HasValue ? zoom.Value : m_DefaultFocusZoom;

            // Clamp the zoom to allowable range
            if (m_OrthographicMode)
            {
                targetZoom = Mathf.Clamp(targetZoom, m_MinOrthographicSize, m_MaxOrthographicSize);
            }
            else
            {
                targetZoom = Mathf.Clamp(targetZoom, m_MinZoomDistance, m_MaxZoomDistance);
            }

            // Get the position to focus on (set Z to interaction plane)
            Vector3 targetPosition = target.transform.position;
            targetPosition.z = m_InteractionPlaneZ; // Keep at interaction plane depth

            // Enforce XY boundaries
            targetPosition.x = Mathf.Clamp(targetPosition.x, -m_XYBoundary.x, m_XYBoundary.x);
            targetPosition.y = Mathf.Clamp(targetPosition.y, -m_XYBoundary.y, m_XYBoundary.y);

            // Start the focus animation coroutine
            StopAllCoroutines(); // Stop any ongoing focus
            // Target zoom is interpreted as public normalized zoom (0=farthest, 1=nearest)
            StartCoroutine(AnimateFocusTo(targetPosition, targetZoom));
        }

        /// <summary>
        /// Focus the camera on a specific world position with smooth animation
        /// </summary>
        /// <param name="position">The world position to focus on</param>
        /// <param name="zoom">Optional zoom level (distance from target)</param>
        public void FocusOnPosition(Vector3 position, float? zoom = null)
        {
            // Use the provided zoom or the default
            float targetZoom = zoom.HasValue ? zoom.Value : m_DefaultFocusZoom;

            // Clamp the zoom to allowable range
            if (m_OrthographicMode)
            {
                targetZoom = Mathf.Clamp(targetZoom, m_MinOrthographicSize, m_MaxOrthographicSize);
            }
            else
            {
                targetZoom = Mathf.Clamp(targetZoom, m_MinZoomDistance, m_MaxZoomDistance);
            }

            // Project position onto interaction plane
            position.z = m_InteractionPlaneZ;

            // Enforce XY boundaries
            position.x = Mathf.Clamp(position.x, -m_XYBoundary.x, m_XYBoundary.x);
            position.y = Mathf.Clamp(position.y, -m_XYBoundary.y, m_XYBoundary.y);

            // Start the focus animation coroutine
            StopAllCoroutines(); // Stop any ongoing focus
            // Target zoom is interpreted as public normalized zoom (0=farthest, 1=nearest)
            StartCoroutine(AnimateFocusTo(position, targetZoom));
        }

        private IEnumerator AnimateFocusTo(Vector3 targetPosition, float targetPublicZoom) // Renamed param
        {
            m_IsFocusing = true;

            // Store starting values
            Vector3 startLookTarget = m_LookTarget;
            // Start zoom needs to be calculated based on current state, interpreted in the target mode's units
            float startZoomValue;
            float targetZoomValue;

            if (m_OrthographicMode)
            {
                startZoomValue = m_CurrentOrthographicSize;
                // Lerp from Max to Min based on target public zoom (0=farthest, 1=nearest)
                targetZoomValue = Mathf.Lerp(m_MaxOrthographicSize, m_MinOrthographicSize, targetPublicZoom);
            }
            else
            {
                startZoomValue = m_CurrentDistance;
                // Lerp from Max to Min based on target public zoom (0=farthest, 1=nearest)
                targetZoomValue = Mathf.Lerp(m_MaxZoomDistance, m_MinZoomDistance, targetPublicZoom);
            }

            float elapsedTime = 0f;

            while (elapsedTime < m_FocusDuration)
            {
                elapsedTime += Time.deltaTime;
                float t = elapsedTime / m_FocusDuration;

                // Apply easing
                float easedT = m_FocusEaseCurve.Evaluate(t);

                // Interpolate look target and zoom
                m_LookTarget = Vector3.Lerp(startLookTarget, targetPosition, easedT);

                if (m_OrthographicMode)
                {
                    m_CurrentOrthographicSize = Mathf.Lerp(startZoomValue, targetZoomValue, easedT);
                    m_MainCamera.orthographicSize = m_CurrentOrthographicSize;
                    
                    // Keep perspective value in sync
                    float rawOrthoZoom = Mathf.InverseLerp(m_MinOrthographicSize, m_MaxOrthographicSize, m_CurrentOrthographicSize);
                    m_NormalizedZoomInternal = 1f - rawOrthoZoom;
                    m_CurrentDistance = Mathf.Lerp(m_MaxZoomDistance, m_MinZoomDistance, m_NormalizedZoomInternal);
                }
                else
                {
                    m_CurrentDistance = Mathf.Lerp(startZoomValue, targetZoomValue, easedT);
                    
                    // Keep orthographic value in sync
                    float rawPerspZoom = Mathf.InverseLerp(m_MinZoomDistance, m_MaxZoomDistance, m_CurrentDistance);
                    m_NormalizedZoomInternal = 1f - rawPerspZoom;
                    m_CurrentOrthographicSize = Mathf.Lerp(m_MaxOrthographicSize, m_MinOrthographicSize, m_NormalizedZoomInternal);
                }

                yield return null;
            }

            // Ensure we reach exactly the target values
            m_LookTarget = targetPosition;

            if (m_OrthographicMode)
            {
                m_CurrentOrthographicSize = targetZoomValue;
                m_TargetOrthographicSize = targetZoomValue; // Ensure target is also set
                m_MainCamera.orthographicSize = m_CurrentOrthographicSize;
                
                // Keep perspective distance in sync
                float finalRawOrthoZoom = Mathf.InverseLerp(m_MinOrthographicSize, m_MaxOrthographicSize, m_CurrentOrthographicSize);
                m_NormalizedZoomInternal = 1f - finalRawOrthoZoom;
                m_CurrentDistance = m_TargetDistance = Mathf.Lerp(m_MaxZoomDistance, m_MinZoomDistance, m_NormalizedZoomInternal);
            }
            else
            {
                m_CurrentDistance = targetZoomValue;
                m_TargetDistance = targetZoomValue; // Ensure target is also set
                
                // Keep orthographic size in sync
                float finalRawPerspZoom = Mathf.InverseLerp(m_MinZoomDistance, m_MaxZoomDistance, m_CurrentDistance);
                m_NormalizedZoomInternal = 1f - finalRawPerspZoom;
                m_CurrentOrthographicSize = m_TargetOrthographicSize = Mathf.Lerp(m_MaxOrthographicSize, m_MinOrthographicSize, m_NormalizedZoomInternal);
            }

            m_IsFocusing = false;
        }

        public void ResetCamera()
        {
            StopAllCoroutines();
            m_IsFocusing = false;
            m_LookTarget = new Vector3(0, 0, m_InteractionPlaneZ); // Reset to center of XY plane at depth

            // Reset to initial zoom level using the public normalized value stored internally
            SetZoomFromPublicNormalized(m_NormalizedZoomInternal);

            transform.rotation = Quaternion.Euler(m_CameraAngle, 0, 0); // Reset rotation based on angle
            PositionCamera(); // Reposition camera immediately
        }

        /// <summary>
        /// Toggle between orthographic and perspective camera modes
        /// </summary>
        public void ToggleOrthographicMode()
        {
            SetCameraProjectionMode(!m_OrthographicMode);
        }

        /// <summary>
        /// Set the camera projection mode directly
        /// </summary>
        /// <param name="orthographic">Whether to use orthographic projection</param>
        public void SetOrthographicMode(bool orthographic)
        {
            SetCameraProjectionMode(orthographic);
        }

        /// <summary>
        /// Set the zoom level using a normalized value (0-1)
        /// </summary>
        /// <param name="normalizedValue">Normalized zoom (0 = farthest, 1 = nearest)</param>
        public void SetNormalizedZoom(float normalizedValue)
        {
            // Directly use the public value for setting internal state
            SetZoomFromPublicNormalized(normalizedValue);
        }

        /// <summary>
        /// Enable or disable mouse panning controls (right/middle mouse button drag).
        /// </summary>
        /// <param name="enabled">True to enable, false to disable.</param>
        public void SetMousePanEnabled(bool enabled)
        {
            m_MousePanEnabled = enabled;
            // If disabling, ensure we cancel any ongoing drag state within the camera controller
            if (!enabled)
            {
                 m_IsDragging = false;
            }
        }

        /// <summary>
        /// Gets the current zoom level as a normalized value (0-1).
        /// </summary>
        /// <returns>Normalized zoom (0 = farthest, 1 = nearest)</returns>
        public float GetNormalizedZoom()
        {
            // Return the internally stored value, which now represents the public interpretation.
            return m_NormalizedZoomInternal;
        }

        /// <summary>
        /// Focus the camera on an area defined by a center position and radius
        /// </summary>
        /// <param name="centerPosition">The center position of the area to focus on</param>
        /// <param name="radius">The radius of the area to encapsulate in the view</param>
        public void FocusOnArea(Vector3 centerPosition, float radius)
        {
            // Focus on center position with appropriate zoom level based on radius
            float zoomLevel = CalculateZoomLevelForRadius(radius);
            FocusOnPosition(centerPosition, zoomLevel);
        }

        /// <summary>
        /// Calculate appropriate zoom level based on a radius that needs to be visible
        /// </summary>
        /// <param name="radius">The radius to fit in the camera view</param>
        /// <returns>Normalized zoom level (0 = farthest, 1 = nearest)</returns>
        private float CalculateZoomLevelForRadius(float radius)
        {
            // Adjust radius to account for camera angle and ensure visibility
            float adjustedRadius = radius * 2.0f;

            // Convert radius to normalized zoom (0-1)
            // Larger radius = lower zoom value (farther)
            float normalizedZoom;
            if (m_OrthographicMode)
            {
                // Map radius to orthographic size range inversely
                // Adjust orthographic size based on radius - more direct mapping
                float orthoSize = Mathf.Clamp(adjustedRadius, m_MinOrthographicSize, m_MaxOrthographicSize);
                normalizedZoom = 1f - Mathf.InverseLerp(m_MinOrthographicSize, m_MaxOrthographicSize, orthoSize);
            }
            else
            {
                // For perspective camera, use a distance calculation based on radius
                // The distance needs to be adjusted by camera angle
                float angleAdjust = Mathf.Max(0.5f, Mathf.Sin(m_CameraAngle * Mathf.Deg2Rad));
                float distance = adjustedRadius / angleAdjust;
                distance = Mathf.Clamp(distance, m_MinZoomDistance, m_MaxZoomDistance);
                normalizedZoom = 1f - Mathf.InverseLerp(m_MinZoomDistance, m_MaxZoomDistance, distance);
            }

            return Mathf.Clamp01(normalizedZoom);
        }

        /// <summary>
        /// Checks if the pointer is over a UI element
        /// </summary>
        /// <returns>True if the pointer is over UI</returns>
        private bool IsPointerOverUIElement()
        {
            if (EventSystem.current == null)
                return false;

            // Check using the EventSystem
            return EventSystem.current.IsPointerOverGameObject();
        }

        /// <summary>
        /// Enable or disable all camera input handling
        /// </summary>
        /// <param name="enabled">Whether input should be enabled</param>
        public void SetInputEnabled(bool enabled)
        {
            m_InputEnabled = enabled;

            // If disabling input, also cancel any ongoing drag operations
            if (!enabled && m_IsDragging)
            {
                m_IsDragging = false;
            }

            Debug.Log($"[CameraController] Camera input {(enabled ? "enabled" : "disabled")}");
        }

        /// <summary>
        /// Get the current input enabled state
        /// </summary>
        public bool IsInputEnabled()
        {
            return m_InputEnabled;
        }
    }
}

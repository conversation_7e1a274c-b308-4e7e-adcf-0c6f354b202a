using UnityEngine;
using System.Collections.Generic;

namespace Workshop.UXLab
{
    /// <summary>
    /// Defines a rectangular area that can constrain card movement.
    /// Cards within the inner boundary are locked to the area until they cross the outer threshold.
    /// </summary>
    public class CardArea : MonoBehaviour
    {
        [Header("Area Settings")]
        [Tooltip("The unit space to use for size measurements")]
        [SerializeField] private UnitSpace m_UnitSpace = UnitSpace.Millimeters;

        [Tooltip("The size of the inner boundary (white area)")]
        [SerializeField] private Vector2 m_InnerSize = new Vector2(100f, 100f);

        [Tooltip("The threshold distance beyond the inner boundary (yellow area width)")]
        [SerializeField] private float m_ThresholdDistance = 20f;

        [Tooltip("Color for the inner boundary visualization")]
        [SerializeField] private Color m_InnerColor = Color.white;

        [Tooltip("Color for the outer boundary visualization")]
        [SerializeField] private Color m_OuterColor = Color.yellow;

        [<PERSON>lt<PERSON>("Color for the border")]
        [SerializeField] private Color m_BorderColor = Color.black;

        [Tooltip("Width of the border")]
        [SerializeField] private float m_BorderWidth = 0.02f;

        [Tooltip("Whether to show the area visualization")]
        [SerializeField] private bool m_ShowVisualization = true;

        [Header("Card Behavior")]
        [Tooltip("Whether to center cards in the area when dropped")]
        [SerializeField] private bool m_CenterCardsOnDrop = true;

        // List of cards that are associated with this area
        protected HashSet<GameObject> m_AssociatedCards = new HashSet<GameObject>();

        // Cached values
        private Vector2 m_OuterSize;
        private Bounds m_InnerBounds;
        private Bounds m_OuterBounds;
        private GameObject m_VisualizationObject;

        // Cached world-space values (converted from the selected unit space)
        private Vector2 m_InnerSizeWorld;
        private float m_ThresholdDistanceWorld;

        protected void Awake()
        {
            UpdateBounds();
        }

        protected void OnEnable()
        {
            if (m_ShowVisualization)
            {
                CreateVisualization();
            }

            if (Application.isPlaying && TableManager.Instance != null)
            {
                CardAreaSystem areaSystem = TableManager.Instance.GetSystem<CardAreaSystem>();
                if (areaSystem != null)
                {
                    areaSystem.RegisterCardArea(this);
                }
                else
                {
                    Debug.LogWarning($"CardArea ({name}): Could not find CardAreaSystem to register with.", this);
                }
            }
        }

        protected void OnDisable()
        {
            DestroyVisualization();

            if (Application.isPlaying && TableManager.Instance != null) // Check Instance still exists (order of destruction)
            {
                CardAreaSystem areaSystem = TableManager.Instance.GetSystem<CardAreaSystem>();
                if (areaSystem != null)
                {
                    areaSystem.UnregisterCardArea(this);
                }
                // No warning if system is null here, as it might be shutting down
            }
        }

        protected void OnValidate()
        {
            UpdateBounds();

            if (Application.isPlaying && m_ShowVisualization)
            {
                DestroyVisualization();
                CreateVisualization();
            }
        }

        protected void OnDrawGizmos()
        {
            // Update bounds to ensure we're using the latest values
            UpdateBounds();

            // Draw outer boundary (yellow area)
            Gizmos.color = new Color(m_OuterColor.r, m_OuterColor.g, m_OuterColor.b, 0.3f);
            Gizmos.DrawCube(transform.position, new Vector3(m_OuterSize.x, m_OuterSize.y, 0.01f));

            // Draw inner boundary (white area)
            Gizmos.color = new Color(m_InnerColor.r, m_InnerColor.g, m_InnerColor.b, 0.5f);
            Gizmos.DrawCube(transform.position, new Vector3(m_InnerSizeWorld.x, m_InnerSizeWorld.y, 0.02f));

            // Draw inner boundary border (black)
            Gizmos.color = m_BorderColor;
            Gizmos.DrawWireCube(transform.position, new Vector3(m_InnerSizeWorld.x, m_InnerSizeWorld.y, 0.02f));
        }

        protected void OnDrawGizmosSelected()
        {
            // Update bounds to ensure we're using the latest values
            UpdateBounds();

            // Draw outer boundary with higher alpha when selected
            Gizmos.color = new Color(m_OuterColor.r, m_OuterColor.g, m_OuterColor.b, 0.5f);
            Gizmos.DrawCube(transform.position, new Vector3(m_OuterSize.x, m_OuterSize.y, 0.01f));

            // Draw inner boundary with higher alpha when selected
            Gizmos.color = new Color(m_InnerColor.r, m_InnerColor.g, m_InnerColor.b, 0.7f);
            Gizmos.DrawCube(transform.position, new Vector3(m_InnerSizeWorld.x, m_InnerSizeWorld.y, 0.02f));

            // Draw inner boundary border with higher alpha when selected
            Gizmos.color = m_BorderColor;
            Gizmos.DrawWireCube(transform.position, new Vector3(m_InnerSizeWorld.x, m_InnerSizeWorld.y, 0.02f));

            // Draw labels for inner and outer boundaries
            Vector3 innerLabelPos = transform.position + new Vector3(0, 0, -0.05f);
            Vector3 outerLabelPos = transform.position + new Vector3(0, m_OuterSize.y / 2 - 0.1f, -0.05f);

#if UNITY_EDITOR
            UnityEditor.Handles.Label(innerLabelPos, "Inner Boundary");
            UnityEditor.Handles.Label(outerLabelPos, "Threshold");
#endif
        }

        /// <summary>
        /// Updates the inner and outer bounds based on current settings
        /// </summary>
        private void UpdateBounds()
        {
            // Convert from the selected unit space to world units (meters)
            ConvertToWorldUnits();

            // Calculate outer size
            m_OuterSize = m_InnerSizeWorld + new Vector2(m_ThresholdDistanceWorld * 2, m_ThresholdDistanceWorld * 2);

            // Update bounds
            m_InnerBounds = new Bounds(transform.position, new Vector3(m_InnerSizeWorld.x, m_InnerSizeWorld.y, 0.1f));
            m_OuterBounds = new Bounds(transform.position, new Vector3(m_OuterSize.x, m_OuterSize.y, 0.1f));
        }

        /// <summary>
        /// Converts the size values from the selected unit space to world units (meters)
        /// </summary>
        private void ConvertToWorldUnits()
        {
            // First convert to millimeters (internal units)
            Vector2 innerSizeMm = UnitConverter.ToMillimeters(m_InnerSize, m_UnitSpace);
            float thresholdDistanceMm = UnitConverter.ToMillimeters(m_ThresholdDistance, m_UnitSpace);

            // Then convert from millimeters to meters (Unity world units)
            m_InnerSizeWorld = new Vector2(
                UnitConverter.MmToMeters(innerSizeMm.x),
                UnitConverter.MmToMeters(innerSizeMm.y)
            );
            m_ThresholdDistanceWorld = UnitConverter.MmToMeters(thresholdDistanceMm);
        }

        /// <summary>
        /// Creates a visual representation of the card area
        /// </summary>
        private void CreateVisualization()
        {
            DestroyVisualization();

            m_VisualizationObject = new GameObject("CardAreaVisualization");
            m_VisualizationObject.transform.SetParent(transform, false);
            m_VisualizationObject.transform.localPosition = Vector3.zero;

            // Create a mesh for the visualization
            MeshFilter meshFilter = m_VisualizationObject.AddComponent<MeshFilter>();
            MeshRenderer meshRenderer = m_VisualizationObject.AddComponent<MeshRenderer>();

            // Create a material for the visualization
            Material material = new Material(Shader.Find("Universal Render Pipeline/Unlit"));
            material.enableInstancing = true;
            meshRenderer.material = material;

            // Create the mesh
            Mesh mesh = new Mesh();

            // Create vertices for outer area (yellow)
            Vector3[] vertices = new Vector3[8];

            // Outer rectangle (clockwise from bottom-left)
            vertices[0] = new Vector3(-m_OuterSize.x / 2, -m_OuterSize.y / 2, 0.01f);
            vertices[1] = new Vector3(m_OuterSize.x / 2, -m_OuterSize.y / 2, 0.01f);
            vertices[2] = new Vector3(m_OuterSize.x / 2, m_OuterSize.y / 2, 0.01f);
            vertices[3] = new Vector3(-m_OuterSize.x / 2, m_OuterSize.y / 2, 0.01f);

            // Inner rectangle (counter-clockwise from bottom-left)
            vertices[4] = new Vector3(-m_InnerSizeWorld.x / 2, -m_InnerSizeWorld.y / 2, 0.01f);
            vertices[5] = new Vector3(-m_InnerSizeWorld.x / 2, m_InnerSizeWorld.y / 2, 0.01f);
            vertices[6] = new Vector3(m_InnerSizeWorld.x / 2, m_InnerSizeWorld.y / 2, 0.01f);
            vertices[7] = new Vector3(m_InnerSizeWorld.x / 2, -m_InnerSizeWorld.y / 2, 0.01f);

            // Create triangles
            int[] triangles = new int[24];

            // Outer area (yellow)
            // Bottom edge
            triangles[0] = 0;
            triangles[1] = 4;
            triangles[2] = 1;
            triangles[3] = 1;
            triangles[4] = 4;
            triangles[5] = 7;

            // Right edge
            triangles[6] = 1;
            triangles[7] = 7;
            triangles[8] = 2;
            triangles[9] = 2;
            triangles[10] = 7;
            triangles[11] = 6;

            // Top edge
            triangles[12] = 2;
            triangles[13] = 6;
            triangles[14] = 3;
            triangles[15] = 3;
            triangles[16] = 6;
            triangles[17] = 5;

            // Left edge
            triangles[18] = 3;
            triangles[19] = 5;
            triangles[20] = 0;
            triangles[21] = 0;
            triangles[22] = 5;
            triangles[23] = 4;

            // Set colors
            Color[] colors = new Color[8];

            // Outer area (yellow)
            for (int i = 0; i < 4; i++)
            {
                colors[i] = m_OuterColor;
            }

            // Inner area (white)
            for (int i = 4; i < 8; i++)
            {
                colors[i] = m_InnerColor;
            }

            // Assign to mesh
            mesh.vertices = vertices;
            mesh.triangles = triangles;
            mesh.colors = colors;

            mesh.RecalculateNormals();
            mesh.RecalculateBounds();

            meshFilter.mesh = mesh;

            // Create border for inner area
            CreateBorder();
        }

        /// <summary>
        /// Creates a border around the inner area
        /// </summary>
        private void CreateBorder()
        {
            if (m_VisualizationObject == null) return;

            GameObject borderObject = new GameObject("Border");
            borderObject.transform.SetParent(m_VisualizationObject.transform, false);
            borderObject.transform.localPosition = Vector3.zero;

            LineRenderer lineRenderer = borderObject.AddComponent<LineRenderer>();
            lineRenderer.useWorldSpace = false;
            lineRenderer.startWidth = m_BorderWidth;
            lineRenderer.endWidth = m_BorderWidth;
            lineRenderer.startColor = m_BorderColor;
            lineRenderer.endColor = m_BorderColor;
            lineRenderer.material = new Material(Shader.Find("Universal Render Pipeline/Unlit"));
            lineRenderer.positionCount = 5;

            // Set positions for the border (inner rectangle)
            lineRenderer.SetPosition(0, new Vector3(-m_InnerSizeWorld.x / 2, -m_InnerSizeWorld.y / 2, 0.02f));
            lineRenderer.SetPosition(1, new Vector3(m_InnerSizeWorld.x / 2, -m_InnerSizeWorld.y / 2, 0.02f));
            lineRenderer.SetPosition(2, new Vector3(m_InnerSizeWorld.x / 2, m_InnerSizeWorld.y / 2, 0.02f));
            lineRenderer.SetPosition(3, new Vector3(-m_InnerSizeWorld.x / 2, m_InnerSizeWorld.y / 2, 0.02f));
            lineRenderer.SetPosition(4, new Vector3(-m_InnerSizeWorld.x / 2, -m_InnerSizeWorld.y / 2, 0.02f));
        }

        /// <summary>
        /// Destroys the visualization object
        /// </summary>
        private void DestroyVisualization()
        {
            if (m_VisualizationObject != null)
            {
                if (Application.isPlaying)
                {
                    Destroy(m_VisualizationObject);
                }
                else
                {
                    DestroyImmediate(m_VisualizationObject);
                }
                m_VisualizationObject = null;
            }
        }

        /// <summary>
        /// Checks if a position is within the inner bounds of the area
        /// </summary>
        public bool IsWithinInnerBounds(Vector3 position)
        {
            // Only check X and Y coordinates
            Vector2 position2D = new Vector2(position.x, position.y);
            Vector2 center2D = new Vector2(transform.position.x, transform.position.y);

            return Mathf.Abs(position2D.x - center2D.x) <= m_InnerSizeWorld.x / 2 &&
                   Mathf.Abs(position2D.y - center2D.y) <= m_InnerSizeWorld.y / 2;
        }

        /// <summary>
        /// Checks if a position is within the outer bounds of the area
        /// </summary>
        public bool IsWithinOuterBounds(Vector3 position)
        {
            // Only check X and Y coordinates
            Vector2 position2D = new Vector2(position.x, position.y);
            Vector2 center2D = new Vector2(transform.position.x, transform.position.y);

            return Mathf.Abs(position2D.x - center2D.x) <= m_OuterSize.x / 2 &&
                   Mathf.Abs(position2D.y - center2D.y) <= m_OuterSize.y / 2;
        }

        /// <summary>
        /// Constrains a position to be within the inner bounds of the area
        /// </summary>
        public Vector3 ConstrainToInnerBounds(Vector3 position)
        {
            Vector3 constrainedPosition = position;
            Vector2 center2D = new Vector2(transform.position.x, transform.position.y);

            // Constrain X coordinate
            float halfWidth = m_InnerSizeWorld.x / 2;
            constrainedPosition.x = Mathf.Clamp(constrainedPosition.x,
                center2D.x - halfWidth,
                center2D.x + halfWidth);

            // Constrain Y coordinate
            float halfHeight = m_InnerSizeWorld.y / 2;
            constrainedPosition.y = Mathf.Clamp(constrainedPosition.y,
                center2D.y - halfHeight,
                center2D.y + halfHeight);

            return constrainedPosition;
        }

        /// <summary>
        /// Gets whether cards should be centered in this area when dropped
        /// </summary>
        public bool CenterCardsOnDrop => m_CenterCardsOnDrop;

        /// <summary>
        /// Adds a card to this area and associates it with the area
        /// </summary>
        /// <param name="card">The card to add</param>
        /// <param name="animationSystem">Optional animation system to animate the card (can be null)</param>
        /// <param name="animationDuration">Duration of the animation if animation system is provided</param>
        /// <returns>True if the card was added successfully, false otherwise</returns>
        public virtual bool AddCard(GameObject card, AnimationSystem animationSystem = null, float animationDuration = 0.3f)
        {
            if (card != null)
            {
                if (m_AssociatedCards.Contains(card))
                {
                    Debug.LogWarning($"CardArea: Card {card.name} is already associated with area {name}.", this);
                    return true; // Already associated, treat as success
                }
                m_AssociatedCards.Add(card);
                Debug.Log($"CardArea: Card {card.name} added to area {name}", this);
                // Centering logic is now implicitly handled by CardInteractionSystem via HandleCardDropped result
                return true;
            }
            return false;
        }

        /// <summary>
        /// Removes a card from this area and disassociates it
        /// </summary>
        /// <param name="card">The card to remove</param>
        /// <returns>True if the card was removed successfully, false otherwise</returns>
        public virtual bool RemoveCard(GameObject card)
        {
            if (card != null && m_AssociatedCards.Contains(card))
            {
                m_AssociatedCards.Remove(card);
                Debug.Log($"CardArea: Card {card.name} removed from area {name}", this);
                return true;
            }
            // If card is null or not found, log a warning but don't treat as an error for the caller
            if (card != null) 
            {
                Debug.LogWarning($"CardArea: Card {card.name} not found in area {name} for removal.", this);
            }
            return false;
        }

        /// <summary>
        /// Checks if a card is associated with this area
        /// </summary>
        /// <param name="card">The card to check</param>
        /// <returns>True if the card is associated with this area, false otherwise</returns>
        public virtual bool IsCardAssociated(GameObject card)
        {
            return card != null && m_AssociatedCards.Contains(card);
        }

        // Legacy methods for backward compatibility - these will be deprecated

        /// <summary>
        /// Associates a card with this area (Legacy method - use AddCard instead)
        /// </summary>
        /// <param name="card">The card to associate</param>
        public virtual void AssociateCard(GameObject card)
        {
            AddCard(card);
        }

        /// <summary>
        /// Disassociates a card from this area (Legacy method - use RemoveCard instead)
        /// </summary>
        /// <param name="card">The card to disassociate</param>
        public virtual void DisassociateCard(GameObject card)
        {
            RemoveCard(card);
        }

        /// <summary>
        /// Virtual method that handles a card being dropped on this area.
        /// Override this in derived classes to customize drop behavior.
        /// </summary>
        /// <param name="card">The GameObject of the card being dropped</param>
        /// <param name="position">The position where the card was dropped</param>
        /// <returns>A CardDropResult containing the position and rotation where the card should be placed</returns>
        public virtual CardDropResult HandleCardDropped(GameObject card, Vector3 position)
        {
            // CardArea determines the target X, Y, and Z.
            // CardInteractionSystem will animate to this point and decide on tilt reset based on UseSpecificRotation.
            
            float targetZ = transform.position.z; // Use the CardArea's own Z level as the surface.
            Vector3 targetPosition;

            if (m_CenterCardsOnDrop)
            {
                // Center the card in the area (X,Y) at the area's Z level.
                targetPosition = new Vector3(transform.position.x, transform.position.y, targetZ);
                Debug.Log($"CardArea ({name}): Handling drop for {card.name}. Centering. TargetPos: {targetPosition}", this);
            }
            else
            {
                // Drop at current XY, at the area's Z level.
                targetPosition = new Vector3(position.x, position.y, targetZ);
                Debug.Log($"CardArea ({name}): Handling drop for {card.name}. No centering. TargetPos: {targetPosition}", this);
            }
            // For a base CardArea, we don't specify a unique rotation, so CIS should reset tilt.
            // Thus, use the constructor that defaults UseSpecificRotation to false.
            return new CardDropResult(targetPosition, true); 
        }
    }
}

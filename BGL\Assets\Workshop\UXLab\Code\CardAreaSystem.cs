using UnityEngine;
using System.Collections.Generic;

namespace Workshop.UXLab
{
    /// <summary>
    /// Struct to hold the result of a card drop operation, including position and rotation
    /// </summary>
    public struct CardDropResult
    {
        public Vector3 Position;
        public Quaternion Rotation;
        public bool ShouldAnimateDrop; // True if CardInteractionSystem should animate it to Position/Rotation.
        public bool UseSpecificRotation; // If true, use Rotation field. If false, CardInteractionSystem decides (e.g., reset tilt).

        // Constructor for specific position and rotation
        public CardDropResult(Vector3 position, Quaternion rotation, bool shouldAnimateDrop = true)
        {
            Position = position;
            Rotation = rotation;
            ShouldAnimateDrop = shouldAnimateDrop;
            UseSpecificRotation = true;
        }

        // Constructor for area deciding position, CardInteractionSystem deciding rotation (e.g., reset tilt)
        public CardDropResult(Vector3 position, bool shouldAnimateDrop = true)
        {
            Position = position;
            Rotation = Quaternion.identity; // Placeholder, won't be used by CardInteractionSystem if UseSpecificRotation is false
            ShouldAnimateDrop = shouldAnimateDrop;
            UseSpecificRotation = false;
        }

        // Static factory for when no specific area action is taken
        public static CardDropResult NoAction()
        {
            return new CardDropResult(Vector3.zero, Quaternion.identity, false) { UseSpecificRotation = false };
        }
    }

    [CreateAssetMenu(fileName = "CardAreaSystem", menuName = "Workshop/UXLab/Systems/CardAreaSystem")]
    public class CardAreaSystem : TableSystem
    {
        [Header("Card Area Settings")]
        [SerializeField] private GameObject m_CardAreaPrefab;
        [SerializeField] private bool m_EnableCardAreas = true;

        [Header("Debug Visualization")]
        [Tooltip("Whether to show debug visualization for constrained cards")]
        [SerializeField] private bool m_ShowDebugVisualization = false;
        [Tooltip("Color to use for highlighting constrained cards")]
        [SerializeField] private Color m_ConstraintHighlightColor = new Color(1f, 0.5f, 0f, 0.3f); // Orange

        [Header("Card Behavior")]
        [Tooltip("Whether to center cards in the area when dropped")]
        [SerializeField] private bool m_CenterCardsOnDrop = true;
        [Tooltip("Animation duration for centering cards")]
        [SerializeField] private float m_CenteringAnimationDuration = 0.3f;
        [Tooltip("Delay in seconds before releasing a card from an area after crossing the threshold")]
        [SerializeField] private float m_ReleaseDelay = 0.2f;

        // List of all card areas in the scene
        private List<CardArea> m_CardAreas = new List<CardArea>();

        // Tracking which cards are currently constrained to which areas
        private Dictionary<GameObject, CardArea> m_CardConstraints = new Dictionary<GameObject, CardArea>();

        // Tracking cards that are outside their threshold but haven't been released yet
        private Dictionary<GameObject, float> m_CardsOutsideThreshold = new Dictionary<GameObject, float>();

        // Reference to the card interaction system
        private CardInteractionSystem m_CardInteractionSystem;

        public override void Init(TableManager tableManager)
        {
            base.Init(tableManager);

            // Get reference to the card interaction system
            m_CardInteractionSystem = m_TableManager.GetSystem<CardInteractionSystem>();
            if (m_CardInteractionSystem == null)
            {
                Debug.LogError("CardAreaSystem: CardInteractionSystem not found. Card areas will not function properly.", this);
            }
            else
            {
                // Subscribe to card interaction events
                // Note: Card selection/deselection is now handled by the CardInteractionSystem
                m_CardInteractionSystem.OnCardDragStarted += HandleCardDragStarted;
                m_CardInteractionSystem.OnCardDragEnded += HandleCardDragEnded;
                m_CardInteractionSystem.OnCardMoved += HandleCardMoved;
            }

            // Subscribe to the update event to process delayed releases
            m_TableManager.OnUpdate += ProcessDelayedReleases;

            // Find all existing card areas in the scene
            FindCardAreas();
        }

        /// <summary>
        /// Processes cards that are outside their threshold and should be released after the delay
        /// </summary>
        private void ProcessDelayedReleases()
        {
            if (!m_EnableCardAreas) return;

            // Log the number of cards being tracked (for debugging)
            if (m_CardsOutsideThreshold.Count > 0)
            {
                Debug.Log($"CardAreaSystem: Processing {m_CardsOutsideThreshold.Count} cards outside threshold", this);
            }

            if (m_CardsOutsideThreshold.Count == 0) return;

            // Create a list to store cards that should be released
            List<GameObject> cardsToRelease = new List<GameObject>();

            // Check each card that's outside its threshold
            foreach (var kvp in m_CardsOutsideThreshold)
            {
                GameObject card = kvp.Key;
                float timeOutside = Time.time - kvp.Value;

                Debug.Log($"CardAreaSystem: Card {card.name} has been outside threshold for {timeOutside:F2}s (delay: {m_ReleaseDelay:F2}s)", this);

                // If the card has been outside the threshold for long enough, add it to the release list
                if (timeOutside >= m_ReleaseDelay)
                {
                    cardsToRelease.Add(card);
                    Debug.Log($"CardAreaSystem: Card {card.name} will be released (time outside: {timeOutside:F2}s)", this);
                }
            }

            // Release each card in the list
            foreach (GameObject card in cardsToRelease)
            {
                if (m_CardConstraints.TryGetValue(card, out CardArea area))
                {
                    float timeOutside = Time.time - m_CardsOutsideThreshold[card];

                    // Get the current mouse position in world space
                    Vector3 mousePosition = GetMouseWorldPosition();

                    // Check if the mouse is still outside the outer bounds
                    if (!area.IsWithinOuterBounds(mousePosition))
                    {
                        // Remove the card from both dictionaries
                        // Disassociate the card from the area
                        area.DisassociateCard(card);

                        m_CardConstraints.Remove(card);
                        m_CardsOutsideThreshold.Remove(card);

                        Debug.Log($"CardAreaSystem: Card {card.name} has left area {area.name} after {timeOutside:F2}s (processed in update)", this);

                        // Remove debug visualization if enabled
                        if (m_ShowDebugVisualization)
                        {
                            RemoveDebugVisualization(card);
                        }
                    }
                    else
                    {
                        // Mouse has moved back within the outer bounds, remove from outside threshold tracking
                        m_CardsOutsideThreshold.Remove(card);
                        Debug.Log($"CardAreaSystem: Mouse has returned within the threshold of area {area.name}", this);
                    }
                }
                else
                {
                    // If the card is not constrained to an area, just remove it from the outside threshold tracking
                    m_CardsOutsideThreshold.Remove(card);
                }
            }
        }

        public override void Shutdown()
        {
            if (!m_IsInitialized)
                return;

            // Unsubscribe from card interaction events
            if (m_CardInteractionSystem != null)
            {
                m_CardInteractionSystem.OnCardDragStarted -= HandleCardDragStarted;
                m_CardInteractionSystem.OnCardDragEnded -= HandleCardDragEnded;
                m_CardInteractionSystem.OnCardMoved -= HandleCardMoved;
            }

            // Unsubscribe from the update event
            if (m_TableManager != null)
            {
                m_TableManager.OnUpdate -= ProcessDelayedReleases;
            }

            // Clear lists and dictionaries
            m_CardAreas.Clear();
            m_CardConstraints.Clear();
            m_CardsOutsideThreshold.Clear();

            base.Shutdown();
        }

        /// <summary>
        /// Finds all CardArea components in the scene and adds them to the list
        /// </summary>
        private void FindCardAreas()
        {
            m_CardAreas.Clear();
            CardArea[] areas = GameObject.FindObjectsOfType<CardArea>();

            foreach (CardArea area in areas)
            {
                m_CardAreas.Add(area);
                Debug.Log($"CardAreaSystem: Found card area at {area.transform.position}", this);
            }

            Debug.Log($"CardAreaSystem: Found {m_CardAreas.Count} card areas in the scene", this);
        }

        /// <summary>
        /// Registers a CardArea with the system. Typically called from CardArea.OnEnable.
        /// </summary>
        /// <param name="area">The CardArea to register.</param>
        public void RegisterCardArea(CardArea area)
        {
            if (area != null && !m_CardAreas.Contains(area))
            {
                m_CardAreas.Add(area);
                Debug.Log($"CardAreaSystem: Registered card area {area.name}", this);
            }
            else if (area != null && m_CardAreas.Contains(area))
            {
                Debug.LogWarning($"CardAreaSystem: CardArea {area.name} is already registered.", this);
            }
        }

        /// <summary>
        /// Unregisters a CardArea from the system. Typically called from CardArea.OnDisable.
        /// </summary>
        /// <param name="area">The CardArea to unregister.</param>
        public void UnregisterCardArea(CardArea area)
        {
            if (area != null && m_CardAreas.Contains(area))
            {
                // Before removing the area, ensure any cards constrained to it are handled.
                // This might involve releasing constraints or other cleanup.
                List<GameObject> cardsToReleaseFromArea = new List<GameObject>();
                foreach (var kvp in m_CardConstraints)
                {
                    if (kvp.Value == area)
                    {
                        cardsToReleaseFromArea.Add(kvp.Key);
                    }
                }

                foreach (GameObject card in cardsToReleaseFromArea)
                {
                    m_CardConstraints.Remove(card);
                    if (m_CardsOutsideThreshold.ContainsKey(card))
                    {
                        m_CardsOutsideThreshold.Remove(card);
                    }
                    if (m_ShowDebugVisualization)
                    {
                        RemoveDebugVisualization(card);
                    }
                    // Note: We don't call area.DisassociateCard(card) here as the area itself is being removed
                    // or disabled. The association will naturally break.
                    Debug.Log($"CardAreaSystem: Released card {card.name} from unregistering area {area.name}", this);
                }

                m_CardAreas.Remove(area);
                Debug.Log($"CardAreaSystem: Unregistered card area {area.name}", this);
            }
            else if (area != null)
            {
                 Debug.LogWarning($"CardAreaSystem: CardArea {area.name} was not found for unregistration.", this);
            }
        }

        /// <summary>
        /// Creates a new card area at the specified position
        /// </summary>
        public CardArea CreateCardArea(Vector3 position)
        {
            if (m_CardAreaPrefab == null)
            {
                Debug.LogError("CardAreaSystem: Cannot create card area - prefab is not assigned", this);
                return null;
            }

            GameObject areaObject = Instantiate(m_CardAreaPrefab, position, Quaternion.identity);
            areaObject.name = $"CardArea_{m_CardAreas.Count}";

            CardArea area = areaObject.GetComponent<CardArea>();
            if (area == null)
            {
                Debug.LogError("CardAreaSystem: Card area prefab does not have a CardArea component", this);
                Destroy(areaObject);
                return null;
            }

            // Set default properties based on system settings
            // Note: We can't directly set m_CenterCardsOnDrop since it's private
            // The property will be set through the inspector or serialization

            m_CardAreas.Add(area);
            Debug.Log($"CardAreaSystem: Created new card area at {position}", this);

            return area;
        }

        /// <summary>
        /// Removes a card area from the scene
        /// </summary>
        public void RemoveCardArea(CardArea area)
        {
            if (area == null) return;

            // Remove any constraints using this area
            List<GameObject> cardsToRemove = new List<GameObject>();
            foreach (var kvp in m_CardConstraints)
            {
                if (kvp.Value == area)
                {
                    cardsToRemove.Add(kvp.Key);
                }
            }

            foreach (GameObject card in cardsToRemove)
            {
                m_CardConstraints.Remove(card);
            }

            m_CardAreas.Remove(area);
            Destroy(area.gameObject);

            Debug.Log($"CardAreaSystem: Removed card area", this);
        }

        /// <summary>
        /// Checks if a position is within any card area and returns the area
        /// </summary>
        public CardArea GetCardAreaAtPosition(Vector3 position)
        {
            if (!m_EnableCardAreas) return null;

            foreach (CardArea area in m_CardAreas)
            {
                if (area.IsWithinInnerBounds(position))
                {
                    return area;
                }
            }

            return null;
        }

        /// <summary>
        /// Checks if a card is currently in any card area
        /// </summary>
        /// <param name="card">The card to check</param>
        /// <returns>True if the card is in an area, false otherwise</returns>
        public bool IsCardInArea(GameObject card)
        {
            if (!m_EnableCardAreas || card == null) return false;

            // Check if the card is in the constraints dictionary
            return m_CardConstraints.ContainsKey(card);
        }

        /// <summary>
        /// Gets the current mouse position in world space
        /// </summary>
        private Vector3 GetMouseWorldPosition()
        {
            if (m_TableManager == null || m_TableManager.CameraController == null || m_TableManager.CameraController.Camera == null)
                return Vector3.zero;

            // Get the camera from the camera controller
            Camera camera = m_TableManager.CameraController.Camera;

            // Get the mouse position in screen space
            Vector3 mousePos = Input.mousePosition;

            // Create a ray from the camera through the mouse position
            Ray ray = camera.ScreenPointToRay(mousePos);

            // Create a plane at the table height
            Plane tablePlane = new Plane(Vector3.forward, Vector3.zero);

            // Check if the ray hits the plane
            if (tablePlane.Raycast(ray, out float distance))
            {
                // Return the point where the ray hits the plane
                return ray.GetPoint(distance);
            }

            return Vector3.zero;
        }

        /// <summary>
        /// Constrains a card to an area if it's within the inner bounds
        /// </summary>
        public void CheckAndConstrainCard(GameObject card, Vector3 position)
        {
            if (!m_EnableCardAreas || card == null) return;

            // If the card is already constrained to an area
            if (m_CardConstraints.TryGetValue(card, out CardArea constrainingArea))
            {
                // Get the current mouse position in world space
                Vector3 mousePosition = GetMouseWorldPosition();

                // Check if the mouse has moved outside the outer bounds
                if (!constrainingArea.IsWithinOuterBounds(mousePosition))
                {
                    // Mouse has crossed the threshold, but we'll add a delay before releasing it
                    if (!m_CardsOutsideThreshold.ContainsKey(card))
                    {
                        // First time crossing the threshold, record the time
                        m_CardsOutsideThreshold[card] = Time.time;
                        Debug.Log($"CardAreaSystem: Mouse has crossed the threshold of area {constrainingArea.name}", this);
                    }

                    // Note: We don't release the card here anymore. That's handled in ProcessDelayedReleases
                    // which is called every frame from the update loop.
                }
                else
                {
                    // Mouse is back within the outer bounds, remove from the outside threshold tracking
                    if (m_CardsOutsideThreshold.ContainsKey(card))
                    {
                        m_CardsOutsideThreshold.Remove(card);
                        Debug.Log($"CardAreaSystem: Mouse has returned within the threshold of area {constrainingArea.name}", this);
                    }
                }
            }
            else
            {
                // Check if the card is within any area's inner bounds
                foreach (CardArea area in m_CardAreas)
                {
                    if (area.IsWithinInnerBounds(position))
                    {
                        // Card has entered an area, add the constraint
                        m_CardConstraints[card] = area;

                        // Note: We don't associate the card with the area here anymore
                        // Association only happens when the card is dropped (HandleCardDragEnded)
                        // or deselected (HandleCardDeselected)

                        // Make sure it's not in the outside threshold tracking
                        if (m_CardsOutsideThreshold.ContainsKey(card))
                        {
                            m_CardsOutsideThreshold.Remove(card);
                        }

                        Debug.Log($"CardAreaSystem: Card {card.name} has entered area {area.name}", this);

                        // Add debug visualization if enabled
                        if (m_ShowDebugVisualization)
                        {
                            AddDebugVisualization(card);
                        }
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// Adds debug visualization to a card to show it's constrained
        /// </summary>
        private void AddDebugVisualization(GameObject card)
        {
            if (card == null) return;

            // Use HighlightEffect for visualization if available
            HighlightPlus.HighlightEffect highlightEffect = card.GetComponent<HighlightPlus.HighlightEffect>();
            if (highlightEffect == null)
            {
                highlightEffect = card.AddComponent<HighlightPlus.HighlightEffect>();
            }

            // Set up the highlight effect
            highlightEffect.SetHighlighted(true);
            highlightEffect.outlineColor = m_ConstraintHighlightColor;
            highlightEffect.outlineWidth = 2.0f;
            highlightEffect.overlayColor = m_ConstraintHighlightColor;
            highlightEffect.overlayBlending = 0.5f;
        }

        /// <summary>
        /// Removes debug visualization from a card
        /// </summary>
        private void RemoveDebugVisualization(GameObject card)
        {
            if (card == null) return;

            // Get the HighlightEffect component if it exists
            HighlightPlus.HighlightEffect highlightEffect = card.GetComponent<HighlightPlus.HighlightEffect>();
            if (highlightEffect != null)
            {
                // Only disable the highlight if it's not being used by another system
                // Check if the card is selected in the CardInteractionSystem
                if (m_CardInteractionSystem != null && m_CardInteractionSystem.SelectedCard == card)
                {
                    // Card is selected, don't remove the highlight
                    // Just change the color back to the selection color
                    highlightEffect.outlineColor = new Color(0.2f, 0.8f, 0.2f, 0.5f); // Default selection color
                    highlightEffect.overlayColor = new Color(0.2f, 0.8f, 0.2f, 0.5f);
                }
                else
                {
                    // Card is not selected, remove the highlight
                    highlightEffect.SetHighlighted(false);
                }
            }
        }

        /// <summary>
        /// Constrains a position to be within the card's constraining area, if any
        /// </summary>
        public Vector3 ConstrainPositionToArea(GameObject card, Vector3 position)
        {
            if (!m_EnableCardAreas || card == null) return position;

            // If the card is constrained to an area
            if (m_CardConstraints.TryGetValue(card, out CardArea constrainingArea))
            {
                // Check if the card is outside the inner bounds
                if (!constrainingArea.IsWithinInnerBounds(position))
                {
                    // If the card is outside the inner bounds, always constrain it to the inner bounds
                    // regardless of whether it's in the threshold area or outside it
                    // The release will be handled by the ProcessDelayedReleases method after the delay
                    return constrainingArea.ConstrainToInnerBounds(position);
                }
            }

            return position;
        }

        #region Event Handlers

        // HandleCardSelected and HandleCardDeselected have been removed
        // Card selection/deselection is now handled by the CardInteractionSystem

        private void HandleCardDragStarted(GameObject card)
        {
            if (!m_EnableCardAreas || card == null) return;

            // When a drag starts, if the card was constrained, it might be trying to leave.
            // CheckAndConstrainCard will handle the logic for putting it into m_CardsOutsideThreshold if it crosses the boundary.
            CheckAndConstrainCard(card, card.transform.position);
        }

        private void HandleCardDragEnded(GameObject card, Vector3 newPosition)
        {
            if (!m_EnableCardAreas || card == null) return;

            // CardInteractionSystem will call GetCardAreaAtPosition(newPosition) and then area.HandleCardDropped.
            // CIS will then call area.AddCard() if the drop is successful into an area.
            // This system primarily needs to know if a card *was* constrained and might now be released, or enters a new constraint.

            CardArea currentArea = GetCardAreaAtPosition(newPosition);
            bool wasConstrained = m_CardConstraints.TryGetValue(card, out CardArea oldArea);

            if (currentArea != null) 
            { 
                // Card ended drag in an area. CIS will handle adding it.
                // Ensure constraint is noted if it wasn't before.
                if (!wasConstrained || oldArea != currentArea)
                {
                    m_CardConstraints[card] = currentArea;
                    if (m_ShowDebugVisualization) AddDebugVisualization(card);
                    if (m_CardsOutsideThreshold.ContainsKey(card)) m_CardsOutsideThreshold.Remove(card);
                }
            }
            else if (wasConstrained)
            { 
                // Card ended drag OUTSIDE its previous constraining area and any other area.
                // It should be released from constraint immediately.
                oldArea.RemoveCard(card); // Disassociate from CardArea
                m_CardConstraints.Remove(card);
                if (m_CardsOutsideThreshold.ContainsKey(card)) m_CardsOutsideThreshold.Remove(card);
                if (m_ShowDebugVisualization) RemoveDebugVisualization(card);
                Debug.Log($"CardAreaSystem: Card {card.name} drag ended outside area {oldArea.name}, released constraint.", this);
            }
            // If not in any area and wasn't constrained, nothing to do here for CAS.
        }

        /// <summary>
        /// Gets the proper resting height for a card on the table
        /// </summary>
        private float GetProperCardRestingHeight(GameObject card)
        {
            // Try to get the original resting height from the CardInteractionSystem
            if (m_CardInteractionSystem != null && m_CardInteractionSystem.SelectedCard == card)
            {
                // If this is the currently selected card, use the selection start position's Z value
                return m_CardInteractionSystem.GetCardRestingHeight(card);
            }

            // Fallback: Use a small default height above the table
            // This is a reasonable default if we can't get the exact height
            return 0.01f; // 1cm above the table
        }

        private void HandleCardMoved(GameObject card, Vector3 newPosition)
        {
            if (!m_EnableCardAreas || card == null) return;

            // Check if the card is within any area and constrain if necessary
            CheckAndConstrainCard(card, newPosition);
        }

        #endregion
    }
}

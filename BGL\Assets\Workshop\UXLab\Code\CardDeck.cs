using UnityEngine;
using System.Collections.Generic;
using Workshop.UXLab.Data;
using System; // For System.Random
using System.Threading.Tasks; // Added for Task

namespace Workshop.UXLab
{
    /// <summary>
    /// Manages a collection of CardLayouts, visually stacking them with cumulative offsets.
    /// Also acts as a CardArea that cards can be dropped onto.
    /// </summary>
    public class CardDeck : CardArea
    {
        [Tooltip("The prefab to instantiate for new cards.")]
        [SerializeField] private GameObject m_CardPrefab;

        [Tooltip("Maximum number of cards allowed in the deck.")]
        [SerializeField] private int m_MaxCards = 52;

        [Tooltip("Multiplier applied to the card thickness to determine vertical spacing.")]
        [SerializeField] private float m_ThicknessSpacingMultiplier = 1.1f; // Add 10% gap

        [Header("Cumulative Stack Variation")]
        [Tooltip("Average horizontal offset step (in meters) per card relative to the previous one.")]
        [SerializeField] private Vector2 m_CumulativeOffsetStep = new Vector2(0.0001f, 0.0001f); // Small default step

        [Tooltip("Random noise added to the horizontal offset step (range +/- this value/2).")]
        [SerializeField] private float m_OffsetStepNoise = 0.0005f; // 0.5mm noise

        [Tooltip("Average rotation step (degrees) per card relative to the previous one.")]
        [SerializeField] private float m_CumulativeRotationStep = 0.1f; // Small default rotation step

        [Tooltip("Random noise added to the rotation step (range +/- this value/2).")]
        [SerializeField] private float m_RotationStepNoise = 0.5f; // 0.5 degrees noise

        [Header("Shuffle Animation")]
        [SerializeField] private float m_ShuffleExplodeDuration = 0.3f;
        [SerializeField] private AnimationCurve m_ShuffleExplodeCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        [SerializeField] private float m_ShuffleGatherDuration = 0.4f;
        [SerializeField] private AnimationCurve m_ShuffleGatherCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        [SerializeField] private Vector3 m_ShuffleExplodeExtents = new Vector3(0.15f, 0.15f, 0.05f); // Max X, Y, Z offset from deck center for explosion
        [SerializeField] private float m_ShuffleExplodeRotationRange = 25f; // Max random rotation (degrees) during explosion on each axis

        // Internal struct - simplified, no longer stores pre-calculated randomness
        private struct DeckCardInfo
        {
            public CardLayout CardInstance;
            // public Vector2 RandomOffset; // Removed
            // public float RandomRotation; // Removed
        }

        // Helper struct for storing initial transforms during shuffle animation
        private struct TransformData
        {
            public CardLayout Card;
            public Vector3 Position;
            public Quaternion Rotation;
            public TransformData(CardLayout card, Vector3 pos, Quaternion rot)
            {
                Card = card; Position = pos; Rotation = rot;
            }
        }

        private List<DeckCardInfo> m_Cards = new List<DeckCardInfo>();
        private CardSizeData m_ReferenceCardSize; // Store size from the first card added
        private System.Random m_Rng = new System.Random(); // Use System.Random for noise generation
        private bool m_IsDynamicallyCreated = false; // Flag to indicate if the deck was created at runtime

        /// <summary>
        /// Gets the current number of cards in the deck.
        /// </summary>
        public int CardCount => m_Cards.Count;

        // --- Unity Methods ---

        private void Awake()
        {
            // Call base.Awake() to ensure CardArea initialization
            base.Awake();

            // Initialize RNG if not already
            if (m_Rng == null)
            {
                m_Rng = new System.Random();
            }
        }

        private void OnEnable()
        {
            // Call base.OnEnable() to ensure CardArea visualization
            base.OnEnable();
        }

        private void OnDisable()
        {
            // Call base.OnDisable() to ensure CardArea cleanup
            base.OnDisable();
        }

        private void OnValidate()
        {
            // Call base.OnValidate() to ensure CardArea bounds are updated
            base.OnValidate();
        }

        private void OnDrawGizmos()
        {
            // Call base.OnDrawGizmos() to draw the CardArea visualization
            base.OnDrawGizmos();

            // Add any additional deck-specific gizmo drawing here if needed
        }

        private void OnDrawGizmosSelected()
        {
            // Call base.OnDrawGizmosSelected() to draw the CardArea visualization when selected
            base.OnDrawGizmosSelected();

            // Add any additional deck-specific gizmo drawing here if needed
        }

        private void Start()
        {
            // Optionally apply initial layout if cards were added in editor
            if (m_Cards.Count > 0)
            {
                 // Ensure reference size is set if cards exist on start
                 if (m_ReferenceCardSize == null && m_Cards.Count > 0 && m_Cards[0].CardInstance != null)
                 {
                     TrySetReferenceSize(m_Cards[0].CardInstance);
                 }
                 UpdateLayout();
            }
        }

        // --- Public Deck Operations ---

        /// <summary>
        /// Checks if a given CardLayout instance is part of this deck's internal list of cards.
        /// </summary>
        /// <param name="cardLayout">The CardLayout instance to check.</param>
        /// <returns>True if the card is in the deck, false otherwise.</returns>
        public bool IsCardInDeck(CardLayout cardLayout)
        {
            if (cardLayout == null) return false;
            return m_Cards.Exists(deckCardInfo => deckCardInfo.CardInstance == cardLayout);
        }

        /// <summary>
        /// Adds a pre-instantiated card to the top of the deck.
        /// </summary>
        /// <param name="cardInstance">The CardLayout instance to add.</param>
        /// <returns>True if the card was added successfully, false otherwise (e.g., deck full).</returns>
        public bool AddCard(CardLayout cardInstance)
        {
            if (cardInstance == null)
            {
                Debug.LogError("CardDeck: Cannot add a null card instance.", this);
                return false;
            }
            // Check if already associated, which might happen if CIS calls AddCard after HandleCardDropped
            if (base.IsCardAssociated(cardInstance.gameObject) && m_Cards.Exists(c => c.CardInstance == cardInstance))
            {
                Debug.LogWarning($"CardDeck: Card {cardInstance.name} is already in the deck and associated. Finalizing position.", this);
                // Card is already in m_Cards, ensure its transform is correct based on its position in the list.
                // This might involve finding its index and re-running PositionNewCard or part of UpdateLayout for it.
                // For now, we assume HandleCardDropped got it to the right spot, and CIS called AddCard.
                // We just need to ensure it's parented correctly if it wasn't.
                cardInstance.transform.SetParent(this.transform, true);
                UpdateLayout(); // Could be heavy, but ensures correctness if card was re-added
                return true;
            }

            if (m_Cards.Count >= m_MaxCards)
            {
                Debug.LogWarning("CardDeck: Deck is full. Cannot add more cards.", this);
                return false;
            }

            cardInstance.transform.SetParent(this.transform, true);

            bool isFirstCard = m_Cards.Count == 0;
            if (isFirstCard)
            {
                if (!TrySetReferenceSize(cardInstance))
                {
                    cardInstance.transform.SetParent(null, true);
                    Debug.LogError("CardDeck: Could not determine reference card size. Card not added.", this);
                    return false;
                }
            }

            // Add to list BEFORE positioning, so PositionNewCard knows the correct count for previousTopCard
            DeckCardInfo cardInfo = new DeckCardInfo { CardInstance = cardInstance };
            m_Cards.Add(cardInfo);

            PositionNewCard(cardInstance, isFirstCard); // This sets localPosition and localRotation

            cardInstance.transform.SetSiblingIndex(m_Cards.Count - 1);

            base.AddCard(cardInstance.gameObject); // Associate with CardArea part
            Debug.Log($"CardDeck: Added card {cardInstance.name} to deck. Total: {m_Cards.Count}", this);
            return true;
        }

        /// <summary>
        /// Adds a card GameObject to the deck. Overrides the base CardArea method.
        /// </summary>
        /// <param name="card">The card GameObject to add</param>
        /// <param name="animationSystem">Optional animation system to animate the card (can be null)</param>
        /// <param name="animationDuration">Duration of the animation if animation system is provided</param>
        /// <returns>True if the card was added successfully, false otherwise</returns>
        public override bool AddCard(GameObject card, AnimationSystem animationSystem = null, float animationDuration = 0.3f)
        {
            if (card == null)
            {
                Debug.LogError("CardDeck: Cannot add a null card GameObject.", this);
                return false;
            }
            if (card.TryGetComponent<CardLayout>(out CardLayout cardLayout))
            {
                // The animation is handled by CardInteractionSystem based on HandleCardDropped.
                // This method is now called by CIS *after* the animation is complete or started.
                // So, we just need to add the card logically and ensure its transform is correct.
                return AddCard(cardLayout);
            }
            else
            {
                Debug.LogError($"CardDeck: Card {card.name} does not have a CardLayout component and cannot be added to the deck.", this);
                return false;
            }
        }

        /// <summary>
        /// Instantiates the CardPrefab, adds it to the deck, and applies default size/layout.
        /// </summary>
        /// <returns>The newly created CardLayout instance, or null on failure.</returns>
        public CardLayout AddNewCard()
        {
            if (m_CardPrefab == null)
            {
                Debug.LogError("CardDeck: Card Prefab is not assigned. Cannot add new card from prefab.", this);
                return null;
            }
             if (m_Cards.Count >= m_MaxCards)
            {
                Debug.LogWarning("Deck is full. Cannot add more cards.", this);
                return null;
            }

            GameObject newCardGO = Instantiate(m_CardPrefab);
            newCardGO.name = $"{m_CardPrefab.name}_{m_Cards.Count}";

            if (!newCardGO.TryGetComponent<CardLayout>(out var cardLayout))
            {
                 Debug.LogError($"Instantiated CardPrefab '{m_CardPrefab.name}' does not have a CardLayout component.", this);
                 Destroy(newCardGO);
                 return null;
            }

             CardMeshGenerator meshGen = cardLayout.GetComponent<CardMeshGenerator>();
             if (meshGen == null) {
                 Debug.LogError($"Instantiated CardPrefab '{m_CardPrefab.name}' does not have a CardMeshGenerator component.", this);
                 Destroy(newCardGO);
                 return null;
             }
             if(meshGen.CurrentSizeData == null || meshGen.CurrentSizeData.Width <= 0) {
                 if(meshGen.EditorSizeData != null && meshGen.EditorSizeData.Width > 0) {
                      meshGen.ApplySizeData(meshGen.EditorSizeData);
                      Debug.Log($"Applied EditorSizeData to new card '{newCardGO.name}'.", this);
                 } else {
                      Debug.LogError($"Could not apply valid size data to new card '{newCardGO.name}'. A default CardSizeData might be needed.", this);
                      if (Application.isPlaying) Destroy(newCardGO); else DestroyImmediate(newCardGO);
                      return null;
                 }
             }
            if (AddCard(cardLayout))
            {
                return cardLayout;
            }
            else
            {
                Debug.LogError($"Failed to add new card '{newCardGO.name}' to deck.", this);
                 if (Application.isPlaying) Destroy(newCardGO); else DestroyImmediate(newCardGO);
                return null;
            }
        }

        /// <summary>
        /// Removes and returns the top card from the deck.
        /// The card GameObject is NOT destroyed but is unparented.
        /// </summary>
        /// <returns>The CardLayout of the drawn card, or null if the deck is empty.</returns>
        public CardLayout DrawCard()
        {
             if (m_Cards.Count == 0)
             {
                 Debug.Log("Deck is empty. Cannot draw card.", this);
                 return null;
             }

             int topIndex = m_Cards.Count - 1;
             DeckCardInfo drawnCardInfo = m_Cards[topIndex];
             m_Cards.RemoveAt(topIndex);

             if (drawnCardInfo.CardInstance != null)
             {
                 drawnCardInfo.CardInstance.transform.SetParent(null, true); // Keep world position

                 // Remove the card from the associated cards list (base class functionality)
                 base.RemoveCard(drawnCardInfo.CardInstance.gameObject);

                 Debug.Log($"Drew card: {drawnCardInfo.CardInstance.name}", this);
             }

             if(m_Cards.Count == 0) {
                 m_ReferenceCardSize = null;
             }

            HandleDynamicDeckStateChange();
             // No UpdateLayout needed when drawing top card
             // UpdateLayout();
             return drawnCardInfo.CardInstance;
        }

        /// <summary>
        /// Finds and removes a card from the deck by its GameObject.
        /// The card GameObject is NOT destroyed but is unparented.
        /// </summary>
        /// <param name="cardObject">The GameObject of the card to remove</param>
        /// <returns>The CardLayout of the removed card, or null if not found</returns>
        public CardLayout RemoveCardByGameObject(GameObject cardObject)
        {
            if (cardObject == null || m_Cards.Count == 0)
            {
                return null;
            }

            int index = m_Cards.FindIndex(info => info.CardInstance != null && info.CardInstance.gameObject == cardObject);

            if (index >= 0)
            {
                CardLayout cardLayout = m_Cards[index].CardInstance;
                m_Cards.RemoveAt(index);

                // CardInteractionSystem will handle the card's new transform after this.
                // We just need to unparent it and disassociate.
                if (cardLayout.transform.parent == this.transform)
                {
                    cardLayout.transform.SetParent(null, true); // Keep world position
                }

                base.RemoveCard(cardObject); // Disassociate from CardArea part

                Debug.Log($"CardDeck: Removed card {cardLayout.name} from deck. Remaining: {m_Cards.Count}", this);

                if (m_Cards.Count == 0)
                {
                    m_ReferenceCardSize = null;
                }
                // No full UpdateLayout() here, as CIS is taking control of the removed card.
                // If a card from middle is removed, remaining cards keep their world pos until next explicit UpdateLayout.

                HandleDynamicDeckStateChange();
                return cardLayout;
            }
            Debug.LogWarning($"CardDeck: Card {cardObject.name} not found in m_Cards for removal by GameObject.", this);
            return null;
        }

        /// <summary>
        /// Removes a card from the deck. Overrides the base CardArea method.
        /// </summary>
        /// <param name="card">The card GameObject to remove</param>
        /// <returns>True if the card was removed successfully, false otherwise</returns>
        public override bool RemoveCard(GameObject card)
        {
            if (card == null)
            {
                return false;
            }

            // Use the existing RemoveCardByGameObject method
            CardLayout removedCard = RemoveCardByGameObject(card);

            // If the card was successfully removed, return true
            return removedCard != null;
        }

        /// <summary>
        /// Returns the top card without removing it.
        /// </summary>
        public CardLayout PeekTopCard()
        {
            if (m_Cards.Count == 0) return null;
            return m_Cards[m_Cards.Count - 1].CardInstance;
        }

        /// <summary>
        /// Removes all cards from the deck and destroys their GameObjects.
        /// </summary>
        public void ClearDeck()
        {
            for (int i = m_Cards.Count - 1; i >= 0; i--)
            {
                if (m_Cards[i].CardInstance != null)
                {
                    if (Application.isPlaying)
                        Destroy(m_Cards[i].CardInstance.gameObject);
                    else
                        DestroyImmediate(m_Cards[i].CardInstance.gameObject);
                }
            }
            m_Cards.Clear();
            m_ReferenceCardSize = null;
            Debug.Log("Deck cleared.", this);
            HandleDynamicDeckStateChange();
        }

        /// <summary>
        /// Re-applies the stacking layout to all cards in the deck using cumulative steps.
        /// </summary>
        public void UpdateLayout()
        {
            if (m_Cards.Count == 0) return;

            // Ensure we have a reference size for thickness calculation
            if (m_ReferenceCardSize == null || m_ReferenceCardSize.Width <= 0)
            {
                 if (m_Cards.Count > 0 && m_Cards[0].CardInstance != null) {
                      if(!TrySetReferenceSize(m_Cards[0].CardInstance)) {
                           Debug.LogError("Cannot update layout: Reference card size is missing or invalid.", this);
                           return;
                      }
                 } else {
                      Debug.LogError("Cannot update layout: No cards in deck to determine reference size.", this);
                      return;
                 }
            }

            float cardThicknessMeters = UnitConverter.MmToMeters(m_ReferenceCardSize.Thickness);
            float effectiveVerticalOffset = cardThicknessMeters * m_ThicknessSpacingMultiplier;

            // Initialize cumulative values
            Vector2 currentOffset = Vector2.zero;
            float currentRotation = 0f;
            float currentVerticalOffset = 0f;

            for (int i = 0; i < m_Cards.Count; i++)
            {
                DeckCardInfo info = m_Cards[i];
                if (info.CardInstance != null)
                {
                    // --- Calculate Transform for Card `i` ---

                    // First card (i=0) is at the origin of the stack
                    if (i > 0)
                    {
                         // Calculate step change from previous card (i-1)
                         float noiseRangeX = m_OffsetStepNoise * 0.5f;
                         float stepOffsetX = m_CumulativeOffsetStep.x + (float)(m_Rng.NextDouble() * m_OffsetStepNoise - noiseRangeX);

                         float noiseRangeY = m_OffsetStepNoise * 0.5f;
                         float stepOffsetY = m_CumulativeOffsetStep.y + (float)(m_Rng.NextDouble() * m_OffsetStepNoise - noiseRangeY);

                         float noiseRangeRot = m_RotationStepNoise * 0.5f;
                         float stepRotation = m_CumulativeRotationStep + (float)(m_Rng.NextDouble() * m_RotationStepNoise - noiseRangeRot);

                        // Accumulate values
                        currentOffset.x += stepOffsetX;
                        currentOffset.y += stepOffsetY;
                        currentRotation += stepRotation;
                        currentVerticalOffset += effectiveVerticalOffset;
                    }
                    else // For the first card (i=0)
                    {
                        currentOffset = Vector2.zero;
                        currentRotation = 0f;
                        currentVerticalOffset = 0f;
                    }

                    // Calculate final local position and rotation
                    Vector3 targetLocalPosition = new Vector3(
                        currentOffset.x,
                        currentOffset.y,
                        -currentVerticalOffset // Negative Z stacks "up" visually if Z+ is forward
                    );
                    Quaternion targetLocalRotation = Quaternion.Euler(0f, 0f, currentRotation);

                    // Apply transform
                    info.CardInstance.transform.localPosition = targetLocalPosition;
                    info.CardInstance.transform.localRotation = targetLocalRotation;
                    info.CardInstance.transform.localScale = Vector3.one; // Ensure scale is correct

                    // Ensure card is at the correct hierarchy index visually
                    info.CardInstance.transform.SetSiblingIndex(i);
                }
                else
                {
                     Debug.LogWarning($"Card at index {i} is null. Removing from deck.", this);
                     m_Cards.RemoveAt(i);
                     i--; // Adjust index after removal
                     if (m_Cards.Count == 0) m_ReferenceCardSize = null;
                }
            }
             Debug.Log($"Performed full deck layout update for {m_Cards.Count} cards.", this);
        }

        // --- Helper Methods ---

        /// <summary>
        /// Calculates and applies the local transform for a newly added card.
        /// </summary>
        private void PositionNewCard(CardLayout newCardInstance, bool isFirstCard)
        {
             if (newCardInstance == null) return;

             Vector3 targetLocalPosition;
             Quaternion targetLocalRotation;

             if (isFirstCard || m_ReferenceCardSize == null) // Should not happen if isFirstCard is false due to checks
             {
                  // First card goes to origin
                  targetLocalPosition = Vector3.zero;
                  targetLocalRotation = Quaternion.identity;
             }
             else
             {
                  // Position relative to the previous top card (which is now at index Count - 1)
                  CardLayout previousTopCard = m_Cards[m_Cards.Count - 1].CardInstance;
                  if (previousTopCard == null) {
                      // Fallback if previous card is somehow null, place at origin
                      Debug.LogWarning("Previous top card was null when adding new card. Placing at origin.", this);
                      targetLocalPosition = Vector3.zero;
                      targetLocalRotation = Quaternion.identity;
                  } else {
                        // Calculate step change from previous card
                        float cardThicknessMeters = UnitConverter.MmToMeters(m_ReferenceCardSize.Thickness);
                        float effectiveVerticalOffset = cardThicknessMeters * m_ThicknessSpacingMultiplier;

                        float noiseRangeX = m_OffsetStepNoise * 0.5f;
                        float stepOffsetX = m_CumulativeOffsetStep.x + (float)(m_Rng.NextDouble() * m_OffsetStepNoise - noiseRangeX);

                        float noiseRangeY = m_OffsetStepNoise * 0.5f;
                        float stepOffsetY = m_CumulativeOffsetStep.y + (float)(m_Rng.NextDouble() * m_OffsetStepNoise - noiseRangeY);

                        float noiseRangeRot = m_RotationStepNoise * 0.5f;
                        float stepRotation = m_CumulativeRotationStep + (float)(m_Rng.NextDouble() * m_RotationStepNoise - noiseRangeRot);

                        // Apply step change to previous card's transform
                        targetLocalPosition = previousTopCard.transform.localPosition + new Vector3(stepOffsetX, stepOffsetY, -effectiveVerticalOffset);
                        targetLocalRotation = previousTopCard.transform.localRotation * Quaternion.Euler(0f, 0f, stepRotation);
                  }
             }

             // Apply transform
             newCardInstance.transform.localPosition = targetLocalPosition;
             newCardInstance.transform.localRotation = targetLocalRotation;
             newCardInstance.transform.localScale = Vector3.one;
        }

        private bool TrySetReferenceSize(CardLayout cardInstance)
        {
             if (cardInstance == null || cardInstance.CardMeshGenerator == null)
             {
                 Debug.LogError("Cannot set reference size: Provided card instance or its CardMeshGenerator is null.", cardInstance);
                 return false;
             }

             CardMeshGenerator meshGen = cardInstance.CardMeshGenerator;
             m_ReferenceCardSize = meshGen.CurrentSizeData;

             if (m_ReferenceCardSize == null || m_ReferenceCardSize.Width <= 0)
             {
                  Debug.LogWarning("Card's CurrentSizeData is invalid. Trying EditorSizeData...", cardInstance);
                  if (meshGen.EditorSizeData != null && meshGen.EditorSizeData.Width > 0)
                  {
                       meshGen.ApplySizeData(meshGen.EditorSizeData);
                       m_ReferenceCardSize = meshGen.CurrentSizeData;
                       Debug.Log("Applied EditorSizeData to determine reference size.", this);
                  }
             }

             if (m_ReferenceCardSize == null || m_ReferenceCardSize.Width <= 0)
             {
                  Debug.LogError("Failed to obtain valid CardSizeData to use as reference.", cardInstance);
                  m_ReferenceCardSize = null;
                  return false;
             }
             return true;
        }

        // --- Stub Methods for Future Implementation ---

        /// <summary>
        /// Shuffles the deck using Fisher-Yates algorithm.
        /// This is the synchronous, non-animated version.
        /// </summary>
        public void Shuffle()
        {
            ShuffleInternalLogicOnly();
            UpdateLayout(); // Full layout update needed after shuffle
            Debug.Log($"Deck shuffled ({m_Cards.Count} cards) synchronously.", this);
        }

        /// <summary>
        /// Shuffles the m_Cards list internally using Fisher-Yates. Does not update layout or log.
        /// </summary>
        private void ShuffleInternalLogicOnly()
        {
            int n = m_Cards.Count;
            while (n > 1)
            {
                n--;
                int k = m_Rng.Next(n + 1); // m_Rng is the main deck RNG
                DeckCardInfo value = m_Cards[k];
                m_Cards[k] = m_Cards[n];
                m_Cards[n] = value;
            }
        }

        /// <summary>
        /// Shuffles the deck with animation.
        /// Cards will "explode" outwards, then gather into their new shuffled positions.
        /// </summary>
        public async Task ShuffleAnimated()
        {
            if (m_Cards.Count <= 1)
            {
                Debug.Log("CardDeck: Shuffle animated skipped, 1 or 0 cards in deck.", this);
                return;
            }

            AnimationSystem animationSystem = TableManager.Instance?.GetSystem<AnimationSystem>();
            if (animationSystem == null)
            {
                Debug.LogWarning("CardDeck: AnimationSystem not found. Performing non-animated shuffle.", this);
                Shuffle(); // Call the synchronous, non-animated version
                return;
            }

            // --- Check if any cards in the deck are currently animating ---
            foreach (var cardInfo in m_Cards)
            {
                if (cardInfo.CardInstance != null && animationSystem.IsObjectAnimating(cardInfo.CardInstance.gameObject))
                {
                    Debug.LogWarning($"CardDeck: Cannot start shuffle. Card '{cardInfo.CardInstance.name}' is currently animating. Please try again shortly.", this);
                    return; // Don't start shuffle if any card is busy
                }
            }

            // --- Store original state for explosion reference ---
            List<TransformData> originalTransforms = new List<TransformData>();
            foreach (var cardInfo in m_Cards)
            {
                if (cardInfo.CardInstance != null)
                {
                    originalTransforms.Add(new TransformData(cardInfo.CardInstance, cardInfo.CardInstance.transform.position, cardInfo.CardInstance.transform.rotation));
                }
            }

            // --- "Explode" Phase ---
            Debug.Log($"CardDeck: Starting animated shuffle for {m_Cards.Count} cards - Explode phase.", this);
            List<Task> explodeTasks = new List<Task>();
            System.Random explodeRng = new System.Random(m_Rng.Next()); // Use a temporary RNG for explosion visuals

            foreach (var initialTransform in originalTransforms)
            {
                CardLayout cardInstance = initialTransform.Card;
                if (cardInstance == null) continue;

                // Calculate explosion target relative to the deck's transform
                float randomX = ((float)explodeRng.NextDouble() * 2f - 1f) * m_ShuffleExplodeExtents.x;
                float randomY = ((float)explodeRng.NextDouble() * 2f - 1f) * m_ShuffleExplodeExtents.y;
                float randomZOffset = -m_ShuffleExplodeExtents.z * (0.5f + (float)explodeRng.NextDouble()); // Explode "upwards" (local -Z)

                Vector3 localExplodePosition = new Vector3(randomX, randomY, randomZOffset);
                Vector3 explodeTargetWorldPos = transform.TransformPoint(localExplodePosition);

                Quaternion randomLocalRotation = Quaternion.Euler(
                    //((float)explodeRng.NextDouble() * 2f - 1f) * m_ShuffleExplodeRotationRange,
                    //((float)explodeRng.NextDouble() * 2f - 1f) * m_ShuffleExplodeRotationRange,
                    0, 0,
                    ((float)explodeRng.NextDouble() * 2f - 1f) * m_ShuffleExplodeRotationRange
                );
                Quaternion explodeTargetWorldRot = transform.rotation * randomLocalRotation;

                explodeTasks.Add(animationSystem.AnimateCardPositionAndRotation(cardInstance.gameObject, explodeTargetWorldPos, explodeTargetWorldRot, m_ShuffleExplodeDuration, m_ShuffleExplodeCurve, false));
            }
            if (explodeTasks.Count > 0) await Task.WhenAll(explodeTasks);
            Debug.Log("CardDeck: Explode phase complete.", this);

            // --- Shuffle Internals (Fisher-Yates on m_Cards list) ---
            ShuffleInternalLogicOnly();

            // --- Calculate Final Deck Positions (local and world) for the GATHER phase ---
            Debug.Log("CardDeck: Starting Gather phase - Calculating final positions.", this);
            Dictionary<CardLayout, (Vector3 worldPos, Quaternion worldRot)> cardToFinalWorldTransform = new Dictionary<CardLayout, (Vector3, Quaternion)>();

            if (m_Cards.Count > 0 && (m_ReferenceCardSize == null || m_ReferenceCardSize.Width <= 0))
            {
                DeckCardInfo firstCardInfo = m_Cards[0];
                if (firstCardInfo.CardInstance != null && !TrySetReferenceSize(firstCardInfo.CardInstance))
                {
                    Debug.LogError("CardDeck: Cannot calculate final positions for shuffle: Reference card size issue. Falling back to non-animated.", this);
                    UpdateLayout(); // Attempt to fix positions
                    return;
                }
                else if (firstCardInfo.CardInstance == null)
                {
                    Debug.LogError("CardDeck: First card in deck is null after shuffle, cannot determine reference size for layout. Falling back to non-animated.", this);
                    UpdateLayout(); // Attempt to cleanup
                    return;
                }
            }

            float cardThicknessMeters = (m_ReferenceCardSize != null && m_ReferenceCardSize.Width > 0) ? UnitConverter.MmToMeters(m_ReferenceCardSize.Thickness) : 0.0005f; // A small default if no ref size
            float effectiveVerticalOffsetPerCard = cardThicknessMeters * m_ThicknessSpacingMultiplier;
            Vector2 currentOffsetAccumulator = Vector2.zero;
            float currentRotationAccumulator = 0f;
            float currentVerticalOffsetAccumulator = 0f;
            System.Random layoutCalcRng = new System.Random(m_Rng.Next()); // Seed from main RNG for layout variation, or new Random() for consistent look

            for (int i = 0; i < m_Cards.Count; i++)
            {
                CardLayout cardInstance = m_Cards[i].CardInstance;
                if (cardInstance == null) continue;

                Vector3 targetLocalPos;
                Quaternion targetLocalRot;

                if (i > 0)
                {
                    float noiseRangeX = m_OffsetStepNoise * 0.5f;
                    float stepOffsetX = m_CumulativeOffsetStep.x + (float)(layoutCalcRng.NextDouble() * m_OffsetStepNoise - noiseRangeX);
                    float noiseRangeY = m_OffsetStepNoise * 0.5f;
                    float stepOffsetY = m_CumulativeOffsetStep.y + (float)(layoutCalcRng.NextDouble() * m_OffsetStepNoise - noiseRangeY);
                    float noiseRangeRot = m_RotationStepNoise * 0.5f;
                    float stepRotation = m_CumulativeRotationStep + (float)(layoutCalcRng.NextDouble() * m_RotationStepNoise - noiseRangeRot);

                    currentOffsetAccumulator.x += stepOffsetX;
                    currentOffsetAccumulator.y += stepOffsetY;
                    currentRotationAccumulator += stepRotation;
                    currentVerticalOffsetAccumulator += effectiveVerticalOffsetPerCard;
                }
                else // First card
                {
                    currentOffsetAccumulator = Vector2.zero;
                    currentRotationAccumulator = 0f;
                    currentVerticalOffsetAccumulator = 0f;
                }
                targetLocalPos = new Vector3(currentOffsetAccumulator.x, currentOffsetAccumulator.y, -currentVerticalOffsetAccumulator);
                targetLocalRot = Quaternion.Euler(0f, 0f, currentRotationAccumulator);

                cardToFinalWorldTransform[cardInstance] = (transform.TransformPoint(targetLocalPos), transform.rotation * targetLocalRot);
            }

            // --- "Gather" Phase ---
            List<Task> gatherTasks = new List<Task>();
            foreach (var cardInfo in m_Cards) // Iterate in the new shuffled order
            {
                CardLayout cardInstance = cardInfo.CardInstance;
                if (cardInstance == null) continue;

                if (cardToFinalWorldTransform.TryGetValue(cardInstance, out var finalTransform))
                {
                    gatherTasks.Add(animationSystem.AnimateCardPositionAndRotation(cardInstance.gameObject, finalTransform.worldPos, finalTransform.worldRot, m_ShuffleGatherDuration, m_ShuffleGatherCurve, false));
                }
                else
                {
                    Debug.LogWarning($"CardDeck: Card {cardInstance.name} was not found in final transform map for gather phase. Skipping animation for it. It might be snapped by UpdateLayout.", this);
                }
            }
            if (gatherTasks.Count > 0) await Task.WhenAll(gatherTasks);
            Debug.Log("CardDeck: Gather phase complete.", this);

            // Small delay to ensure animations visually complete before snapping transforms
            await Task.Delay(50); // 50 milliseconds, can be adjusted

            // --- Finalization ---
            UpdateLayout(); // Snap to perfect positions and set sibling indices
            Debug.Log($"Deck shuffled ({m_Cards.Count} cards) with animation.", this);
        }

        public void RevealTopCard()
        {
            CardLayout topCard = PeekTopCard();
            if (topCard != null)
            {
                Debug.Log($"TODO: Implement visual reveal for card: {topCard.name}", this);
            }
             else { Debug.Log("Deck is empty. Cannot reveal card.", this);}
        }

        public void PlaceOnTop(CardLayout card)
        {
            if (card == null) {
                Debug.LogError("Cannot place a null card on top.", this);
                return;
            }

             int existingIndex = m_Cards.FindIndex(info => info.CardInstance == card);
             if (existingIndex != -1) {
                 m_Cards.RemoveAt(existingIndex);
                 // Update layout if removing from middle, otherwise sibling indices are wrong
                 if (existingIndex < m_Cards.Count) {
                     UpdateLayout(); // Need full update if removing from middle/bottom
                 }
             }

            if (m_Cards.Count >= m_MaxCards) {
                Debug.LogWarning("Deck is full. Cannot place card on top.", this);
                return;
            }

            Debug.Log($"Placing card {card.name} on top of the deck.", this);
            // AddCard handles parenting and positioning the single card
            if (!AddCard(card)) {
                 Debug.LogError($"Failed to re-add card {card.name} after removing it.", this);
            }
        }

        public void FlipDeck()
        {
            m_Cards.Reverse();
            UpdateLayout(); // Full layout update needed after reversing
            Debug.Log("Deck order reversed.", this);
        }

        public void Discard(CardLayout card)
        {
            if (card == null) return;

             int index = m_Cards.FindIndex(info => info.CardInstance == card);
             if (index != -1)
             {
                 m_Cards.RemoveAt(index);
                 if(m_Cards.Count == 0) {
                     m_ReferenceCardSize = null;
                 }
                 Debug.Log($"Removed card {card.name} from deck for discarding.", this);
                 // Update layout if removing from middle/bottom
                 if (index < m_Cards.Count) {
                     UpdateLayout();
                 }

                 // TODO: Add logic to move the card GameObject to a discard pile location/manager
                 Debug.Log($"TODO: Implement moving card {card.name} to a discard pile.", this);
             }
             else
             {
                 Debug.LogWarning($"Card {card.name} not found in the deck to discard.", this);
             }
        }

        #region CardArea Overrides
        /// <returns>A CardDropResult containing the position and rotation where the card should be placed</returns>
        public override CardDropResult HandleCardDropped(GameObject card, Vector3 position)
        {
            if (!card.TryGetComponent<CardLayout>(out CardLayout cardLayout))
            {
                Debug.LogWarning($"CardDeck ({name}): Card {card.name} lacks CardLayout. Cannot process drop.", this);
                return CardDropResult.NoAction();
            }

            if (m_Cards.Count >= m_MaxCards)
            {
                Debug.LogWarning($"CardDeck ({name}): Deck is full. Cannot add {card.name}.", this);
                return CardDropResult.NoAction();
            }

            // --- Calculate target transform without actually adding/parenting yet ---
            Vector3 targetLocalPosition;
            Quaternion targetLocalRotation;
            bool isFirstCardForCalc = m_Cards.Count == 0;

            // Logic adapted from PositionNewCard to predict transform
            if (isFirstCardForCalc || m_ReferenceCardSize == null)
            {
                 if (isFirstCardForCalc && m_ReferenceCardSize == null) {
                    // Attempt to set reference size from the card being dropped if it's the first
                    // This is a temporary measure for calculation; actual setting happens in AddCard.
                    CardMeshGenerator tempMeshGen = cardLayout.CardMeshGenerator;
                    if(tempMeshGen == null || tempMeshGen.CurrentSizeData == null || tempMeshGen.CurrentSizeData.Width <=0) {
                        Debug.LogWarning($"CardDeck ({name}): Dropped card {card.name} has no valid size for ref. Assuming default stack.", this);
                        // Cannot determine thickness, use minimal stacking for prediction
                    } else {
                        // Use this size for prediction only
                    }
                 }
                 targetLocalPosition = Vector3.zero;
                 targetLocalRotation = Quaternion.identity;
            }
            else
            {
                CardLayout previousTopCard = m_Cards[m_Cards.Count - 1].CardInstance;
                if (previousTopCard == null)
                {
                    targetLocalPosition = Vector3.zero;
                    targetLocalRotation = Quaternion.identity;
                }
                else
                {
                    float cardThicknessMeters = UnitConverter.MmToMeters(m_ReferenceCardSize.Thickness);
                    float effectiveVerticalOffset = cardThicknessMeters * m_ThicknessSpacingMultiplier;
                    float noiseRangeX = m_OffsetStepNoise * 0.5f;
                    float stepOffsetX = m_CumulativeOffsetStep.x + (float)(m_Rng.NextDouble() * m_OffsetStepNoise - noiseRangeX);
                    float noiseRangeY = m_OffsetStepNoise * 0.5f;
                    float stepOffsetY = m_CumulativeOffsetStep.y + (float)(m_Rng.NextDouble() * m_OffsetStepNoise - noiseRangeY);
                    float noiseRangeRot = m_RotationStepNoise * 0.5f;
                    float stepRotation = m_CumulativeRotationStep + (float)(m_Rng.NextDouble() * m_RotationStepNoise - noiseRangeRot);

                    targetLocalPosition = previousTopCard.transform.localPosition + new Vector3(stepOffsetX, stepOffsetY, -effectiveVerticalOffset);
                    targetLocalRotation = previousTopCard.transform.localRotation * Quaternion.Euler(0f, 0f, stepRotation);
                }
            }

            // Convert local to world for CardInteractionSystem
            Vector3 targetWorldPosition = transform.TransformPoint(targetLocalPosition);
            Quaternion targetWorldRotation = transform.rotation * targetLocalRotation;

            Debug.Log($"CardDeck ({name}): Handling drop for {card.name}. TargetWorldPos: {targetWorldPosition}, TargetWorldRot: {targetWorldRotation.eulerAngles}", this);
            // CardInteractionSystem will animate to this, then call deck.AddCard(gameObject)
            return new CardDropResult(targetWorldPosition, targetWorldRotation, true);
        }
        #endregion

        /// <summary>
        /// Gets the reference card size, attempting to set it if null and cards exist.
        /// Used by external systems needing size info for animations before full layout.
        /// </summary>
        public CardSizeData GetReferenceCardSizeForAnim()
        {
            if (m_ReferenceCardSize == null || m_ReferenceCardSize.Width <= 0)
            {
                if (m_Cards.Count > 0 && m_Cards[0].CardInstance != null)
                {
                    TrySetReferenceSize(m_Cards[0].CardInstance);
                }
                // If still null, it means no cards or first card has no size data yet.
                // Consumer might need to use a default.
            }
            return m_ReferenceCardSize;
        }

        /// <summary>
        /// Gets the thickness spacing multiplier for animation calculations.
        /// </summary>
        public float GetThicknessSpacingMultiplierForAnim() => m_ThicknessSpacingMultiplier;

        /// <summary>
        /// Gets the cumulative offset step for animation calculations.
        /// </summary>
        public Vector2 GetCumulativeOffsetStepForAnim() => m_CumulativeOffsetStep;

        /// <summary>
        /// Gets the cumulative rotation step for animation calculations.
        /// </summary>
        public float GetCumulativeRotationStepForAnim() => m_CumulativeRotationStep;

        /// <summary>
        /// Marks this deck instance as dynamically created (or not).
        /// Dynamically created decks may be cleaned up when empty.
        /// </summary>
        public void MarkAsDynamic(bool isDynamic)
        {
            m_IsDynamicallyCreated = isDynamic;
        }

        private void HandleDynamicDeckStateChange()
        {
            if (!m_IsDynamicallyCreated) return;

            if (m_Cards.Count == 1)
            {
                CardLayout lastCardLayout = m_Cards[0].CardInstance;
                if (lastCardLayout != null)
                {
                    Debug.Log($"CardDeck '{this.name}': Is dynamic and has 1 card left ('{lastCardLayout.name}'). Dissolving deck and releasing card.", this);
                    
                    // Unparent the card, preserving its world position/rotation for now
                    lastCardLayout.transform.SetParent(null, true);
                    
                    // Reset rotation to be flat on the table (assuming Quaternion.identity is flat)
                    lastCardLayout.transform.rotation = Quaternion.identity;
                    
                    // Optional: Adjust Z position to a defined table height if known.
                    // Vector3 cardPosition = lastCardLayout.transform.position;
                    // cardPosition.z = TableManager.Instance.GetTableSurfaceHeight(); // Example, if such a method existed
                    // lastCardLayout.transform.position = cardPosition;

                    // Remove from CardArea's perspective and deck's internal list
                    base.RemoveCard(lastCardLayout.gameObject); // Important to do this before clearing m_Cards if it relies on finding it
                    m_Cards.Clear(); // Clear the list now that the card is out
                }
                else
                {
                    Debug.LogWarning($"CardDeck '{this.name}': Is dynamic, has 1 card but it's null. Destroying deck.", this);
                    m_Cards.Clear(); // Ensure list is clear if card was null
                }
                Destroy(this.gameObject); 
            }
            else if (m_Cards.Count == 0)
            {
                Debug.Log($"CardDeck '{this.name}': Is dynamic and empty. Destroying self.", this);
                // Ensure m_AssociatedCards is also clear if base.RemoveCard wasn't called for each card
                // However, calls leading here (DrawCard, RemoveCardByGameObject, ClearDeck) should handle m_AssociatedCards for individual cards.
                // If ClearDeck was called, it would have removed all from m_AssociatedCards already via its loop of Destroy.
                // This path is more of a final catch-all if count hits 0.
                Destroy(this.gameObject); 
            }
        }
    }
}

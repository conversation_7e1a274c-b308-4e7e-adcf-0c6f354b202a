using UnityEngine;
using UnityEngine.UIElements;
using Unity.AppUI.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using Unity.AppUI.Core; // For ExVisualElement if needed
using Workshop.UXLab.Data; // Added for CardSizeData
using System.Globalization; // Added for CultureInfo

namespace Workshop.UXLab
{
    /// <summary>
    /// Custom VisualElement for editing Card dimensions.
    /// Replaces the previous MonoBehaviour CardDimensionEditorControl.
    /// </summary>
    public class CardDimensionEditorControl : VisualElement // Inherit from VisualElement
    {
        // Path to the UXML file (consider making this configurable)
        private const string UxmlPath = "UI/CardDimensionEditorLayout"; 
        private const string UssPath = "UI/CardDimensionEditorStyles"; 
        private const string VisibleClassName = "visible"; // USS class for visibility

        // UI Elements (matching names in CardDimensionEditorLayout.uxml)
        private DraggablePanel m_Container; // Still need the draggable container within
        private Unity.AppUI.UI.Dropdown m_CardTypeDropdown;
        private VisualElement m_CustomSizeContainer;
        private Unity.AppUI.UI.FloatField m_WidthField;
        private Unity.AppUI.UI.FloatField m_HeightField;
        private Unity.AppUI.UI.FloatField m_ThicknessField;
        private Unity.AppUI.UI.FloatField m_CornerRadiusField;
        private Unity.AppUI.UI.Dropdown m_UnitDropdown;

        // References
        private CardMeshGenerator m_CurrentCard;
        private bool m_IgnoreValueChange = false;

        // Common card types and their dimensions in mm (Copied from original)
        private static readonly Dictionary<string, Vector3> kCardSizes = new Dictionary<string, Vector3>
        {
            { "Poker/Playing Card (63.5 x 88.9 mm)", new Vector3(63.5f, 88.9f, 0.3f) },
            { "Standard Credit Card (85.6 x 53.98 mm)", new Vector3(85.6f, 53.98f, 0.76f) },
            { "Business Card (89 x 51 mm)", new Vector3(89f, 51f, 0.35f) },
            { "Tarot Card (70 x 120 mm)", new Vector3(70f, 120f, 0.5f) },
            { "Mini Card (44.5 x 63.5 mm)", new Vector3(44.5f, 63.5f, 0.3f) },
            { "Custom", Vector3.zero } 
        };

        // Lists to store dropdown options
        private List<string> m_CardTypeOptions;
        private List<string> m_UnitOptions;

        // UxmlFactory and UxmlTraits (Needed for usage directly in UXML if desired, but not strictly required for C# instantiation)
        // public new class UxmlFactory : UxmlFactory<CardDimensionEditorControl, UxmlTraits> { }
        // public new class UxmlTraits : VisualElement.UxmlTraits { }

        public CardDimensionEditorControl()
        {
            // Load the UXML template
            var visualTree = Resources.Load<VisualTreeAsset>(UxmlPath);
            if (visualTree != null)
            {
                visualTree.CloneTree(this); // Clone the content into this element
                 // Apply the Styles
                 StyleSheet styleSheet = Resources.Load<StyleSheet>(UssPath);
                 if (styleSheet != null)
                 {
                     styleSheets.Add(styleSheet);
                 } else {
                     Debug.LogError($"CardDimensionEditorControl: Could not load USS from {UssPath}");
                 }
            }
            else
            {
                Debug.LogError($"CardDimensionEditorControl: Failed to load UXML from {UxmlPath}");
                // Optionally add fallback UI elements here if loading fails
                this.Add(new Label("Error loading Card Dimension Editor UI."));
                return; 
            }

            // Query for elements *within this newly cloned tree*
            // Important: Query from 'this' element, not the root document
            m_Container = this.Q<DraggablePanel>("card-editor-container");
            if (m_Container == null) { Debug.LogError("CardDimensionEditorControl: Could not find DraggablePanel 'card-editor-container'"); return; }
            
            m_CardTypeDropdown = this.Q<Unity.AppUI.UI.Dropdown>("card-type-dropdown");
            m_CustomSizeContainer = this.Q<VisualElement>("custom-size-container");
            m_WidthField = this.Q<Unity.AppUI.UI.FloatField>("width-field");
            m_HeightField = this.Q<Unity.AppUI.UI.FloatField>("height-field");
            m_ThicknessField = this.Q<Unity.AppUI.UI.FloatField>("thickness-field");
            m_CornerRadiusField = this.Q<Unity.AppUI.UI.FloatField>("corner-radius-field");
            m_UnitDropdown = this.Q<Unity.AppUI.UI.Dropdown>("unit-dropdown");

            // Basic null checks for essential fields
            if (m_CardTypeDropdown == null || m_WidthField == null || m_HeightField == null || 
                m_ThicknessField == null || m_CornerRadiusField == null || m_UnitDropdown == null)
            {
                 Debug.LogError("CardDimensionEditorControl: Failed to query essential UI fields within the UXML.");
                 return;
            }

            // Initialize Dropdowns
            InitializeCardTypeDropdown();
            InitializeUnitDropdown();

            // Register event handlers
            RegisterCallbacks();

            // Initial State
            // The element starts hidden via USS by default (by not having the .visible class)
            m_Container?.AddToClassList("hidden"); // Add hidden by default
            // Set the initial state of the custom fields
            UpdateCustomFieldsState("Custom"); // Default to enabled
            // Set initial formatting based on default unit
            UpdateAllFieldFormatting(); 
        }

        private void InitializeCardTypeDropdown()
        {
            m_CardTypeOptions = new List<string>(kCardSizes.Keys);
            m_CardTypeDropdown.sourceItems = m_CardTypeOptions;
            m_CardTypeDropdown.bindItem = (item, index) => {
                item.label = m_CardTypeOptions[index];
            };
            // Default to "Custom" which is the last item
            m_CardTypeDropdown.SetValueWithoutNotify(new int[] { m_CardTypeOptions.Count - 1 });
        }

        private void InitializeUnitDropdown()
        {
             m_UnitOptions = new List<string>();
             foreach (var unit in UnitConverter.kUnitLabels.OrderBy(kvp => kvp.Key))
             {
                 m_UnitOptions.Add(unit.Value);
             }
             m_UnitDropdown.sourceItems = m_UnitOptions;
             m_UnitDropdown.bindItem = (item, index) => {
                 item.label = m_UnitOptions[index];
             };
              // Set default unit (e.g., mm) without notification
              int defaultIndex = m_UnitOptions.IndexOf(UnitConverter.kUnitLabels[UnitSpace.Millimeters]);
              if (defaultIndex >= 0)
              {
                  m_UnitDropdown.SetValueWithoutNotify(new int[] { defaultIndex });
              }
              // No need to call UpdateAllFieldFormatting here yet, call it after all fields are queried in constructor.
        }

        private void RegisterCallbacks()
        {
            m_CardTypeDropdown?.RegisterValueChangedCallback(OnCardTypeChanged);
            m_WidthField?.RegisterValueChangedCallback(OnValueChanged);
            m_HeightField?.RegisterValueChangedCallback(OnValueChanged);
            m_ThicknessField?.RegisterValueChangedCallback(OnValueChanged);
            m_CornerRadiusField?.RegisterValueChangedCallback(OnValueChanged);
            m_UnitDropdown?.RegisterValueChangedCallback(OnUnitChanged);
            
             // Optional: Hook into draggable panel events if needed
            // m_Container?.DragStarted += (sender, e) => Debug.Log("CardEditor: Panel drag started");
            // m_Container?.DragEnded += (sender, e) => Debug.Log("CardEditor: Panel drag ended");
        }

        // --- Public Methods for Controlling the Element ---

        /// <summary>
        /// Sets the active card to be edited by the UI.
        /// </summary>
        public void SetActiveCard(CardMeshGenerator card)
        {
            if (card == null)
            {
                ClearActiveCard();
                return;
            }
            
            // Debug.Log("[CardDimensionEditorControl] SetActiveCard called."); // Removed log
            m_CurrentCard = card;
            UpdateUIValues();
            
            if (m_Container != null) 
            {
                m_Container.RemoveFromClassList("hidden"); // Remove hidden
                m_Container.AddToClassList(VisibleClassName);
                m_Container.pickingMode = PickingMode.Position;
                // Debug.Log("[CardDimensionEditorControl] m_Container found, setting visible."); // Removed log
            }
            else
            {
                Debug.LogError("[CardDimensionEditorControl] SetActiveCard: m_Container is null! Cannot make visible."); 
            }
            // this.style.display = DisplayStyle.Flex; // Replaced by class toggle
             // Update formatting when card becomes active
             UpdateAllFieldFormatting();
        }
        
        /// <summary>
        /// Clears the active card and hides the UI.
        /// </summary>
        public void ClearActiveCard()
        {
            // Debug.Log("[CardDimensionEditorControl] ClearActiveCard called."); // Removed log
            m_CurrentCard = null;
            
            if (m_Container != null)
            {
                m_Container.RemoveFromClassList(VisibleClassName);
                m_Container.AddToClassList("hidden"); // Add hidden
                m_Container.pickingMode = PickingMode.Ignore;
                // Debug.Log("[CardDimensionEditorControl] m_Container found, setting hidden."); // Removed log
            }
            else
            {
                Debug.LogError("[CardDimensionEditorControl] ClearActiveCard: m_Container is null! Cannot make hidden."); 
            }
            // this.style.display = DisplayStyle.None; // Replaced by class toggle
            
            // Reset UI fields to default? Optional, but might be good practice
            // ResetFieldsToDefault(); 
        }

        // --- Internal Logic (Copied and adapted from CardDimensionEditorControl) ---

        /// <summary>
        /// Updates the format string (precision and unit label) for a FloatField.
        /// </summary>
        private void UpdateFloatFieldFormatting(Unity.AppUI.UI.FloatField field, UnitSpace unit)
        {
            if (field == null) return;

            string format = "";
            string unitLabel = "";

            switch (unit)
            {
                case UnitSpace.Millimeters:
                    format = "0.00"; // 2 decimal places for mm
                    unitLabel = UnitConverter.kUnitLabelsShort[UnitSpace.Millimeters];
                    break;
                case UnitSpace.Centimeters:
                    format = "0.000"; // 3 decimal places for cm
                    unitLabel = UnitConverter.kUnitLabelsShort[UnitSpace.Centimeters];
                    break;
                case UnitSpace.Inches:
                    format = "0.0000"; // 4 decimal places for inches
                    unitLabel = UnitConverter.kUnitLabelsShort[UnitSpace.Inches];
                    break;
                case UnitSpace.Meters:
                    format = "0.0000"; // 4 decimal places for meters
                    unitLabel = UnitConverter.kUnitLabelsShort[UnitSpace.Meters];
                    break;
                 // Add cases for other units if needed (Pixels, Percent?)
            }

            // Use the dedicated 'unit' property and a custom numeric format string
            field.formatString = format;
            field.unit = unitLabel;
            // field.culture = CultureInfo.InvariantCulture; // Remove culture setting
        }

        /// <summary>
        /// Updates the formatting for all dimension-related FloatFields.
        /// </summary>
        private void UpdateAllFieldFormatting()
        {
            UnitSpace currentUnit = GetSelectedUnitSpace();
            UpdateFloatFieldFormatting(m_WidthField, currentUnit);
            UpdateFloatFieldFormatting(m_HeightField, currentUnit);
            UpdateFloatFieldFormatting(m_ThicknessField, currentUnit);
            UpdateFloatFieldFormatting(m_CornerRadiusField, currentUnit); // Radius uses the same unit
        }

        private void UpdateUIValues()
        {
            if (m_CurrentCard == null) return;
            m_IgnoreValueChange = true;
            
            // Get size data from the generator
            CardSizeData sizeData = m_CurrentCard.CurrentSizeData;
            // Get the display unit from the dropdown
            UnitSpace displayUnit = GetSelectedUnitSpace(); 
            
            int customIndex = m_CardTypeOptions.IndexOf("Custom");
            m_CardTypeDropdown.SetValueWithoutNotify(new int[] { customIndex });
            
            // Convert stored MM values to the current display unit for UI
            m_WidthField.value = UnitConverter.FromMillimeters(sizeData.Width, displayUnit);
            m_HeightField.value = UnitConverter.FromMillimeters(sizeData.Height, displayUnit);
            m_ThicknessField.value = UnitConverter.FromMillimeters(sizeData.Thickness, displayUnit);
            m_CornerRadiusField.value = UnitConverter.FromMillimeters(sizeData.UniformCornerRadius, displayUnit); // Assuming uniform for now
            
            TryMatchPredefinedCardSize(); // Will use the current display unit
            
            // Set dropdown to the correct unit label (already handled by GetSelectedUnitSpace)
            // int unitIndex = m_UnitOptions.IndexOf(UnitConverter.kUnitLabels[displayUnit]);
            // if (unitIndex >= 0)
            // {
            //     m_UnitDropdown.SetValueWithoutNotify(new int[] { unitIndex });
            // }
            
            m_IgnoreValueChange = false;
             // Update formatting after setting values
             UpdateAllFieldFormatting();
        }
        
        private void TryMatchPredefinedCardSize()
        {
            if (m_CurrentCard == null) return;
            
            // Get current dimensions displayed in the UI (which are in displayUnit)
            UnitSpace displayUnit = GetSelectedUnitSpace();
            float currentWidthUI = m_WidthField.value;
            float currentHeightUI = m_HeightField.value;
            float currentThicknessUI = m_ThicknessField.value;

            // Convert UI values to MM for comparison with predefined sizes
            float currentWidthMM = UnitConverter.ToMillimeters(currentWidthUI, displayUnit);
            float currentHeightMM = UnitConverter.ToMillimeters(currentHeightUI, displayUnit);
            float currentThicknessMM = UnitConverter.ToMillimeters(currentThicknessUI, displayUnit);
            
            const float toleranceMM = 0.1f; // Tolerance in MM
            
            bool matched = false;
            foreach (var cardSize in kCardSizes)
            {
                if (cardSize.Key == "Custom") continue;
                    
                Vector3 sizeMM = cardSize.Value; // Predefined sizes are in mm
                if (Mathf.Abs(sizeMM.x - currentWidthMM) < toleranceMM &&
                    Mathf.Abs(sizeMM.y - currentHeightMM) < toleranceMM &&
                    Mathf.Abs(sizeMM.z - currentThicknessMM) < toleranceMM)
                {
                    bool wasIgnoring = m_IgnoreValueChange;
                    m_IgnoreValueChange = true;
                    
                    int cardTypeIndex = m_CardTypeOptions.IndexOf(cardSize.Key);
                    if (cardTypeIndex >= 0)
                    {
                        m_CardTypeDropdown.SetValueWithoutNotify(new int[] { cardTypeIndex });
                        matched = true;
                    }
                    
                    m_IgnoreValueChange = wasIgnoring;
                    break;
                }
            }

            // If no match was found, ensure "Custom" is selected (redundant if default is Custom, but safe)
            if (!matched)
            {
                bool wasIgnoring = m_IgnoreValueChange;
                m_IgnoreValueChange = true;
                int customIndex = m_CardTypeOptions.IndexOf("Custom");
                 if (customIndex >= 0) m_CardTypeDropdown.SetValueWithoutNotify(new int[] { customIndex });
                m_IgnoreValueChange = wasIgnoring;
            }
            
            UpdateCustomFieldsState(GetSelectedCardType());
            
            // No need to call UpdateAllFieldFormatting here, it's called by the calling methods (UpdateUIValues, OnCardTypeChanged)
        }
        
        private void OnCardTypeChanged(ChangeEvent<IEnumerable<int>> evt)
        {
            if (m_IgnoreValueChange || m_CurrentCard == null || !evt.newValue.Any()) return;
                
            string selectedCardType = GetSelectedCardType();
            UpdateCustomFieldsState(selectedCardType);
            
            if (selectedCardType != "Custom" && kCardSizes.TryGetValue(selectedCardType, out Vector3 dimensionsMM))
            {
                // Predefined sizes are in mm
                UnitSpace currentDisplayUnit = GetSelectedUnitSpace(); // The unit currently shown in the UI

                 // Convert target MM dimensions to the *current display unit* for UI fields
                 float targetWidthUI = UnitConverter.FromMillimeters(dimensionsMM.x, currentDisplayUnit);
                 float targetHeightUI = UnitConverter.FromMillimeters(dimensionsMM.y, currentDisplayUnit);
                 float targetThicknessUI = UnitConverter.FromMillimeters(dimensionsMM.z, currentDisplayUnit);
                 // Assuming corner radius matches standard? Or maybe keep it as is?
                 // Let's update corner radius too for consistency, assuming 3mm default for most.
                 float targetCornerRadiusMM = 3.0f; // Default corner radius for predefined
                 float targetCornerRadiusUI = UnitConverter.FromMillimeters(targetCornerRadiusMM, currentDisplayUnit);

                 // --- Update CardSizeData --- 
                 // Create a new CardSizeData struct with the predefined dimensions (in MM)
                 CardSizeData newSizeData = m_CurrentCard.CurrentSizeData; // Start with current data (preserves cutouts etc.)
                 newSizeData.Width = dimensionsMM.x;
                 newSizeData.Height = dimensionsMM.y;
                 newSizeData.Thickness = dimensionsMM.z;
                 newSizeData.UseUniformCorners = true; // Assume predefined use uniform
                 newSizeData.UniformCornerRadius = targetCornerRadiusMM;
                 // Update corner fields for consistency
                 newSizeData.TopLeftCornerRadius = targetCornerRadiusMM;
                 newSizeData.TopRightCornerRadius = targetCornerRadiusMM;
                 newSizeData.BottomLeftCornerRadius = targetCornerRadiusMM;
                 newSizeData.BottomRightCornerRadius = targetCornerRadiusMM;
                 
                 // Apply the new data (this triggers mesh regeneration)
                 m_CurrentCard.ApplySizeData(newSizeData);
                 // --------------------------

                 // --- Update UI fields --- 
                 m_IgnoreValueChange = true;
                 m_WidthField.SetValueWithoutNotify(targetWidthUI);
                 m_HeightField.SetValueWithoutNotify(targetHeightUI);
                 m_ThicknessField.SetValueWithoutNotify(targetThicknessUI);
                 m_CornerRadiusField.SetValueWithoutNotify(targetCornerRadiusUI); // Update radius field too
                 m_IgnoreValueChange = false;
                 // -----------------------
                 
                 // Update formatting after setting predefined values
                 UpdateAllFieldFormatting();
            }
        }
        
        private string GetSelectedCardType()
        {
            if (m_CardTypeDropdown.value == null || !m_CardTypeDropdown.value.Any()) return "Custom";
            int index = m_CardTypeDropdown.value.First();
            if (index >= 0 && index < m_CardTypeOptions.Count) return m_CardTypeOptions[index];
            return "Custom";
        }
        
        private UnitSpace GetSelectedUnitSpace()
        {
             if (m_UnitDropdown.value == null || !m_UnitDropdown.value.Any()) return UnitSpace.Millimeters; // Default
             int index = m_UnitDropdown.value.First();
             if (index >= 0 && index < m_UnitOptions.Count)
             {
                 string selectedLabel = m_UnitOptions[index];
                 foreach (var unitPair in UnitConverter.kUnitLabels)
                 {
                     if (unitPair.Value == selectedLabel) return unitPair.Key;
                 }
             }
             return UnitSpace.Millimeters; // Fallback
        }
        
        private void UpdateCustomFieldsState(string cardType)
        {
            bool enableCustomFields = cardType == "Custom";
            m_WidthField?.SetEnabled(enableCustomFields);
            m_HeightField?.SetEnabled(enableCustomFields);
            m_ThicknessField?.SetEnabled(enableCustomFields);
            m_CornerRadiusField?.SetEnabled(true); // Corner radius always editable
        }
        
        private void OnValueChanged(ChangeEvent<float> evt)
        {
            if (m_IgnoreValueChange || m_CurrentCard == null) return;

            // We assume the value entered in the UI field is in the currently selected display unit
            Unity.AppUI.UI.FloatField field = evt.target as Unity.AppUI.UI.FloatField;
            float newValue = evt.newValue; // Value is already in the selected display unit
            UnitSpace displayUnit = GetSelectedUnitSpace();

            // Convert the new UI value (in displayUnit) to MM for storage
            float newValueMM = UnitConverter.ToMillimeters(newValue, displayUnit);
            
            // Get current size data
            CardSizeData currentSizeData = m_CurrentCard.CurrentSizeData;
            bool requiresRematch = false;
            bool dataChanged = false;

            if (field == m_WidthField)
            {
                 if (!Mathf.Approximately(currentSizeData.Width, newValueMM))
                 {
                    currentSizeData.Width = newValueMM;
                    requiresRematch = true;
                    dataChanged = true;
                 }
            }
            else if (field == m_HeightField)
            {
                 if (!Mathf.Approximately(currentSizeData.Height, newValueMM))
                 {
                     currentSizeData.Height = newValueMM;
                     requiresRematch = true;
                     dataChanged = true;
                 }
            }
            else if (field == m_ThicknessField)
            {
                if (!Mathf.Approximately(currentSizeData.Thickness, newValueMM))
                 {
                     currentSizeData.Thickness = newValueMM;
                     requiresRematch = true;
                     dataChanged = true;
                 }
            }
            else if (field == m_CornerRadiusField)
            {
                 // Assuming uniform corners for now
                 if (!Mathf.Approximately(currentSizeData.UniformCornerRadius, newValueMM))
                 {
                     // Clamp corner radius based on current width/height in MM
                      float maxRadiusMM = Mathf.Min(currentSizeData.Width, currentSizeData.Height) * 0.5f;
                      newValueMM = Mathf.Clamp(newValueMM, 0f, maxRadiusMM); 
                      
                      // Update UI field if clamping occurred
                      float clampedValueUI = UnitConverter.FromMillimeters(newValueMM, displayUnit);
                      if (!Mathf.Approximately(clampedValueUI, newValue))
                      {
                           m_IgnoreValueChange = true;
                           m_CornerRadiusField.SetValueWithoutNotify(clampedValueUI);
                           m_IgnoreValueChange = false;
                      }
                      
                      // Update data if it actually changed after clamping
                      if(!Mathf.Approximately(currentSizeData.UniformCornerRadius, newValueMM))
                      {
                           currentSizeData.UseUniformCorners = true; // Force uniform if this field is edited
                           currentSizeData.UniformCornerRadius = newValueMM;
                           // Also update individual corners for consistency
                           currentSizeData.TopLeftCornerRadius = newValueMM;
                           currentSizeData.TopRightCornerRadius = newValueMM;
                           currentSizeData.BottomLeftCornerRadius = newValueMM;
                           currentSizeData.BottomRightCornerRadius = newValueMM;
                           dataChanged = true;
                           // Corner radius change doesn't affect card type matching (requiresRematch = false)
                      }
                 }
            }
            
            // Apply the updated data back to the generator if anything changed
            if (dataChanged)
            {
                 m_CurrentCard.ApplySizeData(currentSizeData);
            }
             
            // --- Update Card Type Dropdown --- 
            // If a dimension changed, try matching predefined size again
            if (requiresRematch)
            {
                 m_IgnoreValueChange = true;
                 TryMatchPredefinedCardSize();
                 m_IgnoreValueChange = false;
            } else if (requiresRematch) {
                 // If display unit is not mm, changing a dimension means it's now "Custom"
                 m_IgnoreValueChange = true;
                 int customIndex = m_CardTypeOptions.IndexOf("Custom");
                 if (customIndex >= 0) m_CardTypeDropdown.SetValueWithoutNotify(new int[] { customIndex });
                 UpdateCustomFieldsState("Custom");
                 m_IgnoreValueChange = false;
            }
            
            // Update formatting after setting values
            UpdateAllFieldFormatting();
        }
        
        private void OnUnitChanged(ChangeEvent<IEnumerable<int>> evt)
        {
            if (m_IgnoreValueChange || m_CurrentCard == null || !evt.newValue.Any()) return;
                
            UnitSpace previousUnit = GetSelectedUnitSpace(); // Get the unit that *was* displayed
            
             // Force selection of the new unit from dropdown
             m_IgnoreValueChange = true;
             int newUnitIndex = evt.newValue.First();
             m_UnitDropdown.SetValueWithoutNotify(new int[] { newUnitIndex });
             UnitSpace newUnit = GetSelectedUnitSpace(); // The *new* unit selected in the UI
             m_IgnoreValueChange = false;
            
            if (previousUnit == newUnit) return; // No change needed
            
            // Update the formatting for all fields to reflect the new unit
            UpdateAllFieldFormatting();

            // Get current size data (which is stored in MM)
            CardSizeData currentSizeData = m_CurrentCard.CurrentSizeData;

            // Convert MM values to the *new* display unit
            float newWidthUI = UnitConverter.FromMillimeters(currentSizeData.Width, newUnit);
            float newHeightUI = UnitConverter.FromMillimeters(currentSizeData.Height, newUnit);
            float newThicknessUI = UnitConverter.FromMillimeters(currentSizeData.Thickness, newUnit);
            float newRadiusUI = UnitConverter.FromMillimeters(currentSizeData.UniformCornerRadius, newUnit); // Assuming uniform
            
            // Update the UI fields to show the newly converted values
            m_IgnoreValueChange = true;
            m_WidthField.SetValueWithoutNotify(newWidthUI);
            m_HeightField.SetValueWithoutNotify(newHeightUI);
            m_ThicknessField.SetValueWithoutNotify(newThicknessUI);
            m_CornerRadiusField.SetValueWithoutNotify(newRadiusUI);
            m_IgnoreValueChange = false;
            
            // CardSizeData remains unchanged (always MM). MeshGenerator doesn't need update here.
            
            // Try matching card size again based on the new UI values
            // This will call UpdateUIValues which calls UpdateAllFieldFormatting if match found
            TryMatchPredefinedCardSize();
        }
    }
} 
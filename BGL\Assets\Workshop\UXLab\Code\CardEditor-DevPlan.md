# Card Editor Development Plan

This document outlines the development plan, design decisions, and progress for the Board Game Card Editor.

*Working Process: Mark items as [ ] (To Do), [P] (In Progress), [V] (Needs Verification), [X] (Completed). Add notes about decisions or issues encountered.*

## Goal

To create an extremely easy-to-use and intuitive card editor for board games using Unity 6000.0 and UI Toolkit/AppUI. This should allow users to create their own cards, decks, layouts/templates that can be used to create more cards and even deviate from those templates.

Layouts should be able to define elements such as images and text (`CardElement`) and allow easy positioning them both by using mouse/pointer controls and via easy-to-use menus. We should also provide advanced features such as mirroring, pivots/anchoring, duplicating an element to different quadrants and potentially mirroring/flipping those as well. Users should be able to do those things in their preferred unit space (mm, cm, inches, %, etc).

## Rules

Please adhere to these rules:

1.  Update the plan as you go along.
2.  Include filenames / paths where relevant so we can keep track of what's where and what is relevant to what.
3.  Do not mark things as Done until verified. If you've finished work, mark it for verification.
4.  Document issues and gotchas so we can avoid them for later.
5.  Add tasks / QoL suggestions as you go along to ensure we don't forget about them later. Mark them as such or add a note if they're low or high priority, etc.
6.  Do not delete tasks without asking.
7.  Follow coding style (`code-style.mdc`), UI rules (`unity-ui-rules.mdc`, `How to use AppUI - a document for Claude.md`), simplicity (`keep-it-simple.mdc`), and learning practices (`help-yourself-learn.mdc`).

## Current Status ([ ] = To Do, [P] = In Progress, [V] = Needs Verification, [X] = Done)

### Core Systems & Setup

*   [ ] **Project Setup:** Ensure project structure is clean.
*   [X] **Unit System:** Base units defined (`UnitSpace.cs`, `UnitConverter.cs`).
*   [P] **Editor System:** Basic hover/selection exists (`CardEditorSystem.cs`). Needs integration with element manipulation. (Uses `TableSystem` pattern).
    *   [X] UI Host GameObject pattern implemented for managing UI Toolkit elements.
*   [ ] **Input System:** Define how user input (mouse clicks, drags) maps to actions (selection, moving elements, etc.). Integrate with `CardEditorSystem`.
*   [ ] **Serialization:** **(High Priority)** Define strategy for saving/loading card definitions, layouts, and elements (e.g., ScriptableObjects, JSON).
    *   [X] Define `CardDefinition` data structure (holds layout, dimensions, etc.). (`CardDefinition.cs`)
    *   [X] Define `CardLayoutData` structure (holds list of `CardElementData`). (`CardLayoutData.cs`)
    *   [X] Define `CardElementData` base and derived structures (for Image, Text, etc.). (`CardElementData.cs`, `ImageElementData.cs`, `TextElementData.cs`)
    *   [V] Implement saving logic (`CardEditorSystem.SaveSelectedCardDefinition`).
    *   [V] Implement loading logic (`CardEditorSystem.LoadCardDefinition`).
*   [ ] **Undo/Redo:** Plan and implement an undo/redo system for editor actions.

### Camera & Interaction

*   [P] **Camera Controls:** Base orbit/pan/zoom exists (`CameraController.cs`).
    *   [ ] Implement smooth focus on selected card or element (`FocusOn` / `FocusOnPosition`).
    *   [ ] Refine camera controls during direct element manipulation (e.g., disable orbit while dragging).
    *   [ ] Review zoom behavior (perspective vs ortho) for intuitiveness during editing.

### Card Definition & Base

*   [X] **Card Mesh:** Basic rounded-corner card mesh generation (`CardMeshGenerator.cs`).
*   [ ] **Card Mesh Refinement:** Review `CardMeshGenerator.cs` for any optimizations or edge cases (e.g., very small radii, thickness).
*   [P] **Dimension Editor UI Refactor:** Refactor `CardDimensionEditorControl` from MonoBehaviour to `VisualElement`.
    *   [X] Created `CardDimensionEditorControl` inheriting `VisualElement`.
    *   [X] Moved UXML/USS loading and logic into the element class.
    *   [X] `CardEditorSystem` manages instantiation and data context.
    *   [V] Verify AppUI usage and styling consistency.
    *   [V] Verify unit conversion logic is robust.
    *   [ ] Add support for '%' units if desired (requires knowing parent dimensions).
    *   [ ] Consider adding direct pixel unit support.

### Layout & Layers

*   [P] **Layout Management:** `CardLayout.cs` exists for holding `CardElement`s.
    *   [X] Fix element Z-ordering: Ensure `CreateElementFromData` calculates `localPosition.z` based on layer index for correct stacking above the card.
    *   [ ] Implement `RemoveElement` in `CardLayout.cs`.
    *   [ ] Implement `ReorderElement` logic if needed beyond hierarchy manipulation.
    *   [ ] Refine `GetElements` if element nesting becomes a requirement.
*   [P] **Layers Editor UI Refactor:** Refactor `CardLayersEditorControl` from MonoBehaviour to `VisualElement`.
    *   [X] `CardLayersEditorControl` inherits `VisualElement`.
    *   [X] Moved UXML/USS loading and logic into the element class.
    *   [X] `CardEditorSystem` manages instantiation and data context.
    *   [V] Verify AppUI usage (ListView items, buttons). Ensure consistency.
    *   [V] Test list item reordering robustness (`OnListItemsReordered` or drag/drop).
    *   [V] Ensure visibility toggle (`IsVisible`) works reliably for all element types.
    *   [ ] Add 'Duplicate Layer' button/action.

### Element System (Formerly Component System)

*   [X] **Base Element:** `CardElement.cs` exists (Name, IsVisible). (Rename of `CardComponent`).
*   [P] **Image Element:** `ImageCardElement.cs` exists. (Rename of `ImageCardComponent`).
    *   [ ] **Refactor Sizing:** Option B (Chosen): Keep Width/Height properties (in meters) on elements. Ensure all elements implement them consistently. Handle resizing via properties. `CardElementEditorControl` displays values converted to selected units.
        *   [X] `ImageCardElement` uses Width/Height properties (expects meters).
        *   [ ] Implement Width/Height properties/logic for `TextCardElement`.
        *   [X] `CardElementEditorControl` updated to use Width/Height properties via `OnSizeChanged`.
        *   [X] Handle resizing logic (`CardEditorSystem.CalculateNewScaleAndPosition`) updated to set Width/Height properties.
    *   [ ] Remove `RequireChildRenderer.cs` helper class (logic is in `ImageCardElement`).
    *   [ ] Add Texture selection UI to `CardElementEditorControl`.
*   [P] **Text Element:** `TextCardElement.cs` exists. (Rename of `TextCardComponent`).
    *   [ ] Ensure `NotifyElementModified` is called correctly.
    *   [ ] Implement proper sizing/positioning based on the chosen approach (see Image Element task). How does `FontSize` interact with `Transform`?
    *   [ ] Add Font selection UI to `CardElementEditorControl`.
    *   [ ] Add more `TextMeshPro` properties to the editor UI (e.g., Bold, Italic, Alignment options).
*   [ ] **New Element Types:** Plan for potential future elements (e.g., Shapes, Icons, Groups).
*   [P] **Element Editor UI Refactor:** Refactor `CardComponentEditorControl` from MonoBehaviour to `VisualElement`.
    *   [X] `CardElementEditorControl` inherits `VisualElement`.
    *   [X] Moved UXML/USS loading and logic into the element class (`CardElementEditorLayout.uxml`, `CardElementEditorStyles.uss`).
    *   [X] `CardEditorSystem` manages instantiation and data context.
    *   [P] **Refactor Transform Display:** Simplify position/size display. Ensure it correctly reads Width/Height properties (Option B) and converts to selected UI unit. Remove complex relative-to-bottom-left logic if still present.
    *   [X] UI fields update correctly during/after handle drag.
    *   [V] Verify AppUI usage and styling consistency.
    *   [V] Ensure dynamic field generation works correctly for all current and future elements.
    *   [V] Ensure unit switching updates transform fields correctly without altering the actual element state.
    *   [X] Dragging corner handles resizes element (using Width/Height props) and repositions center.
    *   [X] Handles update position during resize drag.
*   [ ] **Rotation Handles:** Implement visual handles for direct rotation.
    *   [ ] Spawn rotation handle.
    *   [ ] Implement rotation drag logic.
    *   [ ] Ensure handles update during rotation drag.
*   [ ] **Snapping:** Implement optional snapping (to grid, to other elements, to centerlines).
*   [ ] **Anchoring/Pivots:** Define how anchoring and pivots affect element positioning and resizing. Implement UI controls and logic.

### Advanced Features

*   [ ] **Mirroring:** Implement Mirror X/Y actions in `CardElementEditorElement`. This should flip scale and adjust position accordingly.
*   [ ] **Duplication:** Implement 'Duplicate to Quadrants' action. Needs logic to calculate positions and potential mirroring based on card dimensions. Requires access to `CardLayout` to add new elements.

### Export & Printing

*   [ ] **Define Export Requirements:** Specify desired output formats (PNG, JPG, PDF?), resolution (DPI), color space.
*   [ ] **Card Rendering:** Implement logic to render a `CardLayout` with its `CardElement`s to a `RenderTexture`.
*   [ ] **Sheet Layout (Optional):** Implement logic to arrange multiple card renders onto a larger sheet (e.g., A4, Letter).
*   [ ] **Export Functionality:** Implement UI and logic to save the rendered card(s) to disk.

### Quality of Life & Polish

*   [ ] **Templates:** Implement saving/loading card layouts as templates (depends on Serialization).
*   [ ] **Error Handling:** Improve error messages (e.g., missing components, failed resource loads) as per `keep-it-simple.mdc`.
*   [ ] **Performance:** Profile and optimize mesh generation and UI updates, especially with many elements. Ensure no GC allocs in critical loops.
*   [ ] **Documentation:** Add code comments for non-trivial parts.
*   [ ] **Testing:** Add unit or integration tests where appropriate.
*   [V] **Zoom Control UI:** Implement a UI slider control (`ZoomControlSystem.cs`, `ZoomControlLayout.uxml`, `ZoomControlStyles.uss`) to adjust camera zoom, synced with `CameraController`.

### Toolbars

*   [X] **Implement `ToolbarSystem.cs`:** Manages creation and basic interactions of editor toolbars.    *   [X] Create UI Host GameObject pattern.
    *   [X] Load UXML/USS for toolbars.
    *   [X] Add basic button click handlers (logging).
    *   [ ] Implement visual toggling for mode/editing toolbars.
*   [X] **Top Toolbar (UXML/USS):** Layout and style the top toolbar (File, Save, etc. - placeholder icons for now).
*   [X] **Mode Toolbar (UXML/USS):** Layout and style the mode selection toolbar (View, Image, Box, Grid, Deck - placeholder icons).
*   [X] **Editing Toolbar (UXML/USS):** Layout and style the element editing toolbar (Select, Move, Zoom, Rotate, etc. - placeholder icons).
*   [X] **Integrate `ToolbarSystem`:** Add instance to `TableManager`.

### Learning & Changelog Tasks

*   [X] **Create `CursorChangeLog.md`:** Initialized.
*   [X] **Create `CursorLearnings.md`:** Initialized.
*   [X] **Create `Changelog.md`:** Initialized.
*   [P] **Update Logs:** Remember to update these files as development progresses.

## Issues & Gotchas

*   Component transform handling (`transform.localScale` vs. properties like Width/Height) needs a clear, consistent decision. Current `ImageCardElement` uses properties, while `CardElementEditorControl` logic for display is complex. **Recommendation:** Use `transform` properties directly for positioning/sizing/rotation. UI converts to display units.
*   Refactoring UI controls to `VisualElement` requires careful handling of data context and event propagation managed by `CardEditorSystem`.
    *   Systems needing UI require a host GameObject pattern (GameObject + UIDocument + PanelSettings) as ScriptableObject systems cannot directly host UI roots.
*   Initial code edits for renaming `CardComponent` -> `CardElement` failed to apply via the tool. Manual review/edit may be required.
*   Resizing handles require careful calculation of the opposite corner anchor and applying both size and position updates to achieve expected corner-anchored scaling. The visual result can sometimes appear more like moving than resizing if the size change is small relative to the position shift.
*   Raycasting for interactions needs correctly defined planes (e.g., using 3 points with correct winding order for the normal) to work reliably.
*   Callbacks (like UI value changes) might execute when system state (like `m_HandleTargetElement`) isn't the expected value from the initial interaction; ensure logic triggered by callbacks fetches or verifies necessary state rather than assuming it persists.
*   AppUI components like `ActionGroup` might require a parent `appui:Panel` to render or behave correctly (e.g., for `direction` attribute). Adding a root panel wrapper solved vertical layout issues for the editing toolbar.
*   *(Add more as they arise)*

## Decisions

*   **Element Naming:** Use `CardElement` instead of `CardComponent`.
*   **System Pattern:** Maintain `TableManager`/`TableSystem` ScriptableObject pattern.
*   **Interaction Loop:** Moved from `TableManager` async task to `TableManagerComponent` MonoBehaviour Update loop for robustness.
*   **UI Controls:** Refactored from `MonoBehaviour` to `VisualElement`.
*   **UI Host:** Systems needing UI will instantiate a host GameObject with UIDocument/PanelSettings.
*   **UI Visibility:** Use USS classes (e.g., `.visible`, `.hidden`) for showing/hiding UI elements where practical, toggled via C#.
*   **Element Sizing:** Use Width/Height properties (in meters) on `CardElement` derived classes (Option B). UI and Handles will interact with these properties.
*   **Handle Updates:** Handles update position based on element changes triggered by direct handle manipulation or UI field edits.
*   *(Record other decisions here, e.g., chosen transform handling approach, serialization format)*


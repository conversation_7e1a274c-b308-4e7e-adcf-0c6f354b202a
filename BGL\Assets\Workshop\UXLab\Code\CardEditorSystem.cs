using UnityEngine;
using System.Threading;
using HighlightPlus;
using UnityEngine.UIElements;
using System.Collections.Generic;
using System.Linq;
using Workshop.UXLab.Data;
using Workshop.UXLab.Handles;
using Unity.AppUI.Core;
using Unity.AppUI.UI;
using LengthUnit = Workshop.UXLab.Data.LengthUnit; // Needed for Panel

namespace Workshop.UXLab
{
    [CreateAssetMenu(fileName = "CardEditorSystem", menuName = "Workshop/UXLab/Systems/CardEditorSystem")]
    public class CardEditorSystem : TableSystem
    {
        [Header("Hover Settings")]
        [SerializeField] private Color m_HoverHighlightColor = new Color(0.8f, 0.8f, 0.2f, 0.3f);
        [SerializeField] private Color m_SelectedHighlightColor = new Color(0.2f, 0.8f, 0.2f, 0.5f);

        [Header("UI Settings")]
        [SerializeField] private bool m_EnableCardEditorUI = true;

        private GameObject m_SelectedComponent;
        private GameObject m_HoveredComponent;

        // UI Root
        private GameObject m_UIHostObject; // GameObject to host the UIDocument
        private VisualElement m_RootUIElement; // Root element from the hosted UIDocument

        // --- UI References (Updated) ---
        // private CardDimensionEditorControl m_DimensionEditorElement; // Removed
        // private CardLayersEditorControl m_LayersEditorControl; // Removed
        // private CardElementEditorControl m_ElementEditorControl; // Removed
        private IntegratedCardEditorControl m_IntegratedEditorControl; // Added

        // Drag State
        private CardElement m_DraggedElement; // The element currently being dragged
        private Plane m_CardDragPlane; // The plane to raycast against during drag
        private Vector3 m_DragOffset; // Offset from element pivot to initial click point
        private bool m_IsDraggingElement = false; // Flag for element drag state
        private float m_LastElementClickTime = 0f; // Time when element was last clicked
        private const float DRAG_DELAY = 0.3f; // 300ms delay before allowing drag

        // Handle Drag State (New)
        private GameObject m_DraggedHandle;
        private HandleType m_DraggedHandleType;
        private CardElement m_HandleTargetElement;
        private bool m_IsDraggingHandle = false;
        private Vector3 m_HandleDragStartElementScale;
        private Vector3 m_HandleDragStartElementPosition;
        private float m_HandleDragStartElementRotationZ;
        private Vector3 m_HandleDragStartOppositeCornerWorld;
        private float m_HandleDragStartAngleOffset;

        // Events
        public delegate void ElementHoverExitHandler(GameObject element);
        public event ElementHoverExitHandler OnElementHoverExit;

        public delegate void CardSelectedHandler(GameObject cardObject, CardMeshGenerator cardMesh);
        public event CardSelectedHandler OnCardSelected;

        public delegate void CardDeselectedHandler(GameObject cardObject);
        public event CardDeselectedHandler OnCardDeselected;

        public GameObject SelectedComponent => m_SelectedComponent;

        public bool IsCardSelected => m_SelectedComponent != null && m_SelectedComponent.GetComponent<CardMeshGenerator>() != null;

        public CardMeshGenerator SelectedCardMesh => m_SelectedComponent?.GetComponent<CardMeshGenerator>();

        [Header("Legacy Handle Settings")] // Legacy settings
        private List<GameObject> m_ActiveHandles = new List<GameObject>(); // Track active handles (legacy)

        // Reference to the handle system
        private HandleSystem m_HandleSystem;

        public override void Init(TableManager tableManager)
        {
            base.Init(tableManager);

            // Subscribe to hover and click events
            m_TableManager.OnComponentHovered += HandleComponentHovered;
            m_TableManager.OnComponentClicked += HandleComponentClicked;
            m_TableManager.OnBlankClicked += HandleBlankClicked;

            // Subscribe to the manager's update event
            m_TableManager.OnUpdate += ProcessInteractionsUpdate;

            // Ensure CardRenderSystem is available
            CardRenderSystem renderSystem = tableManager.GetSystem<CardRenderSystem>();
            if (renderSystem == null)
            {
                // Create and add CardRenderSystem if not found
                renderSystem = ScriptableObject.CreateInstance<CardRenderSystem>();
                renderSystem.name = "CardRenderSystem";
                tableManager.AddSystem(renderSystem);
                Debug.Log("CardRenderSystem created and added to TableManager");
            }

            // Get reference to the HandleSystem
            m_HandleSystem = tableManager.GetSystem<HandleSystem>();
            if (m_HandleSystem == null)
            {
                Debug.LogWarning("HandleSystem not found. Handle functionality will be disabled.");
            }

            // Set up UI if enabled
            if (m_EnableCardEditorUI)
            {
                SetupEditorUIs();
            }
        }

        private void SetupEditorUIs()
        {
            // 1. Create UI Host GameObject and UIDocument (Same as before)
            if (m_UIHostObject == null)
            {
                m_UIHostObject = new GameObject("CardEditorUIHost");
                var uiDocument = m_UIHostObject.AddComponent<UIDocument>();

                if (m_TableManager.UIPanelSettings != null)
                {
                    uiDocument.panelSettings = m_TableManager.UIPanelSettings;
                }
                else
                {
                    Debug.LogError("CardEditorSystem: TableManager.UIPanelSettings is null. Cannot configure UI host.");
                    Object.Destroy(m_UIHostObject);
                    m_UIHostObject = null;
                    return;
                }

                 m_RootUIElement = uiDocument.rootVisualElement;
                 if(m_RootUIElement == null) {
                     Debug.LogError("CardEditorSystem: Failed to get rootVisualElement from created UIDocument.");
                     Object.Destroy(m_UIHostObject);
                     m_UIHostObject = null;
                     return;
                 }
                 m_RootUIElement.AddToClassList("card-editor-ui-root");
            }
            else
            {
                m_RootUIElement = m_UIHostObject.GetComponent<UIDocument>()?.rootVisualElement;
                if (m_RootUIElement == null)
                {
                     Debug.LogError("CardEditorSystem: UI Host exists but failed to get rootVisualElement.");
                     return;
                }
            }

            // 2. Setup Integrated UI Panel
            if (m_IntegratedEditorControl == null)
            {
                // Create the AppUI Panel first to act as a root container
                var appuiPanel = new Panel
                {
                    name = "CardEditorAppUIRootPanel",
                    style =
                    {
                        position = Position.Absolute,
                        top = 0, left = 0, right = 0, bottom = 0,
                        backgroundColor = Color.clear // Transparent background
                    },
                    pickingMode = PickingMode.Ignore // Let clicks pass through to game view/other UI
                };
                // appuiPanel.AddToClassList("appui-theme-dark"); // Theme applied by parent or PanelSettings?
                // appuiPanel.style.flexGrow = 1; // Not needed with absolute positioning
                m_RootUIElement.Add(appuiPanel);

                // Now create and add the integrated control *inside* the AppUI Panel
                m_IntegratedEditorControl = new IntegratedCardEditorControl();
                appuiPanel.Add(m_IntegratedEditorControl);

                // Provide necessary actions
                m_IntegratedEditorControl.SetupActions(ShowHandlesForElement, ClearHandles, UpdateActiveHandles);

                // Subscribe to events from the integrated panel
                m_IntegratedEditorControl.OnElementSelected += HandleIntegratedElementSelected; // New handler
                m_IntegratedEditorControl.OnLayoutChanged += HandleIntegratedLayoutChanged; // New handler
                m_IntegratedEditorControl.OnElementModified += HandleIntegratedElementModified; // New handler
            }
        }

        // Removed SetupDimensionEditorUI, SetupElementEditorUI, SetupLayersEditorUI methods

        public override void Shutdown()
        {
            if (!m_IsInitialized)
                return;

            // Unsubscribe from events
            if (m_TableManager != null)
            {
                m_TableManager.OnComponentHovered -= HandleComponentHovered;
                m_TableManager.OnComponentClicked -= HandleComponentClicked;
                m_TableManager.OnBlankClicked -= HandleBlankClicked;
                m_TableManager.OnUpdate -= ProcessInteractionsUpdate;
            }

            // Clean up Integrated UI
            if (m_IntegratedEditorControl != null)
            {
                // Unsubscribe from events
                m_IntegratedEditorControl.OnElementSelected -= HandleIntegratedElementSelected;
                m_IntegratedEditorControl.OnLayoutChanged -= HandleIntegratedLayoutChanged;
                m_IntegratedEditorControl.OnElementModified -= HandleIntegratedElementModified;

                // Clear active layout (optional, good practice)
                m_IntegratedEditorControl.ClearActiveCardLayout();

                // Element will be removed when host object is destroyed
                m_IntegratedEditorControl = null;
            }

            // Destroy the UI Host GameObject
            if (m_UIHostObject != null)
            {
                 Object.Destroy(m_UIHostObject);
                 m_UIHostObject = null;
                 m_RootUIElement = null;
            }

            // Clean up selected and hovered components
            m_SelectedComponent = null;
            m_HoveredComponent = null;

            // Clean up handle system
            if (m_HandleSystem != null)
            {
                m_HandleSystem.ClearHandles();
            }

            base.Shutdown();
        }

        private void HandleComponentHovered(GameObject component)
        {
            // If we're getting null and had a previous hover, this is a hover exit
            if (component == null && m_HoveredComponent != null)
            {
                GameObject previousHover = m_HoveredComponent;
                m_HoveredComponent = null;

                // Trigger hover exit event
                OnElementHoverExit?.Invoke(previousHover);
                return;
            }

            // If it's the same component, do nothing
            if (component == m_HoveredComponent)
                return;

            // If we had a previous hover that's not the selected one, unhighlight it
            if (m_HoveredComponent != null && m_HoveredComponent != m_SelectedComponent)
            {
                // Trigger hover exit event for the previous component
                OnElementHoverExit?.Invoke(m_HoveredComponent);
            }

            m_HoveredComponent = component;
        }

        private void HandleComponentClicked(GameObject componentGO)
        {
            // Get CardElement component if it exists
            // Note: CardElement is not a MonoBehaviour, so we can't use GetComponent
            // Instead, we need to check if the GameObject has a CardLayout and find the element by name
            CardElement clickedElement = null;
            CardLayout cardLayout = componentGO?.GetComponent<CardLayout>();
            if (cardLayout != null)
            {
                // This is a card layout, not an element
                // We'll handle this in the card layout case below
            }
            // Check if a handle was clicked
            HandleIdentifier clickedHandle = componentGO?.GetComponent<HandleIdentifier>();

            // --- Handle Drag Initiation using the handle system ---
            if (clickedHandle != null && m_HandleSystem != null) // Check if it's a handle and we have a handle system
            {
                Debug.Log($"Clicked on handle: {clickedHandle.Type}");

                // Get the current target element from the handle system
                CardElement targetElement = m_HandleSystem.GetCurrentTargetElement();
                if (targetElement != null)
                {
                    // Get the card layout for the drag plane
                    CardLayout parentLayout = CardElementHelper.GetCardLayout(targetElement);
                    if (parentLayout != null)
                    {
                        // Create a ray from the camera to the mouse position
                        Ray ray = m_TableManager.CameraController.Camera.ScreenPointToRay(Input.mousePosition);

                        // Use the handle system to handle the click
                        bool handleClicked = m_HandleSystem.HandleClicked(componentGO, ray, parentLayout.transform);

                        if (handleClicked)
                        {
                            // Set dragging state
                            m_IsDraggingHandle = true;
                            m_IsDraggingElement = false; // Not dragging the element itself
                            m_DraggedElement = null;

                            // Disable camera pan
                            m_TableManager.CameraController?.SetMousePanEnabled(false);

                            // Stop element selection/deselection logic if we clicked a handle
                            return;
                        }
                    }
                    else
                    {
                        Debug.LogError("Cannot drag handle: Parent CardLayout not found.");
                        return; // Don't proceed with element selection logic below
                    }
                }
                else
                {
                    Debug.LogWarning("Clicked a handle but no target element is set in the handle system.");
                }
            }
            // --- End Handle Drag Initiation ---


            // If we have a selected component and it's not the one we just clicked, clean it up
            if (m_SelectedComponent != null && m_SelectedComponent != componentGO)
            {
                GameObject previousSelection = m_SelectedComponent;

                // Trigger deselection event if it was a card
                if (previousSelection.GetComponent<CardMeshGenerator>() != null)
                {
                    OnCardDeselected?.Invoke(previousSelection);
                    // Hide Integrated UI Panel
                    if (m_EnableCardEditorUI)
                    {
                        m_IntegratedEditorControl?.ClearActiveCardLayout();
                    }
                }
                m_SelectedComponent = null;
                ClearHandles(); // Hide handles on deselect
            }

            // Toggle selection if clicking the same element
            if (m_SelectedComponent == componentGO)
            {
                // Trigger deselection event if it's a card
                if (componentGO.GetComponent<CardMeshGenerator>() != null)
                {
                    OnCardDeselected?.Invoke(componentGO);
                    // Hide Integrated UI Panel
                    if (m_EnableCardEditorUI)
                    {
                        m_IntegratedEditorControl?.ClearActiveCardLayout();
                    }
                }
                m_SelectedComponent = null;
                m_DraggedElement = null;
                m_IsDraggingElement = false;
                ClearHandles();
            }
            else // Select the new element
            {
                CardMeshGenerator clickedCardMesh = componentGO.GetComponent<CardMeshGenerator>();
                CardLayout clickedCardLayout = componentGO.GetComponent<CardLayout>();
                if (clickedCardMesh == null && clickedCardLayout == null)
                {
                    // Clicked on something managed, but not a card (e.g., the table)
                    // Treat it like clicking blank space - deselect current selection.
                    HandleBlankClicked();
                    return; // Stop processing this click further
                }

                m_SelectedComponent = componentGO;

                // Clear previous handle target immediately
                m_HandleTargetElement = null;
                ClearHandles(); // Ensure handles are cleared when selecting a new card/element initially

                CardMeshGenerator cardMesh = m_SelectedComponent.GetComponent<CardMeshGenerator>();
                CardLayout selectedCardLayout = m_SelectedComponent.GetComponent<CardLayout>();

                // If it's a card, show the Integrated UI
                if (cardMesh != null && selectedCardLayout != null)
                {
                    OnCardSelected?.Invoke(m_SelectedComponent, cardMesh);
                    if (m_EnableCardEditorUI)
                    {
                        m_IntegratedEditorControl?.SetActiveCardLayout(selectedCardLayout);
                        // Note: Selection within the integrated control handles showing element properties and handles
                    }
                }
                // Note: We no longer handle clicking on CardElement directly since it's not a MonoBehaviour
                // Instead, we handle clicking on the card layout and let the UI handle element selection
                // This section is kept for reference but is no longer used

                else // Clicked something else (handle, etc.) or component is missing layout
                {
                    if (m_EnableCardEditorUI) m_IntegratedEditorControl?.ClearActiveCardLayout();
                }
            }

            // Handle dragging (Keep this logic)
            if (m_DraggedElement != null) HandleElementDrag();
            else if (m_DraggedHandle != null) HandleHandleDrag();
        }

        private void HandleBlankClicked()
        {
            // Debug log to track blank clicks
            Debug.Log("CardEditorSystem: HandleBlankClicked called");

            // Reset drag state to prevent unwanted movement
            m_DraggedElement = null;
            m_IsDraggingElement = false;
            m_DraggedHandle = null;
            m_IsDraggingHandle = false;

            if (m_SelectedComponent != null)
            {
                GameObject previousSelection = m_SelectedComponent;
                m_SelectedComponent = null;
                ClearHandles(); // Hide handles on blank click

                // Trigger deselection event if it was a card
                if (previousSelection.GetComponent<CardMeshGenerator>() != null)
                {
                    OnCardDeselected?.Invoke(previousSelection);
                    // Hide Integrated UI Panel
                    if (m_EnableCardEditorUI)
                    {
                        m_IntegratedEditorControl?.ClearActiveCardLayout();
                    }
                }
            }

            m_DraggedElement = null;
            m_IsDraggingElement = false;
            m_DraggedHandle = null; // Ensure handle drag state is cleared
            m_IsDraggingHandle = false;
            m_HandleTargetElement = null;
        }

        /*private void SetHighlighted(GameObject component, bool isHighlighted)
        {
            if (component == null)
                return;

            // Check if the component has a HighlightEffect
            var highlightEffect = component.GetComponent<HighlightEffect>();
            if (highlightEffect != null)
            {
                highlightEffect.highlighted = isHighlighted;

                // Set the highlight color based on whether this is a selection or hover
                if (isHighlighted)
                {
                    if (component == m_SelectedComponent)
                    {
                        // Use selection color if this is the selected component
                        //highlightEffect.outlineColor = m_SelectedHighlightColor;
                    }
                    else
                    {
                        // Use hover color for regular hover
                        //highlightEffect.outlineColor = m_HoverHighlightColor;
                    }
                }
            }
            else
            {
                // Fallback logging if no highlight effect is found
                if (isHighlighted)
                {

                }
            }
        }*/

        // --- UI Panel Event Handlers (Updated for Integrated Control) ---

        private void HandleIntegratedElementSelected(CardElement selectedElement)
        {
            // This handler is called by the UI when an element is selected *in the list*.
            // The UI itself handles showing/hiding properties.
            // We mainly need to manage the handle target and potentially show handles.
            if (selectedElement != null)
            {
                // Debug.Log($"[System] Integrated UI selected element: {selectedElement.ElementName}");
                m_HandleTargetElement = selectedElement;

                // Ensure the element is rendered with the UI Toolkit system
                EnsureElementRendered(selectedElement);

                // Handles are shown via the action passed to the UI's SetupActions
                // m_RequestShowHandlesAction is called by the UI's SelectElement method internally.
            }
            else
            {
                // Debug.Log("[System] Integrated UI deselected element.");
                m_HandleTargetElement = null;

                // Handles are cleared via the action passed to the UI's SetupActions
                // m_RequestClearHandlesAction is called by the UI's DeselectElement method internally.
            }
        }

        private void HandleIntegratedLayoutChanged()
        {
            // Called when elements are added, removed, reordered, or visibility changes in the UI.
            Debug.Log("[System] Integrated UI reported layout change.");
            // TODO: Mark card as dirty, potentially trigger auto-save or update previews

            // Get the selected card's mesh generator and tell it to update
            CardMeshGenerator meshGen = SelectedCardMesh; // Use the property
            if (meshGen != null)
            {
                meshGen.GenerateMesh(); // Assuming GenerateMesh() redraws based on CurrentSizeData
                // If handles are shown for an element, update them as card size affects element positions
                if (m_HandleTargetElement != null)
                {
                    UpdateActiveHandles(m_HandleTargetElement);
                }

                // Ensure the card is rendered with the UI Toolkit system
                CardRenderSystem renderSystem = m_TableManager.GetSystem<CardRenderSystem>();
                if (renderSystem != null)
                {
                    CardRenderTarget renderTarget = renderSystem.GetRenderTarget(meshGen);
                    if (renderTarget != null)
                    {
                        // The CardRenderTarget constructor already applies the texture to the card material
                        // But we can force an update if needed
                        Renderer renderer = meshGen.GetComponent<Renderer>();
                        if (renderer != null && renderer.material != null && renderTarget.RenderTexture != null)
                        {
                            renderer.material.mainTexture = renderTarget.RenderTexture;
                        }
                    }
                }
            }
            else
            {
                Debug.LogWarning("HandleIntegratedLayoutChanged called, but SelectedCardMesh is null.");
            }
        }

        private void HandleIntegratedElementModified(CardElement modifiedElement)
        {
            // Called when element properties (name, transform, tint, text, etc.) are changed in the UI.
            Debug.Log($"[System] Integrated UI modified element: {modifiedElement.ElementName}");

            // Ensure the element is rendered with the UI Toolkit system
            EnsureElementRendered(modifiedElement);

            // Update handles if this is the current target element
            if (modifiedElement == m_HandleTargetElement && m_HandleSystem != null)
            {
                m_HandleSystem.UpdateHandles();
            }

            // TODO: Mark card as dirty
        }

        /// <summary>
        /// Ensures the element is rendered with the UI Toolkit system.
        /// </summary>
        private void EnsureElementRendered(CardElement element)
        {
            if (element == null) return;

            // Use the handle system to ensure the element is rendered
            if (m_HandleSystem != null)
            {
                m_HandleSystem.EnsureElementRendered(element);
            }
            else
            {
                // Legacy fallback
                // Get the parent card layout
                CardLayout cardLayout = CardElementHelper.GetCardLayout(element);
                if (cardLayout == null) return;

                // Get the card mesh generator
                CardMeshGenerator cardMesh = cardLayout.GetComponent<CardMeshGenerator>();
                if (cardMesh == null) return;

                // Get the CardRenderSystem
                CardRenderSystem renderSystem = m_TableManager.GetSystem<CardRenderSystem>();
                if (renderSystem == null) return;

                // Get or create a render target for the card
                CardRenderTarget renderTarget = renderSystem.GetRenderTarget(cardMesh);
                if (renderTarget == null) return;

                // The CardRenderTarget constructor already applies the texture to the card material
                // But we can force an update if needed
                Renderer renderer = cardMesh.GetComponent<Renderer>();
                if (renderer != null && renderer.material != null && renderTarget.RenderTexture != null)
                {
                    renderer.material.mainTexture = renderTarget.RenderTexture;
                }
            }
        }

        /// <summary>
        /// Called every frame via TableManager.OnUpdate to process ongoing interactions.
        /// </summary>
        private void ProcessInteractionsUpdate()
        {
            // Only process interactions if we have something to interact with
            if (!m_IsDraggingElement && !m_IsDraggingHandle && m_DraggedElement == null && m_DraggedHandle == null)
            {
                return; // Nothing to process
            }

            // Debug log to track interaction state
            Debug.Log($"ProcessInteractionsUpdate - IsDraggingElement: {m_IsDraggingElement}, " +
                      $"IsDraggingHandle: {m_IsDraggingHandle}, " +
                      $"DraggedElement: {(m_DraggedElement != null ? m_DraggedElement.ElementName : "null")}, " +
                      $"DraggedHandle: {(m_DraggedHandle != null ? m_DraggedHandle.name : "null")}, " +
                      $"MouseButton0: {Input.GetMouseButton(0)}, " +
                      $"MouseButtonUp0: {Input.GetMouseButtonUp(0)}");

            // Handle element dragging
            if (m_DraggedElement != null && m_IsDraggingElement)
            {
                HandleElementDrag();
            }

            // Handle HANDLE dragging
            if (m_IsDraggingHandle) // Only process if we're actually dragging
            {
                // Check for mouse release FIRST
                if (Input.GetMouseButtonUp(0)) // This is FALSE during drag
                {
                    // End dragging
                    // Re-enable camera pan
                    m_TableManager.CameraController?.SetMousePanEnabled(true);

                    // Use the handle system to handle release
                    if (m_HandleSystem != null && m_HandleSystem.IsHandleDragging())
                    {
                        m_HandleSystem.HandleReleased();
                    }

                    // Reset drag state
                    m_DraggedHandle = null;
                    m_IsDraggingHandle = false;

                    Debug.Log("Handle drag ended");
                }
                // If mouse is still held down, continue dragging
                else if (Input.GetMouseButton(0))
                {
                    HandleHandleDrag();
                }
            }
        }

        private void HandleElementDrag()
        {
            // Check if the left mouse button is held down
            if (Input.GetMouseButton(0))
            {
                 // Start dragging only if mouse moved significantly and enough time has passed since click
                 if (!m_IsDraggingElement && (Time.time - m_LastElementClickTime) > DRAG_DELAY)
                 {
                     m_IsDraggingElement = true;
                     // Disable camera panning during drag
                     m_TableManager.CameraController?.SetMousePanEnabled(false);
                     Debug.Log($"Started dragging element: {m_DraggedElement.ElementName}");
                 }

                 if (m_IsDraggingElement)
                 {
                     // Raycast against the card plane
                     Ray ray = m_TableManager.CameraController.Camera.ScreenPointToRay(Input.mousePosition);
                     if (m_CardDragPlane.Raycast(ray, out float enter))
                     {
                         Vector3 hitPointWorld = ray.GetPoint(enter);

                         // Apply offset to find target world position
                         Vector3 targetWorldPosition = hitPointWorld + m_DragOffset;

                         // Convert world position to the parent CardLayout's local space
                         CardLayout parentLayout = CardElementHelper.GetCardLayout(m_DraggedElement);
                         if (parentLayout != null)
                         {
                            Vector3 targetLocalPosition = parentLayout.transform.InverseTransformPoint(targetWorldPosition);

                             // Keep original Z offset relative to the layout
                             targetLocalPosition.z = parentLayout.transform.InverseTransformPoint(CardElementHelper.GetTransform(m_DraggedElement).position).z;

                             // Update the transform position directly
                             Transform elementTransform = CardElementHelper.GetTransform(m_DraggedElement);
                             if (elementTransform != null)
                             {
                                 elementTransform.localPosition = targetLocalPosition;

                                 // Update the PositionMM property to match the transform position
                                 Vector2 newPositionMM = new Vector2(
                                     UnitConverter.MetersToMm(targetLocalPosition.x),
                                     UnitConverter.MetersToMm(targetLocalPosition.y)
                                 );
                                 m_DraggedElement.PositionMM = newPositionMM;

                                 // Debug log to verify the calculation
                                 Debug.Log($"Dragging element: LocalPos={targetLocalPosition}m, PositionMM={newPositionMM}mm");
                             }

                             // Update the UI element to refresh the visual representation
                             m_DraggedElement.Update();

                             // Ensure the element is rendered with the UI Toolkit system
                             EnsureElementRendered(m_DraggedElement);

                             // Update the drag offset based on the new position to avoid drift
                             m_DragOffset = CardElementHelper.GetTransform(m_DraggedElement).position - hitPointWorld;

                             // Update UI fields
                             m_IntegratedEditorControl?.UpdateDisplayedTransformFields(m_DraggedElement);
                         }
                     }
                 }
            }
            else if (Input.GetMouseButtonUp(0)) // Only handle mouse button up, not any other condition
            {
                 // End dragging only if we were actually dragging
                 if (m_IsDraggingElement)
                 {
                     // Re-enable camera pan
                     m_TableManager.CameraController?.SetMousePanEnabled(true);

                     // Notify layout
                     if (m_DraggedElement != null)
                     {
                         CardLayout layout = CardElementHelper.GetCardLayout(m_DraggedElement);
                         if (layout != null)
                         {
                             layout.NotifyLayoutModified(null); // Pass null for general layout modification
                         }

                         Debug.Log($"Finished dragging {m_DraggedElement.ElementName}");
                     }

                     // Reset drag state
                     m_DraggedElement = null;
                     m_IsDraggingElement = false;
                 }
            }
        }
        // -------------------------------------

        // --- Handle Management ---
        /// <summary>
        /// Shows resize handles for the specified element.
        /// </summary>
        public void ShowHandlesForElement(CardElement element)
        {
            // Clear any existing handles
            ClearHandles();

            if (element == null)
            {
                Debug.LogWarning("Cannot show handles: Element is null.");
                return;
            }

            // Get the card layout transform - this is where we'll parent the handles
            Transform cardTransform = CardElementHelper.GetTransform(element);
            if (cardTransform == null)
            {
                Debug.LogError("Cannot show handles: Card transform is null.");
                return;
            }

            // Check if the handle system is available
            if (m_HandleSystem == null)
            {
                Debug.LogWarning("Cannot show handles: HandleSystem is not available.");
                return;
            }

            // Get the default handle types from the handle system
            HandleType[] handleTypes = m_HandleSystem.GetDefaultHandleTypes();

            // Use the handle system to show handles
            m_HandleSystem.ShowHandles(element, cardTransform, handleTypes);

            // Store the target element
            m_HandleTargetElement = element;

            Debug.Log($"Showing handles for {element.ElementName}");
        }

        /// <summary>
        /// Hides any currently visible resize handles.
        /// </summary>
        public void ClearHandles()
        {
            // Clear legacy handles
            if (m_ActiveHandles.Count > 0) {
                 Debug.Log($"Hiding {m_ActiveHandles.Count} legacy handles.");
                 foreach (var handle in m_ActiveHandles)
                 {
                     if (handle != null) Object.Destroy(handle);
                 }
                 m_ActiveHandles.Clear();
            }

            // Clear handles from the new system
            if (m_HandleSystem != null)
            {
                m_HandleSystem.ClearHandles();
            }
        }
        // -------------------------

        private void HandleHandleDrag()
        {
            // Create a ray from the camera to the mouse position
            Ray ray = m_TableManager.CameraController.Camera.ScreenPointToRay(Input.mousePosition);

            // Use the handle system to handle the drag
            if (m_HandleSystem != null && m_HandleSystem.IsHandleDragging())
            {
                bool handled = m_HandleSystem.HandleDragged(ray);

                if (handled)
                {
                    // Get the current target element from the handle system
                    CardElement targetElement = m_HandleSystem.GetCurrentTargetElement();

                    // Update UI fields
                    if (targetElement != null)
                    {
                        m_IntegratedEditorControl?.UpdateDisplayedTransformFields(targetElement);
                    }
                }
            }
            else if (m_HandleTargetElement != null)
            {
                // Legacy fallback - should not be needed anymore
                Debug.LogWarning("Using legacy handle drag logic. This should be replaced with the HandleSystem.");

                bool raycastHit = m_CardDragPlane.Raycast(ray, out float enter);
                if (raycastHit)
                {
                    Vector3 hitPointWorld = ray.GetPoint(enter);
                    Vector3 targetHandleWorldPos = hitPointWorld + m_DragOffset;

                    if (m_DraggedHandleType == HandleType.Rotate)
                    {
                        // Handle rotation differently
                        HandleRotation(targetHandleWorldPos);
                    }
                    else
                    {
                        // Calculate new scale and position based on handle movement
                        CalculateNewScaleAndPosition(targetHandleWorldPos);
                    }

                    // Update handle positions visually after scaling/moving parent
                    UpdateHandlePositions(m_HandleTargetElement);

                    // Update UI fields
                    m_IntegratedEditorControl?.UpdateDisplayedTransformFields(m_HandleTargetElement);
                }
            }
        }

        /// <summary>
        /// Handles rotation of an element based on the rotation handle position.
        /// </summary>
        private void HandleRotation(Vector3 targetHandleWorldPos)
        {
            if (m_HandleTargetElement == null) return;

            // Find the handles parent
            GameObject handlesParent = null;
            foreach (GameObject handle in m_ActiveHandles)
            {
                if (handle != null && handle.name == "ElementHandles")
                {
                    handlesParent = handle;
                    break;
                }
            }

            if (handlesParent == null)
            {
                Debug.LogWarning("[HandleRotation] Handles parent not found.");
                return;
            }

            // Get the element's center in world space (using the handles parent position)
            Vector3 elementCenterWorld = handlesParent.transform.position;

            // Calculate the vector from the center to the target handle position
            Vector3 centerToHandle = targetHandleWorldPos - elementCenterWorld;

            // Calculate the angle in degrees (in the XY plane)
            float angle = Mathf.Atan2(centerToHandle.y, centerToHandle.x) * Mathf.Rad2Deg;

            // Adjust angle to match Unity's rotation system (0 degrees is up, clockwise rotation)
            angle = 90f - angle;

            // Normalize the angle to 0-360 range
            while (angle < 0) angle += 360f;
            while (angle >= 360f) angle -= 360f;

            // Set the rotation on the CardElement
            m_HandleTargetElement.RotationDegrees = angle;

            // Update the UI element to refresh the visual representation
            m_HandleTargetElement.Update();

            // Update the handles parent rotation
            handlesParent.transform.localRotation = Quaternion.Euler(0, 0, angle);

            // Ensure the element is rendered with the UI Toolkit system
            EnsureElementRendered(m_HandleTargetElement);

            // Log the rotation for debugging
            Debug.Log($"Rotated element to {angle} degrees");
        }

        private void CalculateNewScaleAndPosition(Vector3 targetHandleWorldPos)
        {
            if (m_HandleTargetElement == null)
                return;

            // Find the handles parent
            GameObject handlesParent = null;
            foreach (GameObject handle in m_ActiveHandles)
            {
                if (handle != null && handle.name == "ElementHandles")
                {
                    handlesParent = handle;
                    break;
                }
            }

            if (handlesParent == null)
            {
                Debug.LogWarning("[CalculateNewScaleAndPosition] Handles parent not found.");
                return;
            }

            // Get the card layout transform, which defines the card's local coordinate system.
            // The origin of this transform is the card's center.
            Transform cardLayoutTransform = CardElementHelper.GetCardLayout(m_HandleTargetElement)?.transform;
            if (cardLayoutTransform == null)
            {
                Debug.LogWarning("[CalculateNewScaleAndPosition] CardLayout transform not found.");
                return;
            }

            // Convert target handle position and opposite corner to card's local space.
            Vector3 targetHandleLocalPos = cardLayoutTransform.InverseTransformPoint(targetHandleWorldPos);
            Vector3 oppositeCornerLocalPos = cardLayoutTransform.InverseTransformPoint(m_HandleDragStartOppositeCornerWorld);

            // Calculate the new center of the element in the card's local space.
            // This is the midpoint between the dragged handle's local position and the opposite corner's local position.
            Vector3 newElementCenterLocal = (targetHandleLocalPos + oppositeCornerLocalPos) * 0.5f;
            // Preserve the element's Z offset relative to the card plane if necessary, or set it consistently.
            // For UI elements, a small negative Z is often used to ensure they render on top of the card surface.
            newElementCenterLocal.z = -0.0001f; // Consistent small Z offset

            // Calculate the new size of the element in the card's local space.
            // The size vector is the difference between the handle and its opposite corner.
            Vector3 sizeVectorLocal = targetHandleLocalPos - oppositeCornerLocalPos;
            float newWidthMeters = Mathf.Max(Mathf.Abs(sizeVectorLocal.x), 0.001f); // Min width 1mm
            float newHeightMeters = Mathf.Max(Mathf.Abs(sizeVectorLocal.y), 0.001f); // Min height 1mm

            // Convert dimensions from meters to millimeters for CardElement properties.
            float newWidthMM = UnitConverter.MetersToMm(newWidthMeters);
            float newHeightMM = UnitConverter.MetersToMm(newHeightMeters);

            // The newPositionMM for CardElement is its center relative to the card's center, in millimeters.
            Vector2 newPositionMM = new Vector2(
                UnitConverter.MetersToMm(newElementCenterLocal.x),
                UnitConverter.MetersToMm(newElementCenterLocal.y)
            );

            // Set properties on the CardElement
            m_HandleTargetElement.WidthMM = newWidthMM;
            m_HandleTargetElement.HeightMM = newHeightMM;
            m_HandleTargetElement.PositionMM = newPositionMM;

            // Update the UI element to refresh the visual representation
            m_HandleTargetElement.Update();

            // Update the handles parent position and rotation
            handlesParent.transform.localPosition = newElementCenterLocal;

            // Ensure the element is rendered with the UI Toolkit system
            EnsureElementRendered(m_HandleTargetElement);
        }

        // Calculate the world position of the corner opposite to the dragged handle
        private Vector3 CalculateOppositeCornerWorld(HandleType draggedType, CardElement element)
        {
            // Find the handles parent
            GameObject handlesParent = null;
            foreach (GameObject handle in m_ActiveHandles)
            {
                if (handle != null && handle.name == "ElementHandles")
                {
                    handlesParent = handle;
                    break;
                }
            }

            if (handlesParent == null)
            {
                Debug.LogWarning("[CalculateOppositeCornerWorld] Handles parent not found.");
                // Fallback to card transform
                Transform cardTransform = CardElementHelper.GetTransform(element);
                if (cardTransform == null) return Vector3.zero;
                return cardTransform.position;
            }

            // --- Get element size in meters from CardElement ---
            float widthMM = element.WidthMM;
            float heightMM = element.HeightMM;
            float widthMeters = UnitConverter.MmToMeters(widthMM);
            float heightMeters = UnitConverter.MmToMeters(heightMM);
            float halfWidth = widthMeters * 0.5f;
            float halfHeight = heightMeters * 0.5f;

            // Calculate the opposite corner position in local space (relative to handles parent)
            Vector3 oppositeLocalPos = Vector3.zero;
            switch (draggedType)
            {
                case HandleType.TopLeft: oppositeLocalPos = new Vector3(halfWidth, -halfHeight, 0); break;
                case HandleType.TopRight: oppositeLocalPos = new Vector3(-halfWidth, -halfHeight, 0); break;
                case HandleType.BottomLeft: oppositeLocalPos = new Vector3(halfWidth, halfHeight, 0); break;
                case HandleType.BottomRight: oppositeLocalPos = new Vector3(-halfWidth, halfHeight, 0); break;
            }

            // Convert to world space using the handles parent transform
            return handlesParent.transform.TransformPoint(oppositeLocalPos);
        }

        // Updates visual handle positions after element is resized/moved
        private void UpdateHandlePositions(CardElement targetElement)
        {
            if (targetElement == null) {
                Debug.LogWarning("[UpdateHandlePositions] Called but targetElement is null.");
                return;
            }

            // Find the handles parent (should be the first GameObject in m_ActiveHandles)
            GameObject handlesParent = null;
            foreach (GameObject handle in m_ActiveHandles)
            {
                if (handle != null && handle.name == "ElementHandles")
                {
                    handlesParent = handle;
                    break;
                }
            }

            if (handlesParent == null)
            {
                Debug.LogWarning("[UpdateHandlePositions] Handles parent not found.");
                return;
            }

            // Get element size in meters
            float widthMM = targetElement.WidthMM;
            float heightMM = targetElement.HeightMM;
            float widthMeters = UnitConverter.MmToMeters(widthMM);
            float heightMeters = UnitConverter.MmToMeters(heightMM);
            float halfWidth = widthMeters * 0.5f;
            float halfHeight = heightMeters * 0.5f;

            // Update the handles parent position and rotation based on the element
            Vector3 elementPositionMeters = new Vector3(
                UnitConverter.MmToMeters(targetElement.PositionMM.x),
                UnitConverter.MmToMeters(targetElement.PositionMM.y),
                -0.0001f // Small negative Z offset
            );
            Quaternion elementRotation = Quaternion.Euler(0, 0, targetElement.RotationDegrees);

            // Update the handles parent transform
            handlesParent.transform.localPosition = elementPositionMeters;
            handlesParent.transform.localRotation = elementRotation;

            // Update each corner handle position
            foreach (GameObject handleGO in m_ActiveHandles)
            {
                if (handleGO == null || handleGO == handlesParent) continue;

                // Get the handle identifier
                HandleIdentifier id = handleGO.GetComponent<HandleIdentifier>();
                if (id == null) continue;

                // Set the handle position based on its type
                Vector3 localPos = Vector3.zero;
                switch (id.Type)
                {
                    case HandleType.TopLeft:     localPos = new Vector3(-halfWidth, halfHeight, 0); break;
                    case HandleType.TopRight:    localPos = new Vector3(halfWidth, halfHeight, 0); break;
                    case HandleType.BottomLeft:  localPos = new Vector3(-halfWidth, -halfHeight, 0); break;
                    case HandleType.BottomRight: localPos = new Vector3(halfWidth, -halfHeight, 0); break;
                    case HandleType.Rotate:      localPos = new Vector3(0, halfHeight + 0.02f, 0); break; // Use a fixed offset
                    default: continue; // Skip unknown handle types
                }

                // Update the handle position (relative to the handles parent)
                handleGO.transform.localPosition = localPos;

                // Keep handle scale and rotation consistent
                handleGO.transform.localScale = Vector3.one * 0.01f; // Use a fixed size
                handleGO.transform.localRotation = Quaternion.identity;
            }
        }

        /// <summary>
        /// Called when the UI requests handles to be updated (e.g., after size change).
        /// </summary>
        private void UpdateActiveHandles(CardElement element)
        {
            // Use the element that currently has handles active
            CardElement target = element ?? m_HandleTargetElement; // Prioritize passed element
            if (target != null)
            {
                // Update handles using the handle system
                if (m_HandleSystem != null)
                {
                    // If the element is different from the current target, show handles for it
                    if (target != m_HandleSystem.GetCurrentTargetElement())
                    {
                        ShowHandlesForElement(target);
                    }
                    else
                    {
                        // Otherwise just update the existing handles
                        m_HandleSystem.UpdateHandles();
                    }
                }
                else
                {
                    Debug.LogWarning("[UpdateActiveHandles] HandleSystem is not available.");
                }
            }
            else
            {
                Debug.LogWarning("[UpdateActiveHandles] No target element found to update handles for.");
            }
        }

        // --- Serialization ---

        #if UNITY_EDITOR // Saving logic only needed in the editor
        private const string kDefaultSavePath = "Assets/Workshop/UXLab/Data/CardDefinitions";

        /// <summary>
        /// Saves the currently selected card's definition and layout as ScriptableObject assets.
        /// Prompts the user for a save location if necessary.
        /// </summary>
        public void SaveSelectedCardDefinition()
        {
            if (!IsCardSelected)
            {
                Debug.LogError("Cannot save: No card is currently selected.");
                return;
            }

            CardMeshGenerator cardMesh = SelectedCardMesh;
            CardLayout cardLayout = m_SelectedComponent.GetComponent<CardLayout>();
            if (cardLayout == null)
            {
                Debug.LogError($"Cannot save: Selected card '{m_SelectedComponent.name}' is missing a CardLayout component.", m_SelectedComponent);
                return;
            }

            // --- Prepare Data ---
            CardDefinition definitionData = ScriptableObject.CreateInstance<CardDefinition>();
            CardLayoutData layoutData = cardLayout.AppliedLayoutData; // Start with existing or create new
            bool isNewLayout = layoutData == null;
            if (isNewLayout)
            {
                layoutData = new CardLayoutData();
                layoutData.LayoutName = $"{cardMesh.gameObject.name} Layout"; // Default name
            }

            // 1. Populate CardDefinition Data
            definitionData.CardName = cardMesh.gameObject.name; // Use GameObject name for now
            definitionData.SizeData = cardMesh.CurrentSizeData; // Get current size data
            // description could be added via UI later

            // 2. Populate CardLayoutData Elements (Convert runtime state to data)
            layoutData.Elements.Clear(); // Clear existing elements before repopulating

            // Get UI elements instead of CardElements
            List<CardElement> uiElements = cardLayout.GetElements();
            CardDefinition existingDef = GetExistingCardDefinition(cardMesh.gameObject.name);
            CardLayoutData originalLayoutData = existingDef?.LayoutData; // Get original layout if exists

            // For now, we'll just use the existing layout data since we don't have a way to convert
            // CardElements back to CardElementData yet
            if (originalLayoutData != null && originalLayoutData.Elements != null)
            {
                foreach (var elementData in originalLayoutData.Elements)
                {
                    if (elementData != null)
                    {
                        layoutData.Elements.Add(elementData);
                    }
                }
                Debug.Log($"Reused {layoutData.Elements.Count} elements from original layout data.");
            }
            else
            {
                Debug.LogWarning("No original layout data found. Cannot save UI elements yet.");
            }

            // --- Save Assets ---
            string definitionPath = GetSavePath(definitionData, kDefaultSavePath);
            if (string.IsNullOrEmpty(definitionPath)) return; // User cancelled

            // Assign Layout reference to Definition
            definitionData.LayoutData = layoutData;

            // Save Definition Asset (Create or update)
            if (!UnityEditor.AssetDatabase.Contains(definitionData)) // This checks instance ID, may need path check
            {
                // Check if asset already exists at path to avoid error
                CardDefinition existingAsset = UnityEditor.AssetDatabase.LoadAssetAtPath<CardDefinition>(definitionPath);
                if(existingAsset == null)
                {
                    UnityEditor.AssetDatabase.CreateAsset(definitionData, definitionPath);
                } else {
                    // Overwrite existing asset's data
                     UnityEditor.EditorUtility.CopySerialized(definitionData, existingAsset);
                     UnityEditor.EditorUtility.SetDirty(existingAsset);
                     definitionData = existingAsset; // Use the existing asset reference moving forward
                     Debug.Log($"Overwriting existing CardDefinition at: {definitionPath}");
                }
            }
            else
            {
                UnityEditor.EditorUtility.SetDirty(definitionData);
            }

            UnityEditor.AssetDatabase.SaveAssets();
            UnityEditor.AssetDatabase.Refresh();

            Debug.Log($"Card Definition '{definitionData.CardName}' saved to {definitionPath}");
            // Update the layout reference on the runtime component if it was newly created
            if (isNewLayout)
            {
                cardLayout.ApplyLayoutData(layoutData);
            }
        }

        private CardDefinition GetExistingCardDefinition(string cardName)
        {
            string potentialPath = System.IO.Path.Combine(kDefaultSavePath, $"{cardName}.asset");
            return UnityEditor.AssetDatabase.LoadAssetAtPath<CardDefinition>(potentialPath);
        }

        private CardElementData FindOriginalElementData(CardLayoutData originalLayout, string elementName, int index)
        {
            if (originalLayout == null || originalLayout.Elements == null) return null;
            // Try finding by name first, fallback to index if names are duplicated/changed
            CardElementData data = originalLayout.Elements.FirstOrDefault(e => e.ElementName == elementName);
            if (data == null && index >= 0 && index < originalLayout.Elements.Count)
            {
                data = originalLayout.Elements[index];
            }
            return data;
        }

        private string GetSavePath(CardDefinition definitionData, string defaultDirectory)
        {
            string fileName = $"{definitionData.CardName}.asset";
            string initialPath = System.IO.Path.Combine(defaultDirectory, fileName);

            // Ensure the default directory exists
            if (!System.IO.Directory.Exists(defaultDirectory))
            {
                System.IO.Directory.CreateDirectory(defaultDirectory);
                UnityEditor.AssetDatabase.Refresh();
            }

            // For now, let's just save directly to the default path or overwrite.
            // A SaveFilePanel could be used for more flexibility:
            // return UnityEditor.EditorUtility.SaveFilePanelInProject(
            //     "Save Card Definition",
            //     fileName,
            //     "asset",
            //     "Please enter a file name to save the card definition to.",
            //     defaultDirectory);
            return initialPath;
        }

        /// <summary>
        /// Converts a runtime CardElement component state back into a serializable CardElementData object.
        /// </summary>
        private CardElementData ConvertElementToData(CardElement element, CardSizeData cardSize, CardElementData originalData)
        {
            if (element == null) return null;

            // Determine unit types based on original data if available, else default to millimeters
            LengthUnit positionUnit = originalData?.PositionX.Unit ?? LengthUnit.Millimeters;
            LengthUnit sizeUnit = originalData?.Width.Unit ?? LengthUnit.Millimeters;

            // Create the correct data type instance
            CardElementData data = null;
            if (element is CardImageElement imageElement)
            {
                ImageElementData imgData = new ImageElementData();
                imgData.TintColor = imageElement.TintColor;
                // Get Texture GUID
                if (imageElement.MainTexture != null)
                {
                    #if UNITY_EDITOR
                    string path = UnityEditor.AssetDatabase.GetAssetPath(imageElement.MainTexture);
                    imgData.TextureGuid = UnityEditor.AssetDatabase.AssetPathToGUID(path);
                    #endif
                }
                data = imgData;
            }
            else if (element is CardTextElement textElement)
            {
                TextElementData txtData = new TextElementData();
                txtData.TextContent = textElement.Text;
                txtData.FontColor = textElement.FontColor;
                txtData.FontSize = textElement.FontSize;
                txtData.Alignment = textElement.TextAlignment;
                txtData.AutoFit = textElement.AutoFit;
                // Get Font name.
                if (textElement.FontAsset != null)
                {
                    txtData.FontName = textElement.FontAsset.name;
                }
                data = txtData;
            }
            else
            {
                 Debug.LogWarning($"Serialization not implemented for element type: {element.GetType().Name}");
                 return null; // Or create a base CardElementData if needed
            }

            // --- Populate Base Properties ---
            data.ElementName = element.ElementName;
            data.IsVisible = element.IsVisible;
            data.RotationDegrees = element.RotationDegrees;

            // --- Convert Size using LengthValue ---
            float elementWidthMM = element.WidthMM; // Use actual element width in MM
            float elementHeightMM = element.HeightMM; // Use actual element height in MM
            
            if (sizeUnit == LengthUnit.Percentage)
            {
                float widthPercent = cardSize.Width > 0 ? elementWidthMM / cardSize.Width : 0; // % of card width
                float heightPercent = cardSize.Height > 0 ? elementHeightMM / cardSize.Height : 0; // % of card height
                data.Width = LengthValue.FromPercentage(widthPercent);
                data.Height = LengthValue.FromPercentage(heightPercent);
            }
            else
            {
                data.Width = LengthValue.FromMillimeters(elementWidthMM); // Size in mm
                data.Height = LengthValue.FromMillimeters(elementHeightMM); // Size in mm
            }

            // --- Convert Position (from Element-Center-Relative-to-Card-Center to Anchor-Relative-Offset) ---
            // element.PositionMM is the element's center relative to the card's center (in MM).
            Vector2 elementCenterRelativeToCardCenterMM = element.PositionMM;

            // Determine the anchor for calculations.
            CardAnchorPoint anchor = originalData?.Anchor ?? CardAnchorPoint.TopLeft;
            data.Anchor = anchor;

            // Calculate the vector from the element's chosen anchor point to the element's center (in MM).
            Vector2 vectorFromElementAnchorToElementCenterMM = CardLayout.GetVectorFromAnchorToCenter(anchor, elementWidthMM, elementHeightMM);

            // Calculate the element's anchor point relative to the card's center (in MM).
            // ElementAnchorRelToCardCenter = ElementCenterRelToCardCenter - VectorFromElementAnchorToElementCenter
            Vector2 elementAnchorRelativeToCardCenterMM = elementCenterRelativeToCardCenterMM - vectorFromElementAnchorToElementCenterMM;

            // Calculate the card's chosen anchor point relative to the card's center (in MM).
            Vector2 cardAnchorPointRelativeToCardCenterMM = CardLayout.GetAnchorOffset(anchor, cardSize.Width, cardSize.Height);

            // Calculate the final offset: the element's anchor point relative to the card's anchor point (in MM).
            // This is what CardElementData.Position stores.
            // Offset = ElementAnchorRelToCardCenter - CardAnchorRelToCardCenter
            float offsetX_mm = elementAnchorRelativeToCardCenterMM.x - cardAnchorPointRelativeToCardCenterMM.x;
            float offsetY_mm = elementAnchorRelativeToCardCenterMM.y - cardAnchorPointRelativeToCardCenterMM.y;

            if (positionUnit == LengthUnit.Percentage)
            {
                float positionXPercent = cardSize.Width > 0 ? offsetX_mm / cardSize.Width : 0;
                float positionYPercent = cardSize.Height > 0 ? offsetY_mm / cardSize.Height : 0;
                data.PositionX = LengthValue.FromPercentage(positionXPercent);
                data.PositionY = LengthValue.FromPercentage(positionYPercent);
            }
            else
            {
                data.PositionX = LengthValue.FromMillimeters(offsetX_mm); // Position offset in mm
                data.PositionY = LengthValue.FromMillimeters(offsetY_mm); // Position offset in mm
            }

            return data;
        }

        #endif // UNITY_EDITOR

        /// <summary>
        /// Loads a card definition, replacing the currently selected card if any.
        /// </summary>
        /// <param name="definition">The CardDefinition asset to load.</param>
        public void LoadCardDefinition(CardDefinition definition)
        {
            if (definition == null)
            {
                Debug.LogError("Cannot load null CardDefinition.");
                return;
            }

            // Deselect and destroy the current card if one is selected
            if (IsCardSelected)
            {
                GameObject currentCard = m_SelectedComponent;
                HandleBlankClicked(); // Deselect visually and clear UI
                m_TableManager.RemoveComponent(currentCard);
                Object.Destroy(currentCard);
                Debug.Log($"Destroyed previous card '{currentCard.name}'");
            }

            // Spawn the new card using TableManager
            // Position slightly above table, consider centering or using default spawn point?
            Vector3 spawnPosition = new Vector3(0, 0.05f, 0); // Adjust as needed
            Quaternion spawnRotation = Quaternion.Euler(90f, 0, 0); // Lay flat
            GameObject newCard = m_TableManager.SpawnCard(definition, spawnPosition, spawnRotation);

            if (newCard != null)
            {
                Debug.Log($"Loaded card '{definition.CardName}' successfully.");
                // Automatically select the newly loaded card
                // Need to wait a frame perhaps? Or directly call selection logic.
                // Let's try directly calling HandleComponentClicked
                HandleComponentClicked(newCard);
            }
            else
            {
                Debug.LogError($"Failed to spawn card from definition '{definition.CardName}'.");
            }
        }
    }
}

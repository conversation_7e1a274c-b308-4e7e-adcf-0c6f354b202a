using System;
using UnityEngine;
using UnityEngine.UIElements;
using Workshop.UXLab.Data;

namespace Workshop.UXLab
{
    /// <summary>
    /// Base class for UI elements that can be added to a card's UI document.
    /// </summary>
    public abstract class CardElement
    {
        // Properties
        public string ElementName { get; set; } = "New Element";
        public bool IsVisible { get; set; } = true;

        // Position and size (in millimeters)
        public float WidthMM { get; set; } = 10f;
        public float HeightMM { get; set; } = 10f;
        public Vector2 PositionMM { get; set; } = Vector2.zero;
        public float RotationDegrees { get; set; } = 0f;

        // Compatibility properties (convert between mm and meters)
        public float WidthMeters
        {
            get { return UnitConverter.MmToMeters(WidthMM); }
            set { WidthMM = UnitConverter.MetersToMm(value); }
        }

        public float HeightMeters
        {
            get { return UnitConverter.MmToMeters(HeightMM); }
            set { HeightMM = UnitConverter.MetersToMm(value); }
        }

        // UI Element reference
        protected VisualElement m_RootElement;

        // Parent render target
        protected CardRenderTarget m_RenderTarget;

        /// <summary>
        /// Creates a new CardElement.
        /// </summary>
        /// <param name="name">The name of the element</param>
        /// <param name="renderTarget">The render target to add the element to</param>
        public CardElement(string name, CardRenderTarget renderTarget)
        {
            ElementName = name;
            m_RenderTarget = renderTarget;

            // Create the root element
            m_RootElement = new VisualElement();
            m_RootElement.name = name;

            // Set up basic styles
            m_RootElement.style.position = Position.Absolute;
            m_RootElement.style.overflow = Overflow.Visible;
            m_RootElement.pickingMode = PickingMode.Ignore;

            // Add to the render target
            if (m_RenderTarget != null && m_RenderTarget.UIDocument != null)
            {
                m_RenderTarget.AddElement(m_RootElement);
            }
            else
            {
                Debug.LogError($"Cannot add element {name} to render target: UIDocument is null");
            }
        }

        /// <summary>
        /// Updates the element's position, size, and rotation based on its properties.
        /// </summary>
        public virtual void UpdateLayout()
        {
            if (m_RootElement == null) return;

            // Convert millimeters to pixels
            int pixelsPerMm = 1;//m_RenderTarget?.PixelsPerMm ?? 10;
            float widthPixels = WidthMM * pixelsPerMm;
            float heightPixels = HeightMM * pixelsPerMm;

            // Get card dimensions
            CardSizeData cardSize = m_RenderTarget?.CardMesh?.CurrentSizeData;
            if (cardSize == null) return;

            // Convert position from millimeters to pixels
            float posXPixels = PositionMM.x * pixelsPerMm;
            float posYPixels = PositionMM.y * pixelsPerMm; // Y is inverted in UI

            // Apply position and size
            m_RootElement.style.width = widthPixels;
            m_RootElement.style.height = heightPixels;
            m_RootElement.style.left = posXPixels;
            m_RootElement.style.top = posYPixels;

            // Apply rotation
            m_RootElement.style.rotate = new StyleRotate(new Rotate(RotationDegrees));

            // Apply visibility
            m_RootElement.style.display = IsVisible ? DisplayStyle.Flex : DisplayStyle.None;
        }

        /// <summary>
        /// Updates the element's content.
        /// </summary>
        public abstract void UpdateContent();

        /// <summary>
        /// Updates both layout and content.
        /// </summary>
        public void Update()
        {
            UpdateLayout();
            UpdateContent();
        }

        /// <summary>
        /// Removes the element from its parent.
        /// </summary>
        public virtual void Remove()
        {
            if (m_RootElement != null && m_RootElement.parent != null)
            {
                m_RootElement.parent.Remove(m_RootElement);
            }
        }
    }
}

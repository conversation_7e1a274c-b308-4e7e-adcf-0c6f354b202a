using UnityEngine;
using UnityEngine.UIElements;
using Unity.AppUI.UI; // For AppUI specific controls
using Unity.AppUI.Core; // If needed for AppUI specifics like Panel
using System;
using System.Collections.Generic; // For List and Dictionary
using System.Linq; // For LINQ operations like FirstOrDefault
using Workshop.UXLab.Data; // Added for CardSizeData
using System.Globalization; // Added for CultureInfo
// TMPro reference removed as we're now using TextAnchor directly

// Resolve ambiguity if standard UIElements Button/Toggle are also used elsewhere
using Button = Unity.AppUI.UI.Button;
using TextField = Unity.AppUI.UI.TextField;
using Vector3Field = Unity.AppUI.UI.Vector3Field;
using ColorField = Unity.AppUI.UI.ColorField;
using TextArea = Unity.AppUI.UI.TextArea;

// Use explicit aliases if needed to avoid ambiguity
using AppUIDropdown = Unity.AppUI.UI.Dropdown;
using AppUITextField = Unity.AppUI.UI.TextField;
using AppUITextArea = Unity.AppUI.UI.TextArea;
using AppUIVector2Field = Unity.AppUI.UI.Vector2Field;
using AppUIVector3Field = Unity.AppUI.UI.Vector3Field; // Keep if still needed elsewhere
using AppUIFloatField = Unity.AppUI.UI.FloatField;

#if UNITY_EDITOR
// using ObjectField = UnityEditor.UIElements.ObjectField; // Avoid for runtime UI
#endif

namespace Workshop.UXLab
{
    // [RequireComponent(typeof(UIDocument))] // No longer a MonoBehaviour
    public class CardElementEditorControl : VisualElement // Inherit from VisualElement
    {
        // Path to UXML/USS
        private const string UxmlPath = "UI/CardElementEditorLayout";
        private const string UssPath = "UI/CardElementEditorStyles";
        private const string VisibleClassName = "visible"; // USS class for visibility

        // --- UI Elements ---
        // Removed m_Root
        private DraggablePanel m_Container;
        private VisualElement m_NoComponentSelectedVE;
        private VisualElement m_SelectedComponentFieldsVE;
        private ScrollView m_ScrollView; // Reference to the scroll view

        // Basic Fields
        private AppUITextField m_ComponentNameField;

        // Transform Fields
        private AppUIDropdown m_UnitDropdown;
        private AppUIVector2Field m_PositionField;
        private AppUIVector2Field m_SizeField;
        private AppUIFloatField m_RotationField;

        // Specific & Advanced
        private VisualElement m_ComponentSpecificFieldsVE;
        private ActionButton m_MirrorXButton;
        private ActionButton m_MirrorYButton;
        private ActionButton m_DuplicateButton;

        // --- References ---
        private CardElement m_CurrentElement;
        private CardMeshGenerator m_ParentCardMesh; // Reference to the card's mesh generator
        private UnitSpace m_CurrentUnitSpace = UnitSpace.Millimeters; // Default unit
        private bool m_IgnoreValueChange = false;

        // --- Events ---
        public event Action<CardElement> OnElementModified; // Renamed event and type
        private Action m_RequestHandleUpdateAction; // Action provided by CardEditorSystem

        // --- Constants ---
        // private const string kComponentSelectedClass = "component-selected"; // No longer needed if visibility controlled by class

        // Lists for dropdown options
        private List<string> m_UnitOptions;

        public CardElementEditorControl() // Constructor
        {
             // Load UXML
             var visualTree = Resources.Load<VisualTreeAsset>(UxmlPath);
             if (visualTree != null)
             {
                 visualTree.CloneTree(this);
                 // Apply Styles
                 StyleSheet styleSheet = Resources.Load<StyleSheet>(UssPath);
                 if (styleSheet != null) styleSheets.Add(styleSheet);
                 else Debug.LogError($"CardElementEditorControl: Could not load USS from {UssPath}");
             }
             else
             {
                 Debug.LogError($"CardElementEditorControl: Failed to load UXML from {UxmlPath}");
                 this.Add(new Label("Error loading Element Editor UI."));
                 return;
             }

            // Find UI elements (Querying from 'this')
            m_Container = this.Q<DraggablePanel>("card-component-editor-container"); // UXML Name needs update?
            m_NoComponentSelectedVE = this.Q<VisualElement>("no-component-selected");
            m_SelectedComponentFieldsVE = this.Q<VisualElement>("selected-component-fields");
            m_ScrollView = this.Q<ScrollView>("component-scroll-view");

            m_UnitDropdown = this.Q<AppUIDropdown>("unit-dropdown");
            m_ComponentNameField = this.Q<AppUITextField>("component-name-field");
            m_PositionField = this.Q<AppUIVector2Field>("component-position-field");
            m_SizeField = this.Q<AppUIVector2Field>("component-size-field");
            m_RotationField = this.Q<AppUIFloatField>("component-rotation-field");

            m_ComponentSpecificFieldsVE = this.Q<VisualElement>("component-specific-fields");
            m_MirrorXButton = this.Q<ActionButton>("mirror-x-button");
            m_MirrorYButton = this.Q<ActionButton>("mirror-y-button");
            m_DuplicateButton = this.Q<ActionButton>("duplicate-button");

            // Basic null checks
            if (m_Container == null || m_NoComponentSelectedVE == null || m_SelectedComponentFieldsVE == null ||
                m_ScrollView == null || m_ComponentNameField == null || m_PositionField == null || m_SizeField == null || m_RotationField == null || m_UnitDropdown == null)
            {
                 Debug.LogError("CardElementEditorControl: Could not find all required UI elements. Check UXML names.");
                 // Optionally disable interaction or show error state
                 return;
            }

            // Register Callbacks
            m_ComponentNameField?.RegisterValueChangedCallback(OnNameChanged);
            m_UnitDropdown?.RegisterValueChangedCallback(OnUnitChanged);
            m_PositionField?.RegisterValueChangedCallback(OnPositionChanged);
            m_SizeField?.RegisterValueChangedCallback(OnSizeChanged);
            m_RotationField?.RegisterValueChangedCallback(OnRotationChanged);

            m_MirrorXButton.clicked += OnMirrorXClicked;
            m_MirrorYButton.clicked += OnMirrorYClicked;
            m_DuplicateButton.clicked += OnDuplicateClicked;

            // Initial State
            InitializeUnitDropdown();
            m_Container?.AddToClassList("hidden"); // Add hidden by default
            // Element starts hidden via USS
            // ClearActiveElement logic now handles showing/hiding internal parts
             ClearActiveElement();
             // Set initial rotation field formatting
             UpdateRotationFieldFormatting();
             // Set initial formatting for Vector2 fields based on default unit
             UpdateAllVector2FieldFormatting();
        }

        /// <summary>
        /// Provides actions from the controlling system (e.g., CardEditorSystem).
        /// </summary>
        public void SetupActions(Action requestHandleUpdate)
        {
            m_RequestHandleUpdateAction = requestHandleUpdate;
        }

        private void InitializeUnitDropdown()
        {
            m_UnitOptions = new List<string>();
            // Use the shared UnitConverter dictionary
            foreach (var unit in UnitConverter.kUnitLabels.OrderBy(kvp => kvp.Key)) // Order by enum value
            {
                 m_UnitOptions.Add(unit.Value);
            }

            m_UnitDropdown.sourceItems = m_UnitOptions;
            m_UnitDropdown.bindItem = (item, index) => {
                item.label = m_UnitOptions[index];
            };

            // Set default selection (e.g., Millimeters)
            m_CurrentUnitSpace = UnitSpace.Millimeters;
            int defaultIndex = m_UnitOptions.IndexOf(UnitConverter.kUnitLabels[m_CurrentUnitSpace]);
            if (defaultIndex >= 0)
            {
                m_UnitDropdown.SetValueWithoutNotify(new int[] { defaultIndex });
            }
            // Don't update formatting here, do it in constructor after fields are found
        }

        /// <summary>
        /// Sets the currently edited element and updates the UI.
        /// </summary>
        public void SetActiveElement(CardElement element)
        {
            if (element == null)
            {
                ClearActiveElement();
                return;
            }

            Debug.Log($"CardElementEditorControl.SetActiveElement called with {element.ElementName}");
            m_CurrentElement = element;

            // Find the parent CardMeshGenerator
            CardLayout parentLayout = CardElementHelper.GetCardLayout(m_CurrentElement);
            m_ParentCardMesh = parentLayout?.GetComponent<CardMeshGenerator>();
            if (m_ParentCardMesh == null)
            {
                Debug.LogWarning("CardElementEditorControl: Could not find parent CardMeshGenerator for the active element. Cannot calculate relative positions accurately.");
            }

            // Ensure UI elements are properly initialized
            if (m_SelectedComponentFieldsVE == null || m_NoComponentSelectedVE == null)
            {
                Debug.LogError("CardElementEditorControl: UI elements not properly initialized. Attempting to find them now.");
                m_SelectedComponentFieldsVE = this.Q<VisualElement>("selected-component-fields");
                m_NoComponentSelectedVE = this.Q<VisualElement>("no-component-selected");
                m_ComponentSpecificFieldsVE = this.Q<VisualElement>("component-specific-fields");
            }

            // Explicitly show fields and hide placeholder message
            if (m_SelectedComponentFieldsVE != null) m_SelectedComponentFieldsVE.style.display = DisplayStyle.Flex;
            if (m_NoComponentSelectedVE != null) m_NoComponentSelectedVE.style.display = DisplayStyle.None;

            UpdateUIFields(); // Update fields based on element and parent
            GenerateComponentSpecificFields();

            ShowUI(); // Make the main container visible (handles animation)
            Debug.Log($"Element Editor: Set active to {element.ElementName}");

            // Scroll to top when a new element is selected
            if (m_ScrollView != null) m_ScrollView.scrollOffset = Vector2.zero;

            // Update formatting when element becomes active
            UpdateRotationFieldFormatting();
            UpdateAllVector2FieldFormatting();
        }

        /// <summary>
        /// Clears the currently edited element and resets the UI.
        /// </summary>
        public void ClearActiveElement()
        {
             bool wasActive = m_CurrentElement != null;
             m_CurrentElement = null;
             m_ParentCardMesh = null;

            // Hide the fields section and show the placeholder
             if(m_SelectedComponentFieldsVE != null) m_SelectedComponentFieldsVE.style.display = DisplayStyle.None;
             if(m_NoComponentSelectedVE != null) m_NoComponentSelectedVE.style.display = DisplayStyle.Flex;

             ClearComponentSpecificFields();
             // HideUI(); // REMOVED: This caused the stack overflow

             if (wasActive) {
                 Debug.Log("Element Editor: Cleared active element");
             }
        }

        /// <summary>
        /// Populates the UI fields with data from the current element.
        /// </summary>
        private void UpdateUIFields()
        {
            if (m_CurrentElement == null) return;

            m_IgnoreValueChange = true; // Prevent callbacks from firing while we set values

            m_ComponentNameField.SetValueWithoutNotify(m_CurrentElement.ElementName);

            // Update transform fields (Pos, Size, Rot) based on current unit and parent
            UpdateTransformFields();

            // Update component-specific fields (Example: Tint color)
            var tintField = m_ComponentSpecificFieldsVE.Q<ColorField>("tint-color-field");
            if (tintField != null && m_CurrentElement is CardImageElement imageElem)
            {
                tintField.SetValueWithoutNotify(imageElem.TintColor);
            }

            m_IgnoreValueChange = false;
        }

        /// <summary>
        /// Updates Position, Size, Rotation fields based on m_CurrentElement and m_CurrentUnitSpace.
        /// Assumes m_IgnoreValueChange is already true when called.
        /// </summary>
        private void UpdateTransformFields()
        {
             if (m_CurrentElement == null) return;

             // --- Get Size and Position from CardElement ---
             float widthMM = m_CurrentElement.WidthMM;
             float heightMM = m_CurrentElement.HeightMM;
             Vector2 positionMM = m_CurrentElement.PositionMM;
             float rotationDegrees = m_CurrentElement.RotationDegrees;

             // Convert from MM to current unit
             Vector2 positionInCurrentUnit = new Vector2(
                 UnitConverter.FromMillimeters(positionMM.x, m_CurrentUnitSpace),
                 UnitConverter.FromMillimeters(positionMM.y, m_CurrentUnitSpace)
             );

             Vector2 sizeInCurrentUnit = new Vector2(
                 UnitConverter.FromMillimeters(widthMM, m_CurrentUnitSpace),
                 UnitConverter.FromMillimeters(heightMM, m_CurrentUnitSpace)
             );

             // --- Populate UI Fields ---
             m_PositionField.SetValueWithoutNotify(positionInCurrentUnit);
             m_SizeField.SetValueWithoutNotify(sizeInCurrentUnit);
             m_RotationField.SetValueWithoutNotify(rotationDegrees); // Rotation is unit-agnostic (degrees)

             // Update rotation formatting (it's always degrees)
             UpdateRotationFieldFormatting();
             // Update position/size formatting based on current unit
             UpdateAllVector2FieldFormatting();
        }

        /// <summary>
        /// Updates the UI fields directly from the element's current state,
        /// using the specified display unit, without triggering change events.
        /// </summary>
        public void UpdateFieldsFromElement(CardElement element, UnitSpace displayUnit)
        {
            if (element == null) return;

            m_IgnoreValueChange = true;

            // Update Name
            m_ComponentNameField?.SetValueWithoutNotify(element.ElementName);

            // Update Unit Dropdown (reflect the unit used for display)
            m_CurrentUnitSpace = displayUnit;
            int unitIndex = m_UnitOptions.IndexOf(UnitConverter.kUnitLabels[m_CurrentUnitSpace]);
            if (unitIndex >= 0)
            {
                m_UnitDropdown?.SetValueWithoutNotify(new int[] { unitIndex });
            }

            // --- Get Size and Position from CardElement ---
            float widthMM = element.WidthMM;
            float heightMM = element.HeightMM;
            Vector2 positionMM = element.PositionMM;
            float rotationDegrees = element.RotationDegrees;

            // Convert from MM to display unit
            Vector2 positionInDisplayUnit = new Vector2(
                UnitConverter.FromMillimeters(positionMM.x, displayUnit),
                UnitConverter.FromMillimeters(positionMM.y, displayUnit)
            );

            Vector2 sizeInDisplayUnit = new Vector2(
                UnitConverter.FromMillimeters(widthMM, displayUnit),
                UnitConverter.FromMillimeters(heightMM, displayUnit)
            );

            // --- Populate UI Fields ---
            m_PositionField?.SetValueWithoutNotify(positionInDisplayUnit);
            m_SizeField?.SetValueWithoutNotify(sizeInDisplayUnit);
            m_RotationField?.SetValueWithoutNotify(rotationDegrees);
            // ---------------------------

            m_IgnoreValueChange = false;

            // Update formatting after updating fields from element
            UpdateRotationFieldFormatting();
            UpdateAllVector2FieldFormatting();
        }

        // --- Value Change Callbacks ---

        private void OnNameChanged(ChangeEvent<string> evt)
        {
            if (m_IgnoreValueChange || m_CurrentElement == null) return;
            m_CurrentElement.ElementName = evt.newValue;
            NotifyElementModified();
        }

        private void OnUnitChanged(ChangeEvent<IEnumerable<int>> evt)
        {
             if (m_IgnoreValueChange || m_CurrentElement == null || !evt.newValue.Any()) return;

             UnitSpace newUnit = GetSelectedUnitSpace();
             if (newUnit == m_CurrentUnitSpace) return; // No actual change

             Debug.Log($"Unit changed from {m_CurrentUnitSpace} to {newUnit}. Updating UI fields.");
             m_CurrentUnitSpace = newUnit;

             // Re-populate the transform fields using the new unit
             // This reads the current transform values and converts them for display
             m_IgnoreValueChange = true;
             UpdateTransformFields();
             m_IgnoreValueChange = false;

             // Note: We don't modify the actual component transform here, just the display.
             // No need to call NotifyComponentModified.
              // Unit change doesn't affect rotation formatting
             // Update Vector2 fields formatting to reflect the new unit
             UpdateAllVector2FieldFormatting();
        }

        private void OnPositionChanged(ChangeEvent<Vector2> evt)
        {
            if (m_IgnoreValueChange || m_CurrentElement == null) return;

            // Value from UI is relative to bottom-left, in m_CurrentUnitSpace
            Vector2 positionInCurrentUnit = evt.newValue;

            // Convert to millimeters (our standard unit)
            float posX_mm = UnitConverter.ToMillimeters(positionInCurrentUnit.x, m_CurrentUnitSpace);
            float posY_mm = UnitConverter.ToMillimeters(positionInCurrentUnit.y, m_CurrentUnitSpace);

            // Apply position directly to CardElement
            m_CurrentElement.PositionMM = new Vector2(posX_mm, posY_mm);

            NotifyElementModified();
            // Update Rotation field formatting (doesn't change, but good practice)
            UpdateRotationFieldFormatting();
            // Update Vector2 fields formatting (doesn't change, but good practice)
            UpdateAllVector2FieldFormatting();
        }

        private void OnSizeChanged(ChangeEvent<Vector2> evt)
        {
            if (m_IgnoreValueChange || m_CurrentElement == null) return;

            // Value from UI is Width/Height in m_CurrentUnitSpace
            Vector2 sizeInCurrentUnit = evt.newValue;

            // Ensure size is non-negative
            sizeInCurrentUnit.x = Mathf.Max(0.0001f, sizeInCurrentUnit.x);
            sizeInCurrentUnit.y = Mathf.Max(0.0001f, sizeInCurrentUnit.y);

            // Convert to millimeters (our standard unit)
            float width_mm = UnitConverter.ToMillimeters(sizeInCurrentUnit.x, m_CurrentUnitSpace);
            float height_mm = UnitConverter.ToMillimeters(sizeInCurrentUnit.y, m_CurrentUnitSpace);

            // Apply size directly to CardElement
            m_CurrentElement.WidthMM = width_mm;
            m_CurrentElement.HeightMM = height_mm;

            // Update UI field in case value was clamped
            // We need to read back the actual size from the element and convert back to UI units
            Vector2 actualSizeInUIUnits = new Vector2(
                UnitConverter.FromMillimeters(m_CurrentElement.WidthMM, m_CurrentUnitSpace),
                UnitConverter.FromMillimeters(m_CurrentElement.HeightMM, m_CurrentUnitSpace)
            );

            if (Vector2.Distance(actualSizeInUIUnits, sizeInCurrentUnit) > 0.0001f)
            {
                 m_IgnoreValueChange = true;
                 m_SizeField.SetValueWithoutNotify(actualSizeInUIUnits);
                 m_IgnoreValueChange = false;
            }

            NotifyElementModified();
            // Update Rotation field formatting (doesn't change, but good practice)
            UpdateRotationFieldFormatting();
            // Update Vector2 fields formatting (doesn't change, but good practice)
            UpdateAllVector2FieldFormatting();
        }

        private void OnRotationChanged(ChangeEvent<float> evt)
        {
            if (m_IgnoreValueChange || m_CurrentElement == null) return;

            // Value from UI is rotation around Z axis in degrees
            float rotationDegrees = evt.newValue;

            // Apply rotation directly to CardElement
            m_CurrentElement.RotationDegrees = rotationDegrees;

            NotifyElementModified();
            // Update Rotation field formatting (doesn't change, but good practice)
            UpdateRotationFieldFormatting();
            // Update Vector2 fields formatting (doesn't change, but good practice)
            UpdateAllVector2FieldFormatting();
        }

        // --- Button Click Handlers ---

        private void OnMirrorXClicked()
        {
             if (m_CurrentElement == null) return;
             Debug.LogWarning("Mirror X: Not implemented yet.");
             // TODO: Implement mirroring logic (likely flips scale.x and adjusts position.x)
             OnElementModified?.Invoke(m_CurrentElement);
        }

        private void OnMirrorYClicked()
        {
             if (m_CurrentElement == null) return;
             Debug.LogWarning("Mirror Y: Not implemented yet.");
             // TODO: Implement mirroring logic (likely flips scale.y and adjusts position.y)
             OnElementModified?.Invoke(m_CurrentElement);
        }

        private void OnDuplicateClicked()
        {
             if (m_CurrentElement == null) return;
             Debug.LogWarning("Duplicate to Quadrants: Not implemented yet.");
             // TODO: Implement duplication logic
             // Needs access to CardLayout to add new elements
             // Needs card dimensions to calculate quadrant positions/mirrors
        }

        // --- Component Specific Fields ---

        private void GenerateComponentSpecificFields()
        {
            // Clear existing fields
            m_ComponentSpecificFieldsVE.Clear();

            if (m_CurrentElement == null) return;

            // Generate fields based on element type
            if (m_CurrentElement is CardImageElement imageElem)
            {
                GenerateImageElementFields(imageElem);
            }
            else if (m_CurrentElement is CardTextElement textElem)
            {
                GenerateTextElementFields(textElem);
            }
        }

        private void GenerateImageElementFields(CardImageElement imageElem)
        {
            // --- Tint Color ---
            var tintContainer = new VisualElement { name = "tint-container", style = { flexDirection = FlexDirection.Row, alignItems = Align.Center, marginBottom = 10 } };
            tintContainer.AddToClassList("input-container"); // Reuse existing style

            var tintLabel = new Unity.AppUI.UI.Text { text = "Tint Color" };
            tintLabel.AddToClassList("property-label");

            var tintField = new ColorField { name = "tint-color-field", value = imageElem.TintColor };
            tintField.AddToClassList("property-field");
            tintField.RegisterValueChangedCallback(evt => {
                if (m_IgnoreValueChange || !(m_CurrentElement is CardImageElement currentImageElem)) return;
                currentImageElem.TintColor = evt.newValue;
                NotifyElementModified();
            });

            tintContainer.Add(tintLabel);
            tintContainer.Add(tintField);
            m_ComponentSpecificFieldsVE.Add(tintContainer);

            // --- Scale Mode ---
            var scaleModeContainer = new VisualElement();
            scaleModeContainer.AddToClassList("input-container");

            var scaleModeLabel = new Unity.AppUI.UI.Text { text = "Scale Mode" };
            scaleModeLabel.AddToClassList("property-label");

            var scaleModeField = new AppUIDropdown { name = "scale-mode-field" };
            scaleModeField.AddToClassList("property-field");

            // Add scale mode options
            var scaleModeOptions = new List<string> {
                "ScaleToFit",
                "ScaleAndCrop",
                "StretchToFill"
            };

            scaleModeField.sourceItems = scaleModeOptions;
            scaleModeField.bindItem = (item, index) => {
                item.label = scaleModeOptions[index];
            };

            // Set initial value
            int currentScaleModeIndex = (int)imageElem.ScaleMode;
            scaleModeField.SetValueWithoutNotify(new int[] { currentScaleModeIndex });

            scaleModeField.RegisterValueChangedCallback(evt => {
                if (m_IgnoreValueChange || !(m_CurrentElement is CardImageElement currentImageElem)) return;

                // Get the selected index from the IEnumerable<int>
                int selectedIndex = evt.newValue.FirstOrDefault();

                // Map index to ScaleMode
                UnityEngine.ScaleMode newScaleMode = (UnityEngine.ScaleMode)selectedIndex;
                currentImageElem.ScaleMode = newScaleMode;
                NotifyElementModified();
            });

            scaleModeContainer.Add(scaleModeLabel);
            scaleModeContainer.Add(scaleModeField);
            m_ComponentSpecificFieldsVE.Add(scaleModeContainer);

            // --- Texture Field (Placeholder/Future) ---
            // TODO: Add ObjectField or Button for texture selection
        }

        private void GenerateTextElementFields(CardTextElement textElem)
        {
            // Text Content Field
            var textContainer = new VisualElement();
            textContainer.AddToClassList("input-container");

            var textLabel = new Unity.AppUI.UI.Text { text = "Text Content" };
            textLabel.AddToClassList("property-label");

            var textField = new AppUITextArea { name = "text-content-field", value = textElem.Text };
            textField.AddToClassList("property-field");
            textField.RegisterValueChangedCallback(evt => {
                if (m_IgnoreValueChange || !(m_CurrentElement is CardTextElement currentTextElem)) return;
                currentTextElem.Text = evt.newValue;
                NotifyElementModified();
            });

            textContainer.Add(textLabel);
            textContainer.Add(textField);
            m_ComponentSpecificFieldsVE.Add(textContainer);

            // Auto Fit Toggle
            var autoFitContainer = new VisualElement();
            autoFitContainer.AddToClassList("input-container");

            var autoFitLabel = new Unity.AppUI.UI.Text { text = "Auto Fit Text" };
            autoFitLabel.AddToClassList("property-label");

            var autoFitToggle = new Unity.AppUI.UI.Toggle { name = "auto-fit-toggle", value = textElem.AutoFit };
            autoFitToggle.AddToClassList("property-field");
            autoFitToggle.RegisterValueChangedCallback(evt => {
                if (m_IgnoreValueChange || !(m_CurrentElement is CardTextElement currentTextElem)) return;
                currentTextElem.AutoFit = evt.newValue;
                NotifyElementModified();
            });

            autoFitContainer.Add(autoFitLabel);
            autoFitContainer.Add(autoFitToggle);
            m_ComponentSpecificFieldsVE.Add(autoFitContainer);

            // Font Size Field with slider for better UX
            var fontSizeContainer = new VisualElement();
            fontSizeContainer.AddToClassList("input-container");

            var fontSizeLabel = new Unity.AppUI.UI.Text { text = "Font Size (Max)" };
            fontSizeLabel.AddToClassList("property-label");

            // Create a horizontal container for the slider and value field
            var fontSizeControlsContainer = new VisualElement();
            fontSizeControlsContainer.style.flexDirection = FlexDirection.Row;
            fontSizeControlsContainer.style.flexGrow = 1;

            // Create a slider for more intuitive font size adjustment
            var fontSizeSlider = new Slider() {
                name = "font-size-slider",
                lowValue = 4f,
                highValue = 72f,
                value = textElem.FontSize,
                showInputField = false
            };
            fontSizeSlider.style.flexGrow = 1;
            fontSizeSlider.style.marginRight = 5;

            // Create a float field to show the exact value
            var fontSizeField = new AppUIFloatField {
                name = "font-size-field",
                value = textElem.FontSize,
                style = { width = 50 }
            };
            fontSizeField.AddToClassList("property-field");

            // Link the slider and field
            fontSizeSlider.RegisterValueChangedCallback(evt => {
                if (m_IgnoreValueChange || !(m_CurrentElement is CardTextElement currentTextElem)) return;
                fontSizeField.SetValueWithoutNotify(evt.newValue);
                currentTextElem.FontSize = evt.newValue;
                NotifyElementModified();
            });

            fontSizeField.RegisterValueChangedCallback(evt => {
                if (m_IgnoreValueChange || !(m_CurrentElement is CardTextElement currentTextElem)) return;
                fontSizeSlider.SetValueWithoutNotify(evt.newValue);
                currentTextElem.FontSize = evt.newValue;
                NotifyElementModified();
            });

            fontSizeControlsContainer.Add(fontSizeSlider);
            fontSizeControlsContainer.Add(fontSizeField);

            fontSizeContainer.Add(fontSizeLabel);
            fontSizeContainer.Add(fontSizeControlsContainer);
            m_ComponentSpecificFieldsVE.Add(fontSizeContainer);

            // Font Color Field
            var colorContainer = new VisualElement();
            colorContainer.AddToClassList("input-container");

            var colorLabel = new Unity.AppUI.UI.Text { text = "Font Color" };
            colorLabel.AddToClassList("property-label");

            var colorField = new ColorField { name = "font-color-field", value = textElem.FontColor };
            colorField.AddToClassList("property-field");
            colorField.RegisterValueChangedCallback(evt => {
                if (m_IgnoreValueChange || !(m_CurrentElement is CardTextElement currentTextElem)) return;
                currentTextElem.FontColor = evt.newValue;
                NotifyElementModified();
            });

            colorContainer.Add(colorLabel);
            colorContainer.Add(colorField);
            m_ComponentSpecificFieldsVE.Add(colorContainer);

            // Text Alignment Field
            var alignContainer = new VisualElement();
            alignContainer.AddToClassList("input-container");

            var alignLabel = new Unity.AppUI.UI.Text { text = "Alignment" };
            alignLabel.AddToClassList("property-label");

            var alignField = new AppUIDropdown { name = "text-align-field" };
            alignField.AddToClassList("property-field");

            // Add alignment options
            var alignmentOptions = new List<string> { "Left", "Center", "Right" };
            alignField.sourceItems = alignmentOptions;
            alignField.bindItem = (item, index) => {
                item.label = alignmentOptions[index];
            };

            // Set initial value
            int currentAlignmentIndex = 0; // Default to Left
            if (textElem.TextAlignment == TextAnchor.MiddleCenter)
                currentAlignmentIndex = 1;
            else if (textElem.TextAlignment == TextAnchor.MiddleRight)
                currentAlignmentIndex = 2;

            alignField.SetValueWithoutNotify(new int[] { currentAlignmentIndex });

            alignField.RegisterValueChangedCallback(evt => {
                if (m_IgnoreValueChange || !(m_CurrentElement is CardTextElement currentTextElem)) return;

                // Get the selected index from the IEnumerable<int>
                int selectedIndex = evt.newValue.FirstOrDefault();

                // Map index to TextAnchor
                TextAnchor newAlignment;
                switch (selectedIndex) {
                    case 0:
                        newAlignment = TextAnchor.MiddleLeft;
                        break;
                    case 1:
                        newAlignment = TextAnchor.MiddleCenter;
                        break;
                    default:
                        newAlignment = TextAnchor.MiddleRight;
                        break;
                }

                currentTextElem.TextAlignment = newAlignment;
                NotifyElementModified();
            });

            alignContainer.Add(alignLabel);
            alignContainer.Add(alignField);
            m_ComponentSpecificFieldsVE.Add(alignContainer);
        }

        private void ClearComponentSpecificFields()
        {
            // Remove all previously generated fields
             if (m_ComponentSpecificFieldsVE != null)
             {
                 m_ComponentSpecificFieldsVE.Clear();
             }
             // Also ensure internal state is reset when hiding the whole panel
             // ClearActiveElement(); // REMOVED: No longer needed here as HideUI doesn't call it
        }

        /// <summary>
        /// Adds UI rendering section to the component specific fields.
        /// </summary>
        private void AddUIRenderingSection()
        {
            if (m_CurrentElement == null || m_ComponentSpecificFieldsVE == null) return;

            // Add a separator
            var separator = new VisualElement();
            separator.AddToClassList("separator");
            separator.style.height = 1;
            separator.style.backgroundColor = new StyleColor(new Color(0.5f, 0.5f, 0.5f, 0.5f));
            separator.style.marginTop = 10;
            separator.style.marginBottom = 10;
            m_ComponentSpecificFieldsVE.Add(separator);

            // Add UI Rendering header
            var header = new Label("UI Rendering");
            header.AddToClassList("component-section-header");
            m_ComponentSpecificFieldsVE.Add(header);

            // Add info text
            var infoText = new Label("This element is now rendered using UI Toolkit for better performance and quality.");
            infoText.AddToClassList("info-text");
            m_ComponentSpecificFieldsVE.Add(infoText);

            // The CardElement is the UI element itself
            CardElement element = m_CurrentElement;
            if (element != null)
            {
                // Add UI element info
                var uiInfoContainer = new VisualElement();
                uiInfoContainer.AddToClassList("input-container");

                var uiTypeLabel = new Unity.AppUI.UI.Text { text = "UI Element Type" };
                uiTypeLabel.AddToClassList("property-label");

                var uiTypeValue = new Unity.AppUI.UI.Text { text = element.GetType().Name };
                uiTypeValue.AddToClassList("property-value");

                uiInfoContainer.Add(uiTypeLabel);
                uiInfoContainer.Add(uiTypeValue);
                m_ComponentSpecificFieldsVE.Add(uiInfoContainer);
            }
            else
            {
                // Add warning if UI element is not initialized
                var warningText = new Label("UI element not initialized. It will be created when the game starts.");
                warningText.AddToClassList("warning-text");
                warningText.style.color = new StyleColor(new Color(1f, 0.5f, 0f, 1f));
                m_ComponentSpecificFieldsVE.Add(warningText);
            }
        }


        // --- Helper Methods ---
        public UnitSpace GetSelectedUnitSpace()
        {
            if (m_UnitDropdown == null || m_UnitDropdown.value == null || !m_UnitDropdown.value.Any()) return UnitSpace.Millimeters; // Default
            int index = m_UnitDropdown.value.First();
            if (index >= 0 && index < m_UnitOptions.Count)
            {
                 string selectedLabel = m_UnitOptions[index];
                 // Find UnitSpace from label using UnitConverter
                 foreach (var unitPair in UnitConverter.kUnitLabels)
                 {
                     if (unitPair.Value == selectedLabel) return unitPair.Key;
                 }
            }
            return UnitSpace.Millimeters; // Fallback
        }

        // --- Visibility Control (Using Display Style) ---

        public void ShowUI()
        {
             if (m_Container == null) return;
             // Toggle USS class on the container
             m_Container.RemoveFromClassList("hidden"); // Remove hidden
             m_Container.AddToClassList(VisibleClassName);
             m_Container.pickingMode = PickingMode.Position;
        }

        public void HideUI()
        {
             if (m_Container == null) return;
             // Toggle USS class on the container
             m_Container.RemoveFromClassList(VisibleClassName);
             m_Container.AddToClassList("hidden"); // Add hidden
             m_Container.pickingMode = PickingMode.Ignore;

             // Also ensure internal state is reset when hiding the whole panel
             ClearActiveElement();
        }

        private void NotifyElementModified()
        {
            if (m_CurrentElement != null)
            {
                OnElementModified?.Invoke(m_CurrentElement);
                m_RequestHandleUpdateAction?.Invoke();
                 // Optional: Mark scene dirty if in editor
                 #if UNITY_EDITOR
                 if (!Application.isPlaying)
                 {
                     try
                     {
                        // Setting dirty can sometimes throw exceptions if the object is not persistent
                        // CardElement is not a UnityEngine.Object, so we can't set it dirty directly
                        // Instead, set the parent CardLayout dirty
                        CardLayout layout = CardElementHelper.GetCardLayout(m_CurrentElement);
                        if (layout != null)
                        {
                            UnityEditor.EditorUtility.SetDirty(layout);
                        }
                        // Consider setting the scene dirty too:
                        // UnityEditor.SceneManagement.EditorSceneManager.MarkSceneDirty(m_CurrentElement.gameObject.scene);
                     }
                     catch (Exception e) {
                         Debug.LogWarning($"Failed to set element dirty: {e.Message}");
                     }
                 }
                 #endif
            }
        }

         private void OnDisable()
         {
             // This method might not be called reliably for VisualElements added via C#
             // Perform cleanup in a DetachedFromPanelEvent callback if needed
             // UnregisterCallbacks(); // Example cleanup
             // Basic cleanup
             m_CurrentElement = null;
             m_ParentCardMesh = null;
             // Consider unregistering callbacks if they capture state that might become invalid
             // m_ComponentNameField?.UnregisterValueChangedCallback(OnNameChanged);
             // ...etc
         }

         // --- Formatting Helper ---

        /// <summary>
        /// Updates the format string for the Rotation FloatField (always degrees).
        /// </summary>
        private void UpdateRotationFieldFormatting()
        {
            if (m_RotationField == null) return;
            // Rotation is always degrees, use 1 decimal place
            m_RotationField.formatString = "0.0";
            m_RotationField.unit = "°";
            //m_RotationField.culture = CultureInfo.InvariantCulture; // Remove culture setting
        }

        /// <summary>
        /// Updates the format string and unit for the internal FloatFields of a Vector2Field.
        /// </summary>
        private void UpdateVector2FieldFormatting(AppUIVector2Field field, UnitSpace unit)
        {
            if (field == null) return;

            string format = "";
            string unitLabel = "";

            // Determine format and label based on unit (same logic as DimensionEditor)
            switch (unit)
            {
                case UnitSpace.Millimeters:
                    format = "0.00";
                    unitLabel = UnitConverter.kUnitLabelsShort[UnitSpace.Millimeters];
                    break;
                case UnitSpace.Centimeters:
                    format = "0.000";
                    unitLabel = UnitConverter.kUnitLabelsShort[UnitSpace.Centimeters];
                    break;
                case UnitSpace.Inches:
                    format = "0.0000";
                    unitLabel = UnitConverter.kUnitLabelsShort[UnitSpace.Inches];
                    break;
                case UnitSpace.Meters:
                    format = "0.0000";
                    unitLabel = UnitConverter.kUnitLabelsShort[UnitSpace.Meters];
                    break;
                 // Add cases for other units if needed
            }
            field.formatString = $"{format} {unitLabel}";
        }

        /// <summary>
        /// Updates the formatting for all Vector2Fields based on the current unit selection.
        /// </summary>
        private void UpdateAllVector2FieldFormatting()
        {
            UnitSpace currentUnit = GetSelectedUnitSpace();
            UpdateVector2FieldFormatting(m_PositionField, currentUnit);
            UpdateVector2FieldFormatting(m_SizeField, currentUnit);
        }
    }
}

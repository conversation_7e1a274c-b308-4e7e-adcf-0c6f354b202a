using UnityEngine;
using System.Collections.Generic;

namespace Workshop.UXLab
{
    /// <summary>
    /// Helper class to bridge between CardElement and MonoBehaviour functionality.
    /// Provides methods for accessing transform, parent components, and other MonoBehaviour features.
    /// </summary>
    public static class CardElementHelper
    {
        // Dictionary to map CardElements to their parent CardLayout
        private static Dictionary<CardElement, CardLayout> s_ElementToLayoutMap = new Dictionary<CardElement, CardLayout>();

        /// <summary>
        /// Registers a CardElement with its parent CardLayout.
        /// </summary>
        public static void RegisterElement(CardElement element, CardLayout layout)
        {
            if (element != null && layout != null)
            {
                s_ElementToLayoutMap[element] = layout;
            }
        }

        /// <summary>
        /// Unregisters a CardElement.
        /// </summary>
        public static void UnregisterElement(CardElement element)
        {
            if (element != null && s_ElementToLayoutMap.ContainsKey(element))
            {
                s_ElementToLayoutMap.Remove(element);
            }
        }

        /// <summary>
        /// Gets the parent CardLayout for a CardElement.
        /// </summary>
        public static CardLayout GetCardLayout(CardElement element)
        {
            if (element != null && s_ElementToLayoutMap.TryGetValue(element, out CardLayout layout))
            {
                return layout;
            }
            return null;
        }

        /// <summary>
        /// Gets a component of type T from the parent CardLayout's GameObject.
        /// </summary>
        public static T GetComponentInParent<T>(CardElement element) where T : Component
        {
            CardLayout layout = GetCardLayout(element);
            if (layout != null)
            {
                return layout.GetComponent<T>();
            }
            return null;
        }

        /// <summary>
        /// Gets the transform of the parent CardLayout's GameObject.
        /// </summary>
        public static Transform GetTransform(CardElement element)
        {
            CardLayout layout = GetCardLayout(element);
            if (layout != null)
            {
                return layout.transform;
            }
            return null;
        }

        /// <summary>
        /// Gets the GameObject of the parent CardLayout.
        /// </summary>
        public static GameObject GetGameObject(CardElement element)
        {
            CardLayout layout = GetCardLayout(element);
            if (layout != null)
            {
                return layout.gameObject;
            }
            return null;
        }

        /// <summary>
        /// Gets the sibling index of the CardElement in its parent CardLayout.
        /// </summary>
        public static int GetSiblingIndex(CardElement element)
        {
            CardLayout layout = GetCardLayout(element);
            if (layout != null)
            {
                return layout.GetElementIndex(element);
            }
            return -1;
        }

        /// <summary>
        /// Sets the local position of the CardElement.
        /// This is a compatibility method that converts the position to PositionMM.
        /// The position is relative to the card's center (0,0).
        /// </summary>
        public static void SetLocalPosition(CardElement element, Vector3 localPosition)
        {
            if (element != null)
            {
                // Convert from meters to millimeters
                Vector2 positionMM = new Vector2(
                    UnitConverter.MetersToMm(localPosition.x),
                    UnitConverter.MetersToMm(localPosition.y)
                );
                element.PositionMM = positionMM;
                element.Update();
            }
        }

        /// <summary>
        /// Gets the local position of the CardElement.
        /// This is a compatibility method that converts PositionMM to a Vector3.
        /// </summary>
        public static Vector3 GetLocalPosition(CardElement element)
        {
            if (element != null)
            {
                // Convert from millimeters to meters
                return new Vector3(
                    UnitConverter.MmToMeters(element.PositionMM.x),
                    UnitConverter.MmToMeters(element.PositionMM.y),
                    0f
                );
            }
            return Vector3.zero;
        }

        /// <summary>
        /// Sets the local rotation of the CardElement.
        /// This is a compatibility method that sets RotationDegrees.
        /// </summary>
        public static void SetLocalRotation(CardElement element, Quaternion localRotation)
        {
            if (element != null)
            {
                // Extract the Z rotation in degrees
                element.RotationDegrees = localRotation.eulerAngles.z;
                element.Update();
            }
        }

        /// <summary>
        /// Gets the local rotation of the CardElement.
        /// This is a compatibility method that converts RotationDegrees to a Quaternion.
        /// </summary>
        public static Quaternion GetLocalRotation(CardElement element)
        {
            if (element != null)
            {
                return Quaternion.Euler(0f, 0f, element.RotationDegrees);
            }
            return Quaternion.identity;
        }

        /// <summary>
        /// Sets the local euler angles of the CardElement.
        /// This is a compatibility method that sets RotationDegrees.
        /// </summary>
        public static void SetLocalEulerAngles(CardElement element, Vector3 localEulerAngles)
        {
            if (element != null)
            {
                element.RotationDegrees = localEulerAngles.z;
                element.Update();
            }
        }

        /// <summary>
        /// Gets the local euler angles of the CardElement.
        /// This is a compatibility method that converts RotationDegrees to a Vector3.
        /// </summary>
        public static Vector3 GetLocalEulerAngles(CardElement element)
        {
            if (element != null)
            {
                return new Vector3(0f, 0f, element.RotationDegrees);
            }
            return Vector3.zero;
        }
    }
}

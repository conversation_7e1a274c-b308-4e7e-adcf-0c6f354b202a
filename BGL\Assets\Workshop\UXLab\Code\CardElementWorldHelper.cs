using UnityEngine;
using System.Collections.Generic;

namespace Workshop.UXLab
{
    /// <summary>
    /// Helper class for working with CardElements in world space.
    /// Provides methods for converting between world space and UI space,
    /// and for editing UI elements in world space.
    /// </summary>
    public class CardElementWorldHelper
    {
        private CardLayout m_CardLayout;
        private CardMeshGenerator m_CardMesh;
        private Dictionary<CardElement, GameObject> m_WorldSpaceHandles = new Dictionary<CardElement, GameObject>();

        /// <summary>
        /// Creates a new CardElementWorldHelper for the specified card layout.
        /// </summary>
        /// <param name="cardLayout">The card layout to work with</param>
        public CardElementWorldHelper(CardLayout cardLayout)
        {
            m_CardLayout = cardLayout;
            m_CardMesh = cardLayout.CardMeshGenerator;
        }

        /// <summary>
        /// Creates a world space handle for the specified UI element.
        /// </summary>
        /// <param name="element">The UI element to create a handle for</param>
        /// <returns>The created GameObject handle</returns>
        public GameObject CreateWorldSpaceHandle(CardElement element)
        {
            if (element == null) return null;

            // Check if a handle already exists
            if (m_WorldSpaceHandles.TryGetValue(element, out GameObject existingHandle))
            {
                return existingHandle;
            }

            // Create a new GameObject for the handle
            GameObject handle = new GameObject($"Handle_{element.ElementName}");
            handle.transform.SetParent(m_CardLayout.transform, false);

            // Set the position, rotation, and scale
            UpdateHandleTransform(element, handle);

            // Add a bounding box renderer
            LineRenderer lineRenderer = handle.AddComponent<LineRenderer>();
            ConfigureBoundingBoxRenderer(lineRenderer, element);

            // Store the handle
            m_WorldSpaceHandles[element] = handle;

            return handle;
        }

        /// <summary>
        /// Updates the transform of the world space handle for the specified UI element.
        /// </summary>
        /// <param name="element">The UI element</param>
        /// <param name="handle">The world space handle</param>
        public void UpdateHandleTransform(CardElement element, GameObject handle)
        {
            if (element == null || handle == null) return;

            // Convert position from mm to meters
            Vector3 positionMeters = new Vector3(
                UnitConverter.MmToMeters(element.PositionMM.x),
                UnitConverter.MmToMeters(element.PositionMM.y),
                -0.0001f * m_WorldSpaceHandles.Count // Small negative Z offset for layering
            );

            // Set the position and rotation
            handle.transform.localPosition = positionMeters;
            handle.transform.localRotation = Quaternion.Euler(0, 0, element.RotationDegrees);
            handle.transform.localScale = Vector3.one;

            // Update the bounding box
            LineRenderer lineRenderer = handle.GetComponent<LineRenderer>();
            if (lineRenderer != null)
            {
                UpdateBoundingBoxRenderer(lineRenderer, element);
            }
        }

        /// <summary>
        /// Configures the bounding box renderer for the specified UI element.
        /// </summary>
        private void ConfigureBoundingBoxRenderer(LineRenderer lineRenderer, CardElement element)
        {
            if (lineRenderer == null) return;

            // Configure LineRenderer
            lineRenderer.positionCount = 5; // 4 corners + close loop
            lineRenderer.loop = true;
            lineRenderer.useWorldSpace = false; // Use local coordinates relative to this element
            lineRenderer.startWidth = 0.001f; // Thin line (adjust as needed)
            lineRenderer.endWidth = 0.001f;
            lineRenderer.material = new Material(Shader.Find("Legacy Shaders/Particles/Alpha Blended Premultiply")); // Simple unlit material
            lineRenderer.startColor = Color.magenta;
            lineRenderer.endColor = Color.magenta;
            lineRenderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
            lineRenderer.receiveShadows = false;
            lineRenderer.sortingOrder = 1; // Render slightly above element surface if needed

            UpdateBoundingBoxRenderer(lineRenderer, element);
        }

        /// <summary>
        /// Updates the bounding box renderer for the specified UI element.
        /// </summary>
        private void UpdateBoundingBoxRenderer(LineRenderer lineRenderer, CardElement element)
        {
            if (lineRenderer == null) return;

            float halfWidth = UnitConverter.MmToMeters(element.WidthMM) / 2f;
            float halfHeight = UnitConverter.MmToMeters(element.HeightMM) / 2f;

            // Define corners in local space (Z=0)
            Vector3[] positions = new Vector3[5];
            positions[0] = new Vector3(-halfWidth, halfHeight, 0);  // Top-Left
            positions[1] = new Vector3(halfWidth, halfHeight, 0);   // Top-Right
            positions[2] = new Vector3(halfWidth, -halfHeight, 0);  // Bottom-Right
            positions[3] = new Vector3(-halfWidth, -halfHeight, 0); // Bottom-Left
            positions[4] = positions[0]; // Close the loop

            lineRenderer.SetPositions(positions);
        }

        /// <summary>
        /// Updates the UI element's position and rotation based on the world space handle.
        /// </summary>
        /// <param name="element">The UI element to update</param>
        /// <param name="handle">The world space handle</param>
        public void UpdateElementFromHandle(CardElement element, GameObject handle)
        {
            if (element == null || handle == null) return;

            // Convert position from meters to mm
            Vector2 positionMM = new Vector2(
                UnitConverter.MetersToMm(handle.transform.localPosition.x),
                UnitConverter.MetersToMm(handle.transform.localPosition.y)
            );

            // Update the UI element
            element.PositionMM = positionMM;
            element.RotationDegrees = handle.transform.localEulerAngles.z;
            element.Update();
        }

        /// <summary>
        /// Removes the world space handle for the specified UI element.
        /// </summary>
        /// <param name="element">The UI element</param>
        public void RemoveWorldSpaceHandle(CardElement element)
        {
            if (element == null) return;

            if (m_WorldSpaceHandles.TryGetValue(element, out GameObject handle))
            {
                if (handle != null)
                {
                    Object.Destroy(handle);
                }
                m_WorldSpaceHandles.Remove(element);
            }
        }

        /// <summary>
        /// Removes all world space handles.
        /// </summary>
        public void RemoveAllWorldSpaceHandles()
        {
            foreach (var handle in m_WorldSpaceHandles.Values)
            {
                if (handle != null)
                {
                    Object.Destroy(handle);
                }
            }
            m_WorldSpaceHandles.Clear();
        }
    }
}

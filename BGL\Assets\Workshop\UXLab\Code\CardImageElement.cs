using UnityEngine;
using UnityEngine.UIElements;

namespace Workshop.UXLab
{
    /// <summary>
    /// UI element that displays an image on a card.
    /// </summary>
    public class CardImageElement : CardElement
    {
        // Properties
        public Texture2D Texture { get; set; }
        public Texture2D MainTexture { get { return Texture; } set { Texture = value; } } // Alias for compatibility
        public Color TintColor { get; set; } = Color.white;
        public ScaleMode ScaleMode { get; set; } = ScaleMode.ScaleToFit;

        // UI Elements
        private Image m_Image;

        /// <summary>
        /// Creates a new CardImageElement.
        /// </summary>
        /// <param name="name">The name of the element</param>
        /// <param name="renderTarget">The render target to add the element to</param>
        /// <param name="texture">The texture to display</param>
        public CardImageElement(string name, CardRenderTarget renderTarget, Texture2D texture = null)
            : base(name, renderTarget)
        {
            Texture = texture;

            // Create the image element
            m_Image = new Image();
            m_Image.style.width = new StyleLength(new Length(100, LengthUnit.Percent));
            m_Image.style.height = new StyleLength(new Length(100, LengthUnit.Percent));
            m_Image.style.backgroundColor = new StyleColor(new Color(255, 255, 255));
            m_Image.pickingMode = PickingMode.Ignore;

            // Add to root element
            m_RootElement.Add(m_Image);

            // Update content
            UpdateContent();
        }

        /// <summary>
        /// Updates the image content.
        /// </summary>
        public override void UpdateContent()
        {
            if (m_Image == null) return;

            // Set the texture
            m_Image.image = Texture;

            // Set the tint color
            m_Image.tintColor = TintColor;

            // Set the scale mode
            m_Image.scaleMode = ScaleMode;
        }
    }
}

using UnityEngine;
using HighlightPlus;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine.EventSystems;

namespace Workshop.UXLab
{
    [CreateAssetMenu(fileName = "CardInteractionSystem", menuName = "Workshop/UXLab/Systems/CardInteractionSystem")]
    public class CardInteractionSystem : TableSystem
    {
        [Header("Hover Settings")]
        [SerializeField] private Color m_HoverHighlightColor = new Color(0.8f, 0.8f, 0.2f, 0.3f);
        [SerializeField] private Color m_SelectedHighlightColor = new Color(0.2f, 0.8f, 0.2f, 0.5f);

        [Header("Drag Settings")]
        [SerializeField] private float m_DragHeight = 0.05f; // Height above table when dragging
        [SerializeField] private float m_DragStartDelay = 0.1f; // Short delay before drag starts
        [SerializeField] private float m_DropAnimationDuration = 0.3f; // Duration of drop animation
        [SerializeField] private AnimationCurve m_DropAnimationCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

        [Header("Drag Thresholds")]
        [SerializeField] private float m_DragStartDistanceThreshold = 5.0f; // Pixels mouse must move before drag starts

        [Header("Drag Tilt Settings")]
        [SerializeField] private float m_MaxDragTiltAngle = 7.0f;
        [SerializeField] private float m_DragTiltSensitivity = 0.05f;
        [SerializeField] private float m_DragTiltSpeed = 10.0f;
        [SerializeField] private bool m_ResetTiltOnDrop = true;

        [Header("Drag Position Settings")]
        [SerializeField] private float m_DragPositionLerpSpeed = 10.0f;

        [Header("Card Interaction Keys")]
        [SerializeField] private KeyCode m_TapKey = KeyCode.R;
        [SerializeField] private KeyCode m_UntapKey = KeyCode.E;
        [SerializeField] private KeyCode m_FlipKey = KeyCode.F;

        [Header("Deck Interaction Keys")]
        [SerializeField] private KeyCode m_ShuffleDeckKey = KeyCode.H;

        [Header("Dynamic Deck Creation")]
        [SerializeField] private GameObject m_DefaultCardPrefabForDynamicDecks; // Assign a default card prefab in Inspector (optional)

        // State tracking
        private GameObject m_SelectedCard; // Primary selected card (for backward compatibility)
        private List<GameObject> m_SelectedCards = new List<GameObject>(); // All selected cards for multi-selection
        private GameObject m_HoveredCard;
        private Vector3 m_SelectionStartPosition;

        // Dictionary to store the final positions of cards when they're deselected
        private Dictionary<GameObject, Vector3> m_CardFinalPositions = new Dictionary<GameObject, Vector3>();
        private Quaternion m_SelectionStartRotation;
        private Vector3 m_DragStartPosition;
        private Quaternion m_DragStartRotation;
        private bool m_WasDraggingLastFrame;

        // Card rotation state tracking
        private Dictionary<GameObject, bool> m_TappedCards = new Dictionary<GameObject, bool>();
        private Dictionary<GameObject, bool> m_FlippedCards = new Dictionary<GameObject, bool>();
        private Dictionary<GameObject, Quaternion> m_OriginalRotations = new Dictionary<GameObject, Quaternion>();
        private Dictionary<GameObject, Vector3> m_OriginalScales = new Dictionary<GameObject, Vector3>();

        // Reference to the CardAreaSystem for area constraints
        private CardAreaSystem m_CardAreaSystem;

        // Reference to the AnimationSystem for animations
        private AnimationSystem m_AnimationSystem;

        // Drag state
        private bool m_IsDragging = false;
        private float m_LastClickTime;
        private Vector3 m_DragOffset;
        private Plane m_DragPlane;
        private Vector3 m_MouseDownScreenPosition; // Added to store mouse down position for drag threshold

        private Vector3 m_PreviousMousePositionForTilt;
        private Quaternion m_CurrentAppliedTiltRotation = Quaternion.identity;
        private Vector3 m_CurrentSmoothedDragPosition;

        // Events
        public delegate void CardSelectedHandler(GameObject cardObject);
        public event CardSelectedHandler OnCardSelected;

        public delegate void CardDeselectedHandler(GameObject cardObject);
        public event CardDeselectedHandler OnCardDeselected;

        public delegate void MultipleCardsSelectedHandler(List<GameObject> cardObjects);
        public event MultipleCardsSelectedHandler OnMultipleCardsSelected;

        public delegate void CardsDeselectedHandler();
        public event CardsDeselectedHandler OnCardsDeselected;

        public delegate void CardDragStartedHandler(GameObject cardObject);
        public event CardDragStartedHandler OnCardDragStarted;

        public delegate void CardDragEndedHandler(GameObject cardObject, Vector3 newPosition);
        public event CardDragEndedHandler OnCardDragEnded;

        public delegate void CardMovedHandler(GameObject cardObject, Vector3 newPosition);
        public event CardMovedHandler OnCardMoved;

        public delegate void CardTappedHandler(GameObject cardObject, bool isTapped);
        public event CardTappedHandler OnCardTapped;

        public delegate void CardFlippedHandler(GameObject cardObject, bool isFlipped);
        public event CardFlippedHandler OnCardFlipped;

        // Properties
        public bool IsCardSelected => m_SelectedCard != null;
        public GameObject SelectedCard => m_SelectedCard;
        public bool IsDragging => m_IsDragging;
        public List<GameObject> SelectedCards => new List<GameObject>(m_SelectedCards); // Return a copy to prevent external modification
        public bool HasMultipleCardsSelected => m_SelectedCards.Count > 1;

        /// <summary>
        /// Checks if the specified card is currently tapped (based on tracked state)
        /// </summary>
        /// <remarks>
        /// This is a logical state rather than a physical one - it tracks whether the card
        /// was last rotated with TapCard (true) or UntapCard (false), regardless of its actual rotation.
        /// </remarks>
        public bool IsCardTapped(GameObject card) => card != null && m_TappedCards.ContainsKey(card) && m_TappedCards[card];

        /// <summary>
        /// Checks if the specified card is currently flipped (showing back side)
        /// </summary>
        public bool IsCardFlipped(GameObject card) => card != null && m_FlippedCards.ContainsKey(card) && m_FlippedCards[card];

        /// <summary>
        /// Checks if the specified card is currently considered to be visually levitated due to selection or dragging.
        /// </summary>
        /// <param name="card">The card to check.</param>
        /// <returns>True if the card is selected and at a drag/levitation height, false otherwise.</returns>
        public bool IsCardVisuallyLevitated(GameObject card)
        {
            if (card == null || m_SelectedCard != card)
                return false;

            // If dragging, it's definitely visually levitated.
            if (m_IsDragging)
                return true;

            // If selected but not dragging, check if its Z position is significantly above its resting height.
            // This implies it's in the post-selection levitated state before a drag starts or before it's dropped.
            float currentZ = card.transform.position.z;
            float restingZ = GetCardRestingHeight(card);
            float expectedLevitatedZ = restingZ - m_DragHeight;

            // Use a small tolerance for floating point comparisons
            float tolerance = 0.001f;
            return Mathf.Abs(currentZ - expectedLevitatedZ) < tolerance;
        }

        /// <summary>
        /// Gets the animation system used by this interaction system
        /// </summary>
        /// <returns>The animation system, or null if not available</returns>
        public AnimationSystem GetAnimationSystem() => m_AnimationSystem;

        /// <summary>
        /// Gets the proper resting height for a card on the table
        /// </summary>
        /// <param name="card">The card to get the resting height for</param>
        /// <returns>The proper Z coordinate for the card at rest</returns>
        public float GetCardRestingHeight(GameObject card)
        {
            // If this is the selected card, use the selection start position's Z value
            if (card == m_SelectedCard && m_SelectionStartPosition != Vector3.zero)
            {
                return m_SelectionStartPosition.z;
            }

            // If we have a stored final position for this card, use that
            if (m_CardFinalPositions.TryGetValue(card, out Vector3 finalPosition))
            {
                return finalPosition.z;
            }

            // Fallback: Use 0 for the table height
            return 0f;
        }

        public override void Init(TableManager tableManager)
        {
            base.Init(tableManager);

            // Subscribe to hover and click events
            m_TableManager.OnComponentHovered += HandleComponentHovered;
            m_TableManager.OnComponentClicked += HandleComponentClicked;
            m_TableManager.OnBlankClicked += HandleBlankClicked;

            // Subscribe to the manager's update event for drag processing
            m_TableManager.OnUpdate += ProcessInteractionsUpdate;

            // We'll initialize the drag plane dynamically when dragging starts
            m_DragPlane = new Plane(Vector3.forward, Vector3.zero);

            // Get reference to the CardAreaSystem if available
            m_CardAreaSystem = m_TableManager.GetSystem<CardAreaSystem>();
            if (m_CardAreaSystem == null)
            {
                Debug.LogWarning("CardInteractionSystem: CardAreaSystem not found. Card area constraints will not be applied.", this);
            }

            // Get reference to the AnimationSystem if available
            m_AnimationSystem = m_TableManager.GetSystem<AnimationSystem>();
            if (m_AnimationSystem == null)
            {
                Debug.LogError("CardInteractionSystem: AnimationSystem not found. Card animations require AnimationSystem.", this);
            }
        }

        public override void Shutdown()
        {
            if (!m_IsInitialized)
                return;

            // Unsubscribe from events
            if (m_TableManager != null)
            {
                m_TableManager.OnComponentHovered -= HandleComponentHovered;
                m_TableManager.OnComponentClicked -= HandleComponentClicked;
                m_TableManager.OnBlankClicked -= HandleBlankClicked;
                m_TableManager.OnUpdate -= ProcessInteractionsUpdate;
            }

            // Clean up any selected card
            if (m_SelectedCard != null)
            {
                StopAnimationOnCard(m_SelectedCard);

                // Return the card to its original position
                Vector3 finalPosition = m_SelectedCard.transform.position;
                finalPosition.z = m_SelectionStartPosition.z;
                m_SelectedCard.transform.position = finalPosition;

                SetHighlighted(m_SelectedCard, false);
                m_SelectedCard = null;
                m_IsDragging = false;
            }

            // Clear all state dictionaries
            m_TappedCards.Clear();
            m_FlippedCards.Clear();
            m_OriginalRotations.Clear();
            m_CardFinalPositions.Clear();
            m_OriginalScales.Clear();

            base.Shutdown();
        }

        private void HandleComponentHovered(GameObject component)
        {
            GameObject newHoveredCard = null;
            if (component != null && component.GetComponent<CardMeshGenerator>() != null)
            {
                newHoveredCard = component;
            }

            // If the actual hovered card under the mouse has changed from our last known m_HoveredCard
            if (m_HoveredCard != newHoveredCard)
            {
                // Remove highlight from the card we are NO LONGER hovering
                // (only if it wasn't the selected card, whose highlight is managed by selection logic)
                if (m_HoveredCard != null && m_HoveredCard != m_SelectedCard)
                {
                    SetHighlighted(m_HoveredCard, false);
                }

                // Update our tracked hovered card
                m_HoveredCard = newHoveredCard;
            }

            // Now, ensure the CURRENT m_HoveredCard (if any) has the correct highlight.
            // This covers:
            // 1. A new card is hovered: m_HoveredCard was just updated.
            // 2. Same card is still hovered: m_HoveredCard didn't change, but its selection status (m_SelectedCard) might have.
            // 3. Hovering off to null: m_HoveredCard is null, nothing to highlight here.
            if (m_HoveredCard != null)
            {
                if (m_HoveredCard == m_SelectedCard)
                {
                    // If the hovered card is also the selected card, it should have the 'selected' highlight.
                    SetHighlighted(m_HoveredCard, true, true);
                }
                else
                {
                    // If the hovered card is not the selected card, it should have the 'hover' highlight.
                    SetHighlighted(m_HoveredCard, true, false); // isSelected = false for hover
                }
            }
        }

        private void HandleComponentClicked(GameObject component)
        {
            // Only process cards (objects with CardMeshGenerator)
            if (component == null || component.GetComponent<CardMeshGenerator>() == null)
                return;

            // Don't allow selecting a card that's currently being animated
            if (!CanInteractWithCard(component))
            {
                Debug.Log($"Ignoring selection of card {component.name} as it's currently being animated");
                return;
            }

            bool isClickOnSelected = (m_SelectedCard == component);
            GameObject previouslySelectedCard = m_SelectedCard;

            // If a drag operation just finished in the previous frame update (i.e., mouse was released)
            if (m_WasDraggingLastFrame)
            {
                if (isClickOnSelected) // Dragged and dropped ON ITSELF
                {
                    // Card is at its new position. This position should become its new resting position.
                    m_SelectionStartPosition = m_SelectedCard.transform.position;
                    m_SelectionStartRotation = m_SelectedCard.transform.rotation;

                    // Card remains selected.
                    m_LastClickTime = Time.time;
                    CalculateDragOffset(m_SelectedCard);
                    m_MouseDownScreenPosition = Input.mousePosition; // Store mouse down position
                    return; // Stay selected, no further action.
                }
                else // Dragged 'previouslySelectedCard' and dropped/released mouse ON 'component'
                {
                    if (previouslySelectedCard != null) // Should always be true if m_WasDraggingLastFrame related to a card
                    {
                        // Deselect the previous card
                        DeselectCard(previouslySelectedCard);
                    }

                    // Now, select the new 'component'.
                    m_SelectedCard = null;
                    SelectCardInternal(component); // This will set new m_SelectionStartPosition for 'component'
                    m_MouseDownScreenPosition = Input.mousePosition; // Store mouse down position for new selection
                    return;
                }
            }

            // --- Standard click logic (not a drag release) ---

            if (isClickOnSelected)
            {
                // Clicked on the already selected card: prepare for a drag operation.
                // Cancel any ongoing animation for this card
                StopAnimationOnCard(m_SelectedCard);

                m_LastClickTime = Time.time;
                CalculateDragOffset(m_SelectedCard); // Recalculate offset based on current mouse and card state
                m_MouseDownScreenPosition = Input.mousePosition; // Store mouse down position
                return;
            }

            // Clicked on a new card, or no card was selected before.
            // Deselect the 'previouslySelectedCard' if it exists.
            if (previouslySelectedCard != null)
            {
                // Deselect the previous card
                DeselectCard(previouslySelectedCard);
            }

            // Select the new card.
            SelectCardInternal(component);
            m_MouseDownScreenPosition = Input.mousePosition; // Store mouse down position for new selection
        }

        /// <summary>
        /// Deselects a card and handles any necessary cleanup
        /// </summary>
        private void DeselectCard(GameObject cardToDeselect)
        {
            if (cardToDeselect == null)
                return;

            StopAnimationOnCard(cardToDeselect);

            Vector3 originalScale = m_OriginalScales.GetValueOrDefault(cardToDeselect, cardToDeselect.transform.localScale);

            CardArea bestArea = null;
            if (m_CardAreaSystem != null)
            {
                bestArea = m_CardAreaSystem.GetCardAreaAtPosition(cardToDeselect.transform.position);
            }

            CardDropResult dropResult = CardDropResult.NoAction();
            bool handledByArea = false;

            if (bestArea != null)
            {
                dropResult = bestArea.HandleCardDropped(cardToDeselect, cardToDeselect.transform.position);
                if (dropResult.ShouldAnimateDrop)
                {
                    Vector3 animationTargetPosition = dropResult.Position;
                    Quaternion finalRotation = dropResult.UseSpecificRotation ? dropResult.Rotation : cardToDeselect.transform.rotation;
                    _ = m_AnimationSystem.AnimateCardPositionAndRotation(cardToDeselect, animationTargetPosition, finalRotation, m_DropAnimationDuration, m_DropAnimationCurve, true, originalScale);
                    m_CardFinalPositions[cardToDeselect] = animationTargetPosition;
                    bestArea.AddCard(cardToDeselect, m_AnimationSystem, m_DropAnimationDuration);
                    handledByArea = true;
                }
            }

            if (!handledByArea)
            {
                Vector3 currentPosition = cardToDeselect.transform.position;
                Vector3 finalTablePosition = new Vector3(currentPosition.x, currentPosition.y, GetCardRestingHeight(cardToDeselect));

                // Animate to this final table position, using its CURRENT visual rotation.
                _ = m_AnimationSystem.AnimateCardPositionAndRotation(cardToDeselect, finalTablePosition, cardToDeselect.transform.rotation, m_DropAnimationDuration, m_DropAnimationCurve, true, originalScale);
                m_CardFinalPositions[cardToDeselect] = finalTablePosition;
            }

            SetHighlighted(cardToDeselect, false);
            OnCardDeselected?.Invoke(cardToDeselect);

            // If the card we just deselected was the one we thought was hovered,
            // temporarily null out m_HoveredCard. This will force HandleComponentHovered
            // to re-evaluate it as a "new" hover event if the mouse is still on it,
            // ensuring the hover highlight is applied correctly after deselection.
            if (m_HoveredCard == cardToDeselect)
            {
                m_HoveredCard = null;
            }
        }

        private void SelectCardInternal(GameObject cardToSelect)
        {
            if (cardToSelect == null || cardToSelect.GetComponent<CardMeshGenerator>() == null)
                return;

            // Capture the card's current visual state when selected.
            Vector3 initialVisualPosition = cardToSelect.transform.position;
            Quaternion currentVisualRotation = cardToSelect.transform.rotation;

            // Check if the card is in any card area and remove it, adjusting its rotation if so.
            CardArea[] cardAreas = FindObjectsOfType<CardArea>(); // Consider optimizing if many areas
            foreach (CardArea area in cardAreas)
            {
                if (area.IsCardAssociated(cardToSelect))
                {
                    area.RemoveCard(cardToSelect); // Area handles unparenting etc.
                    Debug.Log($"CardInteractionSystem: Card {cardToSelect.name} removed from area {area.name} on selection", this);

                    // Adopt the area's base rotation as the card's current visual rotation for selection purposes.
                    currentVisualRotation = area.transform.rotation;
                    cardToSelect.transform.rotation = currentVisualRotation; // Visually snap rotation before animation

                    // Optional: Snap to area's center X,Y - decide if this is desired
                    // initialVisualPosition = new Vector3(area.transform.position.x, area.transform.position.y, initialVisualPosition.z);
                    // cardToSelect.transform.position = initialVisualPosition; // Visually snap position if changed
                    break;
                }
            }

            m_SelectedCard = cardToSelect;

            if (cardToSelect == null || m_SelectedCard != cardToSelect) // Re-check due to potential modifications
              return;

            // Add to the selected cards list if not already there
            if (!m_SelectedCards.Contains(m_SelectedCard))
            {
                m_SelectedCards.Add(m_SelectedCard);
            }

            // Store original scale BEFORE any elevation scaling
            if (!m_OriginalScales.ContainsKey(m_SelectedCard))
            {
                m_OriginalScales[m_SelectedCard] = m_SelectedCard.transform.localScale;
            }
            else // Update if already exists, to capture current true base scale before this specific selection event
            {
                m_OriginalScales[m_SelectedCard] = m_SelectedCard.transform.localScale;
            }

            m_SelectionStartPosition = initialVisualPosition;
            m_SelectionStartRotation = currentVisualRotation; // Use current (potentially area-aligned) rotation for selection levitation

            // Determine and store the "original" (neutral, un-tapped, un-flipped) world rotation.
            // This is derived from its current visual state (which might now be area-aligned)
            // and its own known tapped/flipped status.
            if (!m_OriginalRotations.ContainsKey(m_SelectedCard))
            {
                Quaternion baseNeutralRotation = m_SelectionStartRotation; // Start with the (potentially area-aligned) current rotation

                if (IsCardTapped(m_SelectedCard))
                {
                    baseNeutralRotation *= Quaternion.Euler(0, 0, 90);
                }

                if (IsCardFlipped(m_SelectedCard))
                {
                    baseNeutralRotation *= Quaternion.Euler(0, 180, 0);
                }
                m_OriginalRotations[m_SelectedCard] = baseNeutralRotation;
            }

            SetHighlighted(m_SelectedCard, true, true);
            OnCardSelected?.Invoke(m_SelectedCard);

            float levitatedZ = GetCardRestingHeight(m_SelectedCard) - m_DragHeight;
            Vector3 raisedPosition = new Vector3(m_SelectionStartPosition.x, m_SelectionStartPosition.y, levitatedZ);

            StopAnimationOnCard(m_SelectedCard);
            // Animate to the raised position, using its current (potentially area-aligned and snapped) rotation.
            // Do not pass targetAbsoluteScale here, let AnimationSystem elevate and scale it up based on Z change.
            AnimateCardToPosition(m_SelectedCard, raisedPosition, m_SelectionStartRotation, m_DropAnimationDuration);

            m_LastClickTime = Time.time;
            m_MouseDownScreenPosition = Input.mousePosition; // Store mouse down position after selection animation starts
            CalculateDragOffset(m_SelectedCard);
        }

        /// <summary>
        /// Selects a card for interaction
        /// </summary>
        /// <param name="card">The card to select</param>
        public void SelectCard(GameObject card)
        {
            if (card == null || card.GetComponent<CardMeshGenerator>() == null)
                return;

            // Don't allow selecting a card that's currently being animated
            if (!CanInteractWithCard(card))
            {
                Debug.Log($"Ignoring selection of card {card.name} as it's currently being animated");
                return;
            }

            // If there's already a selected card, deselect it first
            if (m_SelectedCard != null && m_SelectedCard != card)
            {
                DeselectCard(m_SelectedCard);
                m_SelectedCard = null;
            }

            // Select the new card
            SelectCardInternal(card);
        }

        /// <summary>
        /// Selects multiple cards at once
        /// </summary>
        /// <param name="cards">The list of cards to select</param>
        public void SelectMultipleCards(List<GameObject> cards)
        {
            if (cards == null || cards.Count == 0)
                return;

            // Deselect any currently selected cards first
            if (m_SelectedCard != null)
            {
                DeselectCard(m_SelectedCard);
                m_SelectedCard = null;
            }

            // Clear the selected cards list
            foreach (GameObject card in new List<GameObject>(m_SelectedCards))
            {
                DeselectCard(card);
            }
            m_SelectedCards.Clear();

            // Select the first card as the primary selected card
            SelectCardInternal(cards[0]);

            // Add the rest of the cards to the selected cards list
            for (int i = 1; i < cards.Count; i++)
            {
                GameObject card = cards[i];
                if (card != null && card.GetComponent<CardMeshGenerator>() != null && !m_SelectedCards.Contains(card))
                {
                    // Add to the selected cards list
                    m_SelectedCards.Add(card);

                    // Highlight the card
                    SetHighlighted(card, true, true);

                    // Levitate the card
                    float levitatedZ = GetCardRestingHeight(card) - m_DragHeight;
                    Vector3 raisedPosition = new Vector3(card.transform.position.x, card.transform.position.y, levitatedZ);
                    AnimateCardToPosition(card, raisedPosition, card.transform.rotation, m_DropAnimationDuration);
                }
            }

            // Notify listeners about the multiple selection
            OnMultipleCardsSelected?.Invoke(m_SelectedCards);
        }

        private void HandleBlankClicked()
        {
            m_LastClickTime = Time.time;

            // Nothing to do if no cards are selected
            if (m_SelectedCard == null && m_SelectedCards.Count == 0) return;

            // If we have a primary selected card, deselect it
            if (m_SelectedCard != null)
            {
                GameObject cardToDeselect = m_SelectedCard;
                m_SelectedCard = null; // Clear the selected card before deselecting

                // Deselect the card
                //DeselectCard(cardToDeselect);
            }

            // Deselect any other selected cards
            if (m_SelectedCards.Count > 0)
            {
                // Create a copy of the list to avoid modification during iteration
                List<GameObject> cardsToDeselect = new List<GameObject>(m_SelectedCards);

                foreach (GameObject card in cardsToDeselect)
                {
                    if (card != null)
                    {
                        DeselectCard(card);
                    }
                }

                // Clear the selected cards list
                m_SelectedCards.Clear();

                // Notify listeners that all cards have been deselected
                OnCardsDeselected?.Invoke();
            }
        }

        private void ProcessInteractionsUpdate()
        {
            // Reset the flag at the beginning of each frame.
            // It will be set to true in ProcessCardDrag if a drag ends in this frame.
            m_WasDraggingLastFrame = false;

            // Process card dragging if a card is selected
            if (m_SelectedCard != null)
            {
                ProcessCardDrag();
            }

            // Determine which card to use for tapping/untapping/flipping
            GameObject targetCard = m_SelectedCard;

            // If no card is selected, try to use the hovered card
            if (targetCard == null && m_HoveredCard != null && m_HoveredCard.GetComponent<CardMeshGenerator>() != null)
            {
                targetCard = m_HoveredCard;
            }

            // Process card tapping/untapping/flipping with key input if we have a valid target
            if (targetCard != null && CanInteractWithCard(targetCard))
            {
                if (Input.GetKeyDown(m_TapKey))
                {
                    _ = TapCard(targetCard);
                }
                else if (Input.GetKeyDown(m_UntapKey))
                {
                    _ = UntapCard(targetCard);
                }
                else if (Input.GetKeyDown(m_FlipKey))
                {
                    _ = FlipCard(targetCard);
                }
            }

            // Process deck shuffling with key input
            if (Input.GetKeyDown(m_ShuffleDeckKey))
            {
                GameObject currentHover = m_TableManager.CurrentHoveredComponent;
                if (currentHover != null)
                {
                    CardDeck deckToShuffle = currentHover.GetComponent<CardDeck>();

                    if (deckToShuffle == null && currentHover.transform.parent != null) // Check parent if current is not deck
                    {
                        deckToShuffle = currentHover.transform.parent.GetComponent<CardDeck>();
                    }

                    // Check if the hovered component is a card within a deck
                    if (deckToShuffle == null)
                    {
                        CardLayout hoveredCardLayout = currentHover.GetComponent<CardLayout>();
                        if (hoveredCardLayout != null && hoveredCardLayout.transform.parent != null)
                        {
                            // Check if this card is actually part of the CardDeck's m_Cards list
                            CardDeck potentialDeck = hoveredCardLayout.transform.parent.GetComponent<CardDeck>();
                            if (potentialDeck != null && potentialDeck.IsCardInDeck(hoveredCardLayout))
                            {
                                deckToShuffle = potentialDeck;
                            }
                        }
                    }

                    if (deckToShuffle != null)
                    {
                        Debug.Log($"CardInteractionSystem: Shuffle key '{m_ShuffleDeckKey}' pressed on/near deck '{deckToShuffle.name}'. Initiating animated shuffle.", this);
                        _ = deckToShuffle.ShuffleAnimated();
                    }
                }
            }
        }

        private void ProcessCardDrag()
        {
            if (Input.GetMouseButtonDown(0))
            {
                m_LastClickTime = Time.time;
            }
            // Check if the left mouse button is held down
            if (Input.GetMouseButton(0) && !EventSystem.current.IsPointerOverGameObject())
            {
                // Start dragging only if enough time has passed since click AND mouse has moved enough
                bool timeThresholdMet = (Time.time - m_LastClickTime) > m_DragStartDelay;
                bool distanceThresholdMet = (Input.mousePosition - m_MouseDownScreenPosition).magnitude > m_DragStartDistanceThreshold;

                if (!m_IsDragging && timeThresholdMet && distanceThresholdMet)
                {
                    // Cancel any ongoing animation for this card
                    StopAnimationOnCard(m_SelectedCard);

                    m_IsDragging = true;
                    m_TableManager.CameraController?.SetMousePanEnabled(false);

                    // Store the state before drag starts
                    m_DragStartPosition = m_SelectionStartPosition;
                    m_DragStartRotation = m_SelectionStartRotation;

                    // Initialize for tilt calculation
                    m_PreviousMousePositionForTilt = Input.mousePosition;
                    m_CurrentAppliedTiltRotation = Quaternion.identity;

                    // Card is already levitated from selection, so we just need to store the current position
                    float dragZ = m_SelectionStartPosition.z - m_DragHeight;
                    Vector3 currentPosition = m_SelectedCard.transform.position;

                    // Make sure the card is at the correct height (in case it was moved)
                    if (Mathf.Abs(currentPosition.z - dragZ) > 0.001f)
                    {
                        currentPosition.z = dragZ;
                        m_SelectedCard.transform.position = currentPosition;
                    }

                    m_SelectedCard.transform.rotation = m_DragStartRotation;

                    // Initialize smoothed drag position
                    m_CurrentSmoothedDragPosition = m_SelectedCard.transform.position;

                    m_DragPlane = new Plane(Vector3.forward, new Vector3(0, 0, dragZ));

                    CalculateDragOffset(m_SelectedCard); // Recalculate offset now that card is raised
                    OnCardDragStarted?.Invoke(m_SelectedCard);
                }

                if (m_IsDragging)
                {
                    Camera camera = m_TableManager.CameraController.Camera;
                    if (camera == null) return;

                    // Drag plane should remain at the consistent drag depth
                    float dragZ = m_SelectionStartPosition.z - m_DragHeight;
                    m_DragPlane = new Plane(Vector3.forward, new Vector3(0, 0, dragZ));

                    Ray ray = camera.ScreenPointToRay(Input.mousePosition);
                    if (m_DragPlane.Raycast(ray, out float enter))
                    {
                        Vector3 hitPointWorld = ray.GetPoint(enter);
                        Vector3 targetWorldPosition = hitPointWorld + m_DragOffset;
                        targetWorldPosition.z = dragZ;

                        // Apply card area constraints if CardAreaSystem is available
                        if (m_CardAreaSystem != null)
                        {
                            targetWorldPosition = m_CardAreaSystem.ConstrainPositionToArea(m_SelectedCard, targetWorldPosition);
                        }

                        // Smoothly update card position
                        m_CurrentSmoothedDragPosition = Vector3.Lerp(m_CurrentSmoothedDragPosition, targetWorldPosition, Time.deltaTime * m_DragPositionLerpSpeed);
                        m_SelectedCard.transform.position = m_CurrentSmoothedDragPosition;

                        // --- Apply Drag Tilt ---
                        Vector3 mouseDelta = Input.mousePosition - m_PreviousMousePositionForTilt;
                        m_PreviousMousePositionForTilt = Input.mousePosition;

                        // Tilt around World Z-axis based on horizontal mouse movement.
                        // Positive mouseDelta.x (moving right) should result in a tilt to the right.
                        // Assuming standard Unity Euler: Z rotation is roll. Positive Z = counter-clockwise.
                        // If card's +X is right, +Y is up (screen relative), then positive Z rotation makes it lean left.
                        // So, we might need to negate the delta or sensitivity.
                        float targetZTiltAngle = -mouseDelta.x * m_DragTiltSensitivity;
                        targetZTiltAngle = Mathf.Clamp(targetZTiltAngle, -m_MaxDragTiltAngle, m_MaxDragTiltAngle);

                        Quaternion targetInstantTilt = Quaternion.Euler(0, 0, targetZTiltAngle);

                        // Smoothly update the currently applied tilt
                        m_CurrentAppliedTiltRotation = Quaternion.Slerp(m_CurrentAppliedTiltRotation, targetInstantTilt, Time.deltaTime * m_DragTiltSpeed);

                        // Apply to the card: base orientation on table + current dynamic tilt
                        m_SelectedCard.transform.rotation = m_DragStartRotation * m_CurrentAppliedTiltRotation;
                        // --- End Apply Drag Tilt ---

                        // Notify listeners about card movement
                        OnCardMoved?.Invoke(m_SelectedCard, m_CurrentSmoothedDragPosition);
                    }
                }
            }
            else if (Input.GetMouseButtonUp(0) && !EventSystem.current.IsPointerOverGameObject())
            {
                if (m_IsDragging)
                {
                    m_TableManager.CameraController?.SetMousePanEnabled(true);

                    Vector3 finalDropPositionWorld = m_CurrentSmoothedDragPosition;
                    // Z will be set by area or to resting height

                    OnCardDragEnded?.Invoke(m_SelectedCard, finalDropPositionWorld);

                    bool cardHandledByArea = false;
                    CardArea targetArea = null;

                    // --- Dynamic Deck Creation Check ---
                    GameObject underlyingObject = GetObjectUnderCard(m_SelectedCard);
                    CardMeshGenerator underlyingCardGenerator = null;
                    if (underlyingObject != null) underlyingObject.TryGetComponent(out underlyingCardGenerator);

                    // Check if: an object is under the card, it's a card, it's not the dragged card itself,
                    // and neither card is already in an existing area (like another deck).
                    if (underlyingCardGenerator != null && m_SelectedCard != underlyingObject &&
                        DoCardsOverlap(m_SelectedCard, underlyingObject) && // Check for bounding box overlap
                        !IsCardInAnyArea(m_SelectedCard) && !IsCardInAnyArea(underlyingObject))
                    {
                        Debug.Log($"CardInteractionSystem: Attempting dynamic deck creation with {m_SelectedCard.name} and {underlyingObject.name} due to overlap.", this);
                        _ = AttemptDynamicDeckCreation(m_SelectedCard, underlyingObject);
                        // AttemptDynamicDeckCreation will handle deselection and other states.
                        cardHandledByArea = true; // Effectively handled by dynamic deck creation
                        m_IsDragging = false;
                        m_WasDraggingLastFrame = true;
                        // m_SelectedCard will be nulled out by AttemptDynamicDeckCreation after animation
                        return; // Exit ProcessCardDrag early
                    }
                    // --- End Dynamic Deck Creation Check ---

                    if (m_CardAreaSystem != null)
                    {
                        targetArea = m_CardAreaSystem.GetCardAreaAtPosition(finalDropPositionWorld);
                    }

                    CardDropResult dropResult = CardDropResult.NoAction();

                    if (targetArea != null)
                    {
                        dropResult = targetArea.HandleCardDropped(m_SelectedCard, finalDropPositionWorld);
                        if (dropResult.ShouldAnimateDrop)
                        {
                            Vector3 animationTargetPosition = dropResult.Position;
                            Quaternion animationTargetRotation = dropResult.UseSpecificRotation ? dropResult.Rotation : m_SelectedCard.transform.rotation;
                            _ = m_AnimationSystem.AnimateCardPositionAndRotation(m_SelectedCard, animationTargetPosition, animationTargetRotation, m_DropAnimationDuration, m_DropAnimationCurve, true, m_OriginalScales.GetValueOrDefault(m_SelectedCard, m_SelectedCard.transform.localScale));
                            targetArea.AddCard(m_SelectedCard, m_AnimationSystem, m_DropAnimationDuration);
                            m_CardFinalPositions[m_SelectedCard] = animationTargetPosition;
                            cardHandledByArea = true;

                            SetHighlighted(m_SelectedCard, false);
                            OnCardDeselected?.Invoke(m_SelectedCard);
                            m_SelectedCard = null; // Deselected as it's handled by an area
                        }
                    }

                    if (!cardHandledByArea)
                    {
                        // Standard drop on table, not into a specific area that took control
                        GameObject cardToDrop = m_SelectedCard; // Capture for clarity
                        StopAnimationOnCard(cardToDrop);
                        finalDropPositionWorld.z = GetCardRestingHeight(cardToDrop);
                        Quaternion finalRotation = m_ResetTiltOnDrop ? m_DragStartRotation : cardToDrop.transform.rotation;
                        _ = m_AnimationSystem.AnimateCardPositionAndRotation(cardToDrop, finalDropPositionWorld, finalRotation, m_DropAnimationDuration, m_DropAnimationCurve, true, m_OriginalScales.GetValueOrDefault(cardToDrop, cardToDrop.transform.localScale));
                        m_CardFinalPositions[cardToDrop] = finalDropPositionWorld;

                        SetHighlighted(cardToDrop, false); // Turn off selected highlight first
                        OnCardDeselected?.Invoke(cardToDrop);

                        if (m_SelectedCard == cardToDrop)
                        {
                            m_SelectedCard = null;
                        }

                        // Since the drag just ended on this card and it's not in an area,
                        // it is now the hovered card (assuming mouse is still there).
                        // Directly set its state and highlight.
                        m_HoveredCard = cardToDrop;
                        SetHighlighted(m_HoveredCard, true, false); // Apply hover highlight
                    }

                    m_IsDragging = false;
                    m_WasDraggingLastFrame = true;
                    // m_SelectedCard might be null here if it was dropped into an area
                }
            }
        }

        private void CalculateDragOffset(GameObject card)
        {
            if (card == null) return;

            // Get the camera
            Camera camera = m_TableManager.CameraController.Camera;
            if (camera == null) return;

            // First, raycast against the card to get the hit point
            Ray ray = camera.ScreenPointToRay(Input.mousePosition);

            if (Physics.Raycast(ray, out RaycastHit hit) && hit.collider.gameObject == card)
            {
                // Now raycast against our drag plane to get the plane intersection point
                if (m_DragPlane.Raycast(ray, out float enter))
                {
                    Vector3 planeHitPoint = ray.GetPoint(enter);

                    // Calculate offset from plane hit point to card center
                    // This gives us the correct offset in world space
                    m_DragOffset = card.transform.position - planeHitPoint;

                    // Keep only the XY components for dragging on the plane
                    m_DragOffset.z = 0;

                    // Debug.Log($"Drag offset calculated: {m_DragOffset}");
                }
                else
                {
                    // Fallback if plane raycast fails
                    m_DragOffset = card.transform.position - hit.point;
                    m_DragOffset.z = 0;
                    // Debug.LogWarning("Using fallback drag offset calculation");
                }
            }
            else
            {
                // Default to no offset if raycast fails
                m_DragOffset = Vector3.zero;
                // Debug.LogWarning("Failed to calculate drag offset - no hit detected");
            }
        }

        private void SetHighlighted(GameObject obj, bool highlighted, bool isSelected = false)
        {
            if (obj == null) return;

            HighlightEffect highlightEffect = obj.GetComponent<HighlightEffect>();
            if (highlightEffect == null)
            {
                highlightEffect = obj.AddComponent<HighlightEffect>();
            }

            if (highlighted)
            {
                Color highlightColor = isSelected ? m_SelectedHighlightColor : m_HoverHighlightColor;
                highlightEffect.SetHighlighted(true);
                highlightEffect.outlineColor = highlightColor;
                highlightEffect.overlayColor = highlightColor;
                highlightEffect.overlayBlending = 1.0f;
            }
            else
            {
                highlightEffect.SetHighlighted(false);
            }
        }

        // Consolidated animation methods
        /// <summary>
        /// Animates a card to a specified position
        /// </summary>
        /// <param name="card">The card to animate</param>
        /// <param name="targetPosition">The target position</param>
        /// <param name="duration">The duration of the animation</param>
        /// <returns>True if the animation was started successfully</returns>
        public bool AnimateCardToPosition(GameObject card, Vector3 targetPosition, float duration)
        {
            if (card == null) return false;

            if (m_AnimationSystem == null)
            {
                Debug.LogError("CardInteractionSystem: Cannot animate card position - AnimationSystem is null.", this);
                return false;
            }

            // Use the animation system to animate the card's position only
            // This overload won't support the new explicit scale setting. Consider if it needs it or if it's deprecated by the other.
            _ = m_AnimationSystem.AnimateCardPosition(card, targetPosition, duration, m_DropAnimationCurve);
            return true;
        }

        /// <summary>
        /// Animates a card to a specified position and rotation
        /// </summary>
        /// <param name="card">The card to animate</param>
        /// <param name="targetPosition">The target position</param>
        /// <param name="targetRotation">The target rotation</param>
        /// <param name="duration">The duration of the animation</param>
        /// <param name="targetAbsoluteScale">Optional: The absolute scale the card should animate to.</param>
        /// <returns>True if the animation was started successfully</returns>
        public bool AnimateCardToPosition(GameObject card, Vector3 targetPosition, Quaternion targetRotation, float duration, Vector3? targetAbsoluteScale = null)
        {
            if (card == null) return false;

            if (m_AnimationSystem == null)
            {
                Debug.LogError("CardInteractionSystem: Cannot animate card position and rotation - AnimationSystem is null.", this);
                return false;
            }

            // Use the new combined animation method from AnimationSystem
            _ = m_AnimationSystem.AnimateCardPositionAndRotation(card, targetPosition, targetRotation, duration, m_DropAnimationCurve, false, targetAbsoluteScale);
            return true;
        }

        /// <summary>
        /// Stops any current animations for the selected card
        /// </summary>
        private void StopAnimationOnCard(GameObject card)
        {
            if (m_AnimationSystem != null && card != null)
            {
                m_AnimationSystem.CancelAnimations(card);
            }
        }

        /// <summary>
        /// Checks if a card can be safely interacted with.
        /// </summary>
        /// <param name="card">The card to check</param>
        /// <returns>True if the card can be interacted with, false otherwise</returns>
        public bool CanInteractWithCard(GameObject card)
        {
            if (card == null)
                return false;

            if (m_AnimationSystem == null)
            {
                Debug.LogError("CardInteractionSystem: Cannot check if card can be interacted with - AnimationSystem is null.");
                return true; // Default to allowing interaction if we can't check
            }

            return !m_AnimationSystem.IsObjectAnimating(card);
        }

        /// <summary>
        /// Taps a card by rotating it 90 degrees counterclockwise around the Z axis
        /// </summary>
        /// <param name="card">The card to tap</param>
        /// <returns>True if the card was tapped successfully</returns>
        public async Task<bool> TapCard(GameObject card)
        {
            if (card == null || card.GetComponent<CardMeshGenerator>() == null)
                return false;

            // Check if the card can be interacted with
            if (!CanInteractWithCard(card))
                return false;

            // Store the original rotation if we don't have it yet
            if (!m_OriginalRotations.ContainsKey(card))
            {
                m_OriginalRotations[card] = card.transform.rotation;
            }

            // Update the tapped state - always true when tapping
            m_TappedCards[card] = true;

            if (m_AnimationSystem == null)
            {
                Debug.LogError("CardInteractionSystem: Cannot tap card - AnimationSystem is null.", this);
                return false;
            }

            // Use the animation system to tap the card
            bool animationSuccess = await m_AnimationSystem.TapCard(card);

            if (animationSuccess && m_IsDragging && card == m_SelectedCard)
            {
                m_DragStartRotation = m_SelectedCard.transform.rotation;
                m_CurrentAppliedTiltRotation = Quaternion.identity;
            }

            // Notify listeners
            OnCardTapped?.Invoke(card, true);

            return true;
        }

        /// <summary>
        /// Untaps a card by rotating it 90 degrees clockwise around the Z axis
        /// </summary>
        /// <param name="card">The card to untap</param>
        /// <returns>True if the card was untapped successfully</returns>
        public async Task<bool> UntapCard(GameObject card)
        {
            if (card == null || card.GetComponent<CardMeshGenerator>() == null)
                return false;

            // Check if the card can be interacted with
            if (!CanInteractWithCard(card))
                return false;

            // Store the original rotation if we don't have it yet
            if (!m_OriginalRotations.ContainsKey(card))
            {
                m_OriginalRotations[card] = card.transform.rotation;
            }

            // Update the tapped state - always false when untapping
            m_TappedCards[card] = false;

            if (m_AnimationSystem == null)
            {
                Debug.LogError("CardInteractionSystem: Cannot untap card - AnimationSystem is null.", this);
                return false;
            }

            // Use the animation system to untap the card
            bool animationSuccess = await m_AnimationSystem.UntapCard(card);

            if (animationSuccess && m_IsDragging && card == m_SelectedCard)
            {
                m_DragStartRotation = m_SelectedCard.transform.rotation;
                m_CurrentAppliedTiltRotation = Quaternion.identity;
            }

            // Notify listeners
            OnCardTapped?.Invoke(card, false);

            return true;
        }

        /// <summary>
        /// Flips a card by rotating it 180 degrees around the Y axis
        /// </summary>
        /// <param name="card">The card to flip</param>
        /// <returns>True if the card flip animation was started successfully</returns>
        public async Task<bool> FlipCard(GameObject card)
        {
            if (card == null || card.GetComponent<CardMeshGenerator>() == null)
                return false;

            // Check if the card can be interacted with
            if (!CanInteractWithCard(card))
                return false;

            // Store the original rotation if we don't have it yet
            if (!m_OriginalRotations.ContainsKey(card))
            {
                m_OriginalRotations[card] = card.transform.rotation;
            }

            // Toggle the flipped state
            bool newFlippedState = !IsCardFlipped(card);
            m_FlippedCards[card] = newFlippedState;

            if (m_AnimationSystem == null)
            {
                Debug.LogError("CardInteractionSystem: Cannot flip card - AnimationSystem is null.", this);
                return false;
            }

            // Use the animation system to flip the card
            bool animationSuccess = await m_AnimationSystem.FlipCard(card, newFlippedState);

            if (animationSuccess && m_IsDragging && card == m_SelectedCard)
            {
                m_DragStartRotation = m_SelectedCard.transform.rotation;
                m_CurrentAppliedTiltRotation = Quaternion.identity;
            }

            // Notify listeners
            OnCardFlipped?.Invoke(card, newFlippedState);

            return true;
        }

        // Helper to check if a card is in any CardArea
        private bool IsCardInAnyArea(GameObject card)
        {
            if (card == null) return false;
            CardArea[] allAreas = FindObjectsOfType<CardArea>(); // Consider performance if many areas
            foreach (CardArea area in allAreas)
            {
                if (area.IsCardAssociated(card))
                {
                    return true;
                }
            }
            return false;
        }

        private GameObject GetObjectUnderCard(GameObject draggedCard)
        {
            // 1. Raycast from mouse position
            if (draggedCard == null || m_TableManager.CameraController == null || m_TableManager.CameraController.Camera == null)
                return null;

            Camera camera = m_TableManager.CameraController.Camera;
            Ray ray = camera.ScreenPointToRay(Input.mousePosition);
            RaycastHit[] hits = Physics.RaycastAll(ray, 100f);

            GameObject objectDirectlyUnderMouse = null;
            float closestDist = float.MaxValue;

            foreach (RaycastHit hit in hits)
            {
                if (hit.collider.gameObject != draggedCard && hit.collider.gameObject.GetComponent<UnityEngine.EventSystems.EventSystem>() == null)
                {
                    // Check if it's a card (has CardMeshGenerator)
                    if (hit.collider.gameObject.GetComponent<CardMeshGenerator>() != null)
                    {
                        if (hit.distance < closestDist)
                        {
                            objectDirectlyUnderMouse = hit.collider.gameObject;
                            closestDist = hit.distance;
                        }
                    }
                }
            }

            if (objectDirectlyUnderMouse != null)
            {
                // If we found a card directly under the mouse, return it.
                // The ProcessCardDrag logic will then do its own DoCardsOverlap check.
                return objectDirectlyUnderMouse;
            }

            // 2. If no card under mouse, iterate TableManager.TableComponents for overlaps
            if (TableManager.Instance == null) return null;

            foreach (GameObject tableComponent in TableManager.Instance.TableComponents)
            {
                if (tableComponent == draggedCard || tableComponent == null) continue;
                if (tableComponent.GetComponent<CardMeshGenerator>() == null) continue; // Only consider cards

                if (DoCardsOverlap(draggedCard, tableComponent)) // This will use the 2D version
                {
                    // Found an overlapping card that is not the dragged card itself.
                    // Return the first one found.
                    return tableComponent;
                }
            }

            return null; // No card found under mouse or overlapping
        }

        /// <summary>
        /// Checks if the bounding boxes of two card GameObjects overlap in 2D (X and Y axes).
        /// </summary>
        /// <param name="card1">The first card GameObject.</param>
        /// <param name="card2">The second card GameObject.</param>
        /// <returns>True if their colliders' bounds intersect on the XY plane, false otherwise.</returns>
        private bool DoCardsOverlap(GameObject card1, GameObject card2)
        {
            if (card1 == null || card2 == null)
            {
                return false;
            }

            Collider card1Collider = card1.GetComponent<Collider>();
            Collider card2Collider = card2.GetComponent<Collider>();

            if (card1Collider == null || card2Collider == null)
            {
                if (card1Collider == null) Debug.LogWarning($"CardInteractionSystem: Card '{card1.name}' is missing a Collider component for overlap check.", card1);
                if (card2Collider == null) Debug.LogWarning($"CardInteractionSystem: Card '{card2.name}' is missing a Collider component for overlap check.", card2);
                return false;
            }

            Bounds bounds1 = card1Collider.bounds;
            Bounds bounds2 = card2Collider.bounds;

            // 2D Overlap Check (ignoring Z)
            bool overlapX = bounds1.min.x < bounds2.max.x && bounds1.max.x > bounds2.min.x;
            bool overlapY = bounds1.min.y < bounds2.max.y && bounds1.max.y > bounds2.min.y;

            return overlapX && overlapY;
        }

        private async Task AttemptDynamicDeckCreation(GameObject droppedCard, GameObject targetCardOnTable)
        {
            if (droppedCard == null || targetCardOnTable == null || m_AnimationSystem == null)
            {
                Debug.LogError("CardInteractionSystem: Cannot create dynamic deck. Null reference detected for cards or animation system.", this);
                // Fallback: Deselect the dropped card normally if deck creation fails early
                if (m_SelectedCard == droppedCard)
                {
                    Vector3 finalTablePosition = new Vector3(droppedCard.transform.position.x, droppedCard.transform.position.y, GetCardRestingHeight(droppedCard));
                    Quaternion finalRotation = m_ResetTiltOnDrop ? m_DragStartRotation : droppedCard.transform.rotation;
                    _ = m_AnimationSystem.AnimateCardPositionAndRotation(droppedCard, finalTablePosition, finalRotation, m_DropAnimationDuration, m_DropAnimationCurve, true, m_OriginalScales.GetValueOrDefault(droppedCard, droppedCard.transform.localScale));
                    m_CardFinalPositions[droppedCard] = finalTablePosition;

                    SetHighlighted(droppedCard, false);
                    OnCardDeselected?.Invoke(droppedCard);
                    m_SelectedCard = null;
                    m_HoveredCard = droppedCard;
                    SetHighlighted(m_HoveredCard, true, false);
                }
                return;
            }

            CardLayout droppedCardLayout = droppedCard.GetComponent<CardLayout>();
            CardLayout targetCardLayout = targetCardOnTable.GetComponent<CardLayout>();

            if (droppedCardLayout == null || targetCardLayout == null)
            {
                Debug.LogError("CardInteractionSystem: One or both cards lack CardLayout component. Cannot create dynamic deck.", this);
                 if (m_SelectedCard == droppedCard)
                {
                    Vector3 finalTablePosition = new Vector3(droppedCard.transform.position.x, droppedCard.transform.position.y, GetCardRestingHeight(droppedCard));
                    Quaternion finalRotation = m_ResetTiltOnDrop ? m_DragStartRotation : droppedCard.transform.rotation;
                    _ = m_AnimationSystem.AnimateCardPositionAndRotation(droppedCard, finalTablePosition, finalRotation, m_DropAnimationDuration, m_DropAnimationCurve, true, m_OriginalScales.GetValueOrDefault(droppedCard, droppedCard.transform.localScale));
                    m_CardFinalPositions[droppedCard] = finalTablePosition;
                    SetHighlighted(droppedCard, false);
                    OnCardDeselected?.Invoke(droppedCard);
                    m_SelectedCard = null;
                    m_HoveredCard = droppedCard;
                    SetHighlighted(m_HoveredCard, true, false);
                }
                return;
            }

            StopAnimationOnCard(droppedCard);
            StopAnimationOnCard(targetCardOnTable);

            GameObject deckGO = new GameObject("DynamicDeck_" + System.Guid.NewGuid().ToString().Substring(0,4));
            deckGO.transform.position = targetCardOnTable.transform.position;
            deckGO.transform.rotation = targetCardOnTable.transform.rotation;

            CardDeck newDeck = deckGO.AddComponent<CardDeck>();
            newDeck.MarkAsDynamic(true); // Mark this deck for auto-cleanup when empty
            // No longer setting card prefab, as dynamic decks won't use AddNewCard() by default.
            // newDeck.SetCardPrefabForRuntimeInit(m_DefaultCardPrefabForDynamicDecks);

            Vector3 targetCardDeckLocalPos = Vector3.zero;
            Quaternion targetCardDeckLocalRot = Quaternion.identity;
            Vector3 targetCardDeckWorldPos = newDeck.transform.TransformPoint(targetCardDeckLocalPos);
            Quaternion targetCardDeckWorldRot = newDeck.transform.rotation * targetCardDeckLocalRot;

            Task animTargetCard = m_AnimationSystem.AnimateCardPositionAndRotation(targetCardOnTable, targetCardDeckWorldPos, targetCardDeckWorldRot, m_DropAnimationDuration, m_DropAnimationCurve, false, m_OriginalScales.GetValueOrDefault(targetCardOnTable, targetCardOnTable.transform.localScale));

            float cardThickness = 0.0005f;
            if (newDeck.GetReferenceCardSizeForAnim() != null) cardThickness = UnitConverter.MmToMeters(newDeck.GetReferenceCardSizeForAnim().Thickness);
            float verticalOffset = cardThickness * newDeck.GetThicknessSpacingMultiplierForAnim();
            Vector2 cumulativeOffsetStep = newDeck.GetCumulativeOffsetStepForAnim();
            float cumulativeRotationStep = newDeck.GetCumulativeRotationStepForAnim();

            Vector3 droppedCardDeckLocalPos = new Vector3(cumulativeOffsetStep.x, cumulativeOffsetStep.y, -verticalOffset);
            Quaternion droppedCardDeckLocalRot = Quaternion.Euler(0, 0, cumulativeRotationStep);
            Vector3 droppedCardDeckWorldPos = newDeck.transform.TransformPoint(droppedCardDeckLocalPos);
            Quaternion droppedCardDeckWorldRot = newDeck.transform.rotation * droppedCardDeckLocalRot;

            Task animDroppedCard = m_AnimationSystem.AnimateCardPositionAndRotation(droppedCard, droppedCardDeckWorldPos, droppedCardDeckWorldRot, m_DropAnimationDuration, m_DropAnimationCurve, false, m_OriginalScales.GetValueOrDefault(droppedCard, droppedCard.transform.localScale));

            await Task.WhenAll(animTargetCard, animDroppedCard);

            FinalizeDeckPopulationAfterAnimation(newDeck, targetCardOnTable, droppedCard);

            if (m_SelectedCard == droppedCard)
            {
                SetHighlighted(droppedCard, false);
                OnCardDeselected?.Invoke(droppedCard);
                m_CardFinalPositions.Remove(droppedCard);
                m_OriginalRotations.Remove(droppedCard);
                m_OriginalScales.Remove(droppedCard);
                m_SelectedCard = null;
            }

            HandleComponentHovered(GetObjectUnderCard(null));
        }

        private void FinalizeDeckPopulationAfterAnimation(CardDeck deck, GameObject card1, GameObject card2)
        {
            if (deck == null || card1 == null || card2 == null)
            {
                Debug.LogError("CardInteractionSystem: Cannot finalize deck population. Null reference detected.", this);
                return;
            }

            CardLayout card1Layout = card1.GetComponent<CardLayout>();
            CardLayout card2Layout = card2.GetComponent<CardLayout>();

            if (card1Layout == null || card2Layout == null)
            {
                Debug.LogError("CardInteractionSystem: Cards lack CardLayout component. Cannot add to deck.", this);
                return;
            }

            bool added1 = deck.AddCard(card1Layout);
            bool added2 = deck.AddCard(card2Layout);

            if (!added1 || !added2)
            {
                Debug.LogError($"CardInteractionSystem: Failed to add one or both cards to the new deck. Card1 added: {added1}, Card2 added: {added2}", this);
            }
            else
            {
                Debug.Log($"Successfully created dynamic deck '{deck.gameObject.name}' with {card1.name} and {card2.name}", this);
            }
        }
    }
}

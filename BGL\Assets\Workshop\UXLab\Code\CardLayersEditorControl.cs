using UnityEngine;
using UnityEngine.UIElements;
using Unity.AppUI.UI;
using System.Collections.Generic;
using System.Linq;
using System;
using Workshop.UXLab.Data; // Added for element data
using AppUIButton = Unity.AppUI.UI.Button;
using AppUIToggle = Unity.AppUI.UI.Toggle;
using MenuBuilder = Unity.AppUI.UI.MenuBuilder;
using MenuItem = Unity.AppUI.UI.MenuItem;
using Object = UnityEngine.Object;

namespace Workshop.UXLab
{
    public class CardLayersEditorControl : VisualElement
    {
        // --- Constants ---
        private const int ADD_IMAGE_ACTION = 1;
        private const int ADD_TEXT_ACTION = 2;
        private const string UxmlPath = "UI/CardLayersEditorLayout";
        private const string UssPath = "UI/CardLayersEditorStyles";
        private const string VisibleClassName = "visible"; // USS class for visibility
        private const float DRAG_START_DELAY = 0.3f; // 300ms delay before starting drag
        // Add more type IDs here...

        // UI Elements
        private DraggablePanel m_Container;
        private ListView m_LayerList;
        private AppUIButton m_AddComponentButton;
        private AppUIButton m_RemoveComponentButton;
        private AppUIButton m_MoveUpButton;
        private AppUIButton m_MoveDownButton;

        // References
        private CardLayout m_CurrentCardLayout;
        private List<CardElement> m_CurrentElements; // Using CardElement instead of CardElement
        private List<CardElement> m_CurrentComponents; // Legacy compatibility list
        private Action<CardElement> m_ShowHandlesAction; // Action provided by CardEditorSystem
        private Action m_ClearHandlesAction; // Action provided by CardEditorSystem

        // Custom drag handling
        private bool m_IsDragging = false;
        private float m_LastClickTime = 0f;
        private int m_DragStartIndex = -1;
        private Vector2 m_DragStartPosition;

        // Events
        public event Action<CardElement> OnElementSelected; // Inform other panels
        public event Action OnLayoutChanged; // Inform system if changes require redraw/resave

        public CardLayersEditorControl() // Constructor instead of Awake/OnEnable
        {
            // Load UXML
            var visualTree = Resources.Load<VisualTreeAsset>(UxmlPath);
            if (visualTree != null)
            {
                visualTree.CloneTree(this);
                 // Apply USS
                 StyleSheet styleSheet = Resources.Load<StyleSheet>(UssPath);
                 if (styleSheet != null) styleSheets.Add(styleSheet);
                 else Debug.LogError($"CardLayersEditorControl: Could not load USS from {UssPath}");
            }
            else
            {
                Debug.LogError($"CardLayersEditorControl: Failed to load UXML from {UxmlPath}");
                this.Add(new Label("Error loading Layers Editor UI."));
                return;
            }

            // Find UI elements using correct types/aliases, querying from 'this'
            m_Container = this.Q<DraggablePanel>("card-layers-container");
            m_LayerList = this.Q<ListView>("layer-list"); // Standard ListView
            m_AddComponentButton = this.Q<AppUIButton>("add-component-button"); // AppUI Button
            m_RemoveComponentButton = this.Q<AppUIButton>("remove-component-button"); // AppUI Button
            m_MoveUpButton = this.Q<AppUIButton>("move-up-button"); // AppUI Button
            m_MoveDownButton = this.Q<AppUIButton>("move-down-button"); // AppUI Button

            if (m_Container == null || m_LayerList == null || m_AddComponentButton == null ||
                m_RemoveComponentButton == null || m_MoveUpButton == null || m_MoveDownButton == null)
            {
                // More specific error logging
                if(m_Container == null) Debug.LogError("CardLayersEditorControl: Failed to find DraggablePanel 'card-layers-container'.");
                if(m_LayerList == null) Debug.LogError("CardLayersEditorControl: Failed to find ListView 'layer-list'.");
                // ... add checks for other buttons ...
                return;
            }

            // Configure ListView
            ConfigureListView();

            // Register Callbacks
            m_AddComponentButton.clicked += AddNewComponent;
            m_RemoveComponentButton.clicked += RemoveSelectedComponent;
            m_MoveUpButton.clicked += MoveSelectedComponentUp;
            m_MoveDownButton.clicked += MoveSelectedComponentDown;
            m_LayerList.selectedIndicesChanged += OnListSelectionChanged;

            // Register custom drag handlers
            m_LayerList.RegisterCallback<MouseDownEvent>(OnListMouseDown);
            m_LayerList.RegisterCallback<MouseMoveEvent>(OnListMouseMove);
            m_LayerList.RegisterCallback<MouseUpEvent>(OnListMouseUp);

            // Initial state - Set these after finding elements
            // Element starts hidden via USS by default (by not having the .visible class)
            m_Container?.AddToClassList("hidden"); // Add hidden by default
            UpdateButtonStates();

            // Initialize panel position - DraggablePanel handles this
        }

        /// <summary>
        /// Provides actions from the controlling system (e.g., CardEditorSystem).
        /// </summary>
        public void SetupActions(Action<CardElement> showHandles, Action clearHandles)
        {
            m_ShowHandlesAction = showHandles;
            m_ClearHandlesAction = clearHandles;
        }

        private void ConfigureListView()
        {
            // Set up how to create items using standard UIElements
            m_LayerList.makeItem = () => {
                var itemRow = new VisualElement() { name = "layer-item-row" };
                itemRow.AddToClassList("layer-list-item"); // Apply USS style

                // Use standard Toggle
                var visibilityToggle = new AppUIToggle() { name = "visibility-toggle" };
                visibilityToggle.AddToClassList("layer-list-item__visibility-toggle");

                // Use standard TextElement
                var label = new TextElement() { name = "component-label" };
                label.AddToClassList("layer-list-item__label");

                itemRow.Add(visibilityToggle);
                itemRow.Add(label);
                return itemRow;
            };

            // Set up how to bind data to items
            m_LayerList.bindItem = (element, index) => {
                if (m_CurrentComponents == null || index < 0 || index >= m_CurrentComponents.Count) return;

                var component = m_CurrentComponents[index];
                // Query for standard TextElement
                var label = element.Q<TextElement>("component-label");
                // Query for standard Toggle
                var toggle = element.Q<AppUIToggle>("visibility-toggle");

                if (label != null) label.text = component?.ElementName ?? "<Missing Element>"; // Use ElementName, updated message
                if (toggle != null)
                {
                    // Remove previous listener before adding a new one
                    toggle.UnregisterValueChangedCallback(OnVisibilityToggleChanged);
                    // Set value without triggering callback
                    toggle.SetValueWithoutNotify(component?.IsVisible ?? false); // Add null check
                    // Store component reference for the callback
                    toggle.userData = component;
                    // Add listener
                    toggle.RegisterValueChangedCallback(OnVisibilityToggleChanged);
                }
            };
        }

        private void OnVisibilityToggleChanged(ChangeEvent<bool> evt)
        {
            // Ensure the target is a standard Toggle
            var toggle = evt.target as AppUIToggle;
            if (toggle?.userData is CardElement component)
            {
                component.IsVisible = evt.newValue;
                OnLayoutChanged?.Invoke(); // Notify that something changed
            }
        }

        private void OnListSelectionChanged(IEnumerable<int> selectedIndices)
        {
             int selectedIndex = selectedIndices.Any() ? selectedIndices.First() : -1; // Corrected line
             CardElement selectedElement = null;

             // Ensure list isn't null before accessing
             if (m_CurrentElements != null && selectedIndex >= 0 && selectedIndex < m_CurrentElements.Count)
             {
                 selectedElement = m_CurrentElements[selectedIndex];
             }

             // ListView selection might clear when rebuilding, manually re-select if needed?
             // Notify Element Editor panel
             OnElementSelected?.Invoke(selectedElement);

             // Show/Hide handles via the system actions
             if (selectedElement != null)
             {
                 m_ShowHandlesAction?.Invoke(selectedElement);
             }
             else
             {
                 m_ClearHandlesAction?.Invoke();
             }

             UpdateButtonStates();
        }

        // Use built-in reorder events if available and appropriate
        private void OnListItemsReordered(IEnumerable<int> indices, int insertAtIndex)
        {
            if (m_CurrentCardLayout == null || m_CurrentElements == null) return;

            // --- Manual Data Source Reordering Logic ---
            var itemsToMove = indices.Select(i => m_CurrentElements[i]).ToList();

            // Create a new list representing the state *before* moving
            var currentOrder = m_CurrentElements.ToList();

            // Remove items from their original positions *in reverse order* to maintain correct indices
            foreach (var indexToRemove in indices.OrderByDescending(i => i))
            {
                if (indexToRemove >= 0 && indexToRemove < currentOrder.Count)
                {
                    currentOrder.RemoveAt(indexToRemove);
                } else {
                    Debug.LogError($"Invalid index {indexToRemove} during reorder removal.");
                    return; // Stop if index is invalid
                }
            }

            // Calculate the correct insertion index in the list *after* removal
            // `insertAtIndex` provided by the event usually refers to the index *in the view* before items are fully removed/re-added by the logic below.
            // We need to map this visual insertion point to the data list index after removals.
            // A simpler approach is often to just insert based on the target visual position in the *final* list.
            // Let's re-insert based on the adjusted target position.
            // If insertAtIndex is beyond the new count, append.
            int adjustedInsertIndex = Mathf.Clamp(insertAtIndex, 0, currentOrder.Count);

            // Insert the items at the adjusted index
            currentOrder.InsertRange(adjustedInsertIndex, itemsToMove);

            // Update the main element list
            m_CurrentElements = currentOrder;
            // --- End Manual Data Source Reordering ---

            // Update the order in the CardLayout
            for (int i = 0; i < m_CurrentElements.Count; i++)
            {
                if (m_CurrentElements[i] != null)
                {
                    // Use CardLayout's reorder method instead of directly manipulating transforms
                    m_CurrentCardLayout.ReorderElement(m_CurrentElements[i], i);
                }
                else
                {
                    Debug.LogWarning($"Element at index {i} was null during reorder.");
                }
            }

            // Update ListView source and rebuild
            m_LayerList.itemsSource = m_CurrentElements;
            m_LayerList.Rebuild(); // Crucial after changing itemsSource or order

            // Try to restore selection after rebuild
            // Find the new indices of the items that were moved.
            var newIndices = itemsToMove.Select(item => m_CurrentElements.IndexOf(item)).Where(idx => idx >= 0);
            if (newIndices.Any())
            {
                m_LayerList.SetSelection(newIndices);
            }
            else
            { // Fallback: select the item at the insertion point if possible
                if(adjustedInsertIndex < m_CurrentElements.Count)
                {
                   m_LayerList.SetSelection(adjustedInsertIndex);
                }
            }

            OnLayoutChanged?.Invoke();
            UpdateButtonStates();
        }

        // Custom drag-and-drop implementation with delay
        private void OnListMouseDown(MouseDownEvent evt)
        {
            if (m_CurrentElements == null || m_CurrentElements.Count == 0)
                return;

            // Store the time of the click
            m_LastClickTime = Time.time;

            // Store the mouse position
            m_DragStartPosition = evt.mousePosition;

            // Get the index of the item under the mouse
            int index = GetIndexFromMousePosition(evt.mousePosition);
            if (index >= 0 && index < m_CurrentElements.Count)
            {
                m_DragStartIndex = index;

                // Select the item (this will trigger OnListSelectionChanged)
                m_LayerList.SetSelection(index);
            }
            else
            {
                m_DragStartIndex = -1;
            }

            // Don't stop propagation - allow selection to work
        }

        private void OnListMouseMove(MouseMoveEvent evt)
        {
            // Only start dragging if:
            // 1. We have a valid drag start index
            // 2. Enough time has passed since the click
            // 3. We're not already dragging
            // 4. The mouse has moved a significant distance
            if (m_DragStartIndex >= 0 &&
                !m_IsDragging &&
                (Time.time - m_LastClickTime) > DRAG_START_DELAY &&
                Vector2.Distance(evt.mousePosition, m_DragStartPosition) > 5f)
            {
                StartDragging(m_DragStartIndex, evt.mousePosition);
            }

            // If we're dragging, update the drag position
            if (m_IsDragging)
            {
                UpdateDragPosition(evt.mousePosition);
            }
        }

        private void OnListMouseUp(MouseUpEvent evt)
        {
            // If we were dragging, complete the drag operation
            if (m_IsDragging)
            {
                CompleteDrag(evt.mousePosition);
            }

            // Reset drag state
            m_IsDragging = false;
            m_DragStartIndex = -1;
        }

        private int GetIndexFromMousePosition(Vector2 mousePosition)
        {
            // Convert mouse position to a position within the ListView
            Vector2 localPosition = m_LayerList.WorldToLocal(mousePosition);

            // Calculate the index based on the fixed item height
            float itemHeight = m_LayerList.fixedItemHeight;
            int index = Mathf.FloorToInt(localPosition.y / itemHeight);

            // Clamp to valid range
            return Mathf.Clamp(index, -1, m_CurrentElements.Count - 1);
        }

        private void StartDragging(int index, Vector2 mousePosition)
        {
            if (index < 0 || index >= m_CurrentElements.Count)
                return;

            m_IsDragging = true;

            // Visual feedback could be added here
            Debug.Log($"Started dragging item {index}: {m_CurrentElements[index].ElementName}");

            // Optionally create a drag visual
        }

        private void UpdateDragPosition(Vector2 mousePosition)
        {
            // Get the current index under the mouse
            int currentIndex = GetIndexFromMousePosition(mousePosition);

            // Visual feedback could be updated here
            // For example, showing a line where the item would be inserted
        }

        private void CompleteDrag(Vector2 mousePosition)
        {
            // Get the drop index
            int dropIndex = GetIndexFromMousePosition(mousePosition);

            // Only proceed if we have valid indices and they're different
            if (m_DragStartIndex >= 0 && dropIndex >= 0 &&
                m_DragStartIndex < m_CurrentElements.Count &&
                dropIndex < m_CurrentElements.Count &&
                m_DragStartIndex != dropIndex)
            {
                // Move the item in the CardLayout
                CardElement elementToMove = m_CurrentElements[m_DragStartIndex];
                m_CurrentCardLayout.ReorderElement(elementToMove, dropIndex);

                // Refresh the list and select the moved item
                RefreshComponentList();
                m_LayerList.SetSelection(dropIndex);
                m_LayerList.ScrollToItem(dropIndex);
                OnLayoutChanged?.Invoke();

                Debug.Log($"Moved item from {m_DragStartIndex} to {dropIndex}");
            }

            // Reset drag state
            m_IsDragging = false;
            m_DragStartIndex = -1;
        }

        public void SetActiveCardLayout(CardLayout layout)
        {
            m_CurrentCardLayout = layout;
            RefreshComponentList();
            ShowUI(); // Ensure panel is shown
        }

        public void ClearActiveCardLayout()
        {
            m_CurrentCardLayout = null;
            m_CurrentElements?.Clear();
            m_CurrentComponents?.Clear(); // Clear legacy list too
            // Add null checks for UI elements
            if (m_LayerList != null)
            {
                m_LayerList.itemsSource = new List<CardElement>();
                m_LayerList.Rebuild();
            }
            HideUI(); // Ensure panel is hidden
            UpdateButtonStates();
        }

        /// <summary>
        /// Refreshes the element list from the current layout.
        /// Should be called when elements are added, removed, or reordered externally.
        /// </summary>
        public void RefreshComponentList()
        {
            if (m_LayerList == null)
            {
                Debug.LogError("RefreshComponentList called but m_LayerList is null.");
                return;
            }

            List<int> selectedIndicesBeforeRefresh = m_LayerList.selectedIndices.ToList();
            CardElement selectedElementBeforeRefresh = null;
            if (selectedIndicesBeforeRefresh.Any() && m_CurrentElements != null)
            {
                int firstIndex = selectedIndicesBeforeRefresh.First();
                if (firstIndex >= 0 && firstIndex < m_CurrentElements.Count)
                {
                    selectedElementBeforeRefresh = m_CurrentElements[firstIndex];
                }
            }

            if (m_CurrentCardLayout != null)
            {
                // Get UI elements
                m_CurrentElements = m_CurrentCardLayout.GetElements();

                // Update the ListView
                m_LayerList.itemsSource = m_CurrentElements;
                m_LayerList.Rebuild();
            }
            else
            {
                m_CurrentElements?.Clear();
                m_LayerList.itemsSource = new List<CardElement>();
                m_LayerList.Rebuild();
            }

            // Attempt to restore selection
            if (selectedElementBeforeRefresh != null)
            {
                int newIndex = m_CurrentElements.IndexOf(selectedElementBeforeRefresh);
                if (newIndex >= 0)
                {
                    m_LayerList.SetSelection(newIndex);
                }
            }

            UpdateButtonStates();
        }

        // --- Action Button Handlers ---

        private void AddNewComponent()
        {
            if (m_CurrentCardLayout == null || m_AddComponentButton == null) return;

            // Use AppUI MenuBuilder
            var menu = MenuBuilder.Build(m_AddComponentButton)
                .AddAction(ADD_IMAGE_ACTION, "Image", "", HandleAddComponentSelection)
                .AddAction(ADD_TEXT_ACTION, "Text", "", HandleAddComponentSelection);

            menu.Show();
        }

        // Handler for menu item selection (Signature changed to Action<EventBase>)
        private void HandleAddComponentSelection(EventBase evt)
        {
             // Get actionId from userData of the clicked MenuItem
             if (!(evt.target is MenuItem clickedItem) || !(clickedItem.userData is int selectedActionId))
             {
                 Debug.LogError("Could not determine selected action ID from menu item.");
                 return;
             }

            string selectedTypeName = "";
            CardElement addedComponent = null;

            try
            {
                switch (selectedActionId)
                {
                    case ADD_IMAGE_ACTION:
                        selectedTypeName = "Image";
                        // Create default image element data
                        ImageElementData imageData = new ImageElementData();
                        imageData.ElementName = "New Image";
                        imageData.Width = LengthValue.FromMillimeters(63f); // Default width in mm
                        imageData.Height = LengthValue.FromMillimeters(63f); // Default height in mm
                        imageData.IsVisible = true;
                        var addedUIElement = m_CurrentCardLayout.AddElementFromData(imageData);

                        // Refresh the list and select the newly added element
                        RefreshComponentList();
                        if (addedUIElement != null)
                        {
                            int newIndex = m_CurrentElements.IndexOf(addedUIElement);
                            if (newIndex >= 0)
                            {
                                m_LayerList.SetSelection(newIndex);
                            }
                        }
                        break;
                    case ADD_TEXT_ACTION:
                        selectedTypeName = "Text";
                        // Create default text element data
                        TextElementData textData = new TextElementData();
                        textData.ElementName = "New Text";
                        textData.Width = LengthValue.FromMillimeters(40f); // Default width in mm (4cm)
                        textData.Height = LengthValue.FromMillimeters(20f); // Default height in mm (2cm)
                        textData.TextContent = "Sample Text";
                        textData.FontSize = 12f; // Slightly larger default font
                        textData.IsVisible = true;
                        var addedTextElement = m_CurrentCardLayout.AddElementFromData(textData);

                        // Refresh the list and select the newly added element
                        RefreshComponentList();
                        if (addedTextElement != null)
                        {
                            int newIndex = m_CurrentElements.IndexOf(addedTextElement);
                            if (newIndex >= 0)
                            {
                                m_LayerList.SetSelection(newIndex);
                            }
                        }
                        break;
                    // Add cases for other component types...
                    default:
                        Debug.LogWarning($"Unknown action ID selected: {selectedActionId}");
                        return;
                }

                 Debug.Log($"Selected type via menu: {selectedTypeName}");
            }
            catch (Exception ex)
            {
                 // Ensure selectedTypeName is set even on error for logging
                 // Check selectedActionId again for logging
                 if (string.IsNullOrEmpty(selectedTypeName)) {
                     if(selectedActionId == ADD_IMAGE_ACTION) selectedTypeName = "Image";
                     else if (selectedActionId == ADD_TEXT_ACTION) selectedTypeName = "Text";
                     // ... add others
                 }
                Debug.LogError($"Error adding component of type {selectedTypeName} via menu: {ex.Message}", m_CurrentCardLayout);
            }

            // Notify that layout changed (already done in AddElementFromData)
            OnLayoutChanged?.Invoke();
             // else if (!string.IsNullOrEmpty(selectedTypeName)) { ... } // Optional: Handle pending additions
        }

        // TODO: Remove this placeholder once CardLayout.AddComponent<T> is implemented
        private void AddPlaceholderComponent(string typeName)
        {
             Debug.LogWarning($"Add Component Placeholder: Creating basic element for type {typeName}.");
             CardElementData elementData;
             if (typeName == "Image")
             {
                 elementData = new ImageElementData();
                 elementData.ElementName = "New Image";
                 elementData.Width = LengthValue.FromMillimeters(50f);
                 elementData.Height = LengthValue.FromMillimeters(50f);
             }
             else // Default to Text
             {
                 TextElementData textData = new TextElementData();
                 textData.ElementName = "New Text";
                 textData.Width = LengthValue.FromMillimeters(40f);
                 textData.Height = LengthValue.FromMillimeters(20f);
                 textData.TextContent = "Sample Text";
                 elementData = textData;
             }

             m_CurrentCardLayout.AddElementFromData(elementData);
             RefreshComponentList();
             m_LayerList.selectedIndex = m_CurrentComponents.Count - 1;
             OnLayoutChanged?.Invoke();
        }

        private void RemoveSelectedComponent()
        {
            if (m_CurrentCardLayout == null || !m_LayerList.selectedIndices.Any()) return;

            int selectedIndex = m_LayerList.selectedIndices.First();
            if (selectedIndex < 0 || selectedIndex >= m_CurrentElements.Count) return;

            CardElement selectedElement = m_CurrentElements[selectedIndex];
            if (selectedElement != null)
            {
                // Use CardLayout's remove method
                m_CurrentCardLayout.RemoveElement(selectedElement);

                RefreshComponentList(); // This will clear selection and update buttons
                OnLayoutChanged?.Invoke();
            }
        }

        private void MoveSelectedComponentUp()
        {
             if (m_CurrentCardLayout == null || !m_LayerList.selectedIndices.Any()) return;
             int currentIndex = m_LayerList.selectedIndices.First();
             if (currentIndex <= 0) return; // Already at top or invalid index

             CardElement elementToMove = m_CurrentElements[currentIndex];

             // Move in the CardLayout
             m_CurrentCardLayout.ReorderElement(elementToMove, currentIndex - 1);

             // Refresh list and maintain selection
             int newIndex = currentIndex - 1;
             RefreshComponentList();
             m_LayerList.SetSelection(newIndex);
             m_LayerList.ScrollToItem(newIndex);
             OnLayoutChanged?.Invoke();
        }

        private void MoveSelectedComponentDown()
        {
             if (m_CurrentCardLayout == null || !m_LayerList.selectedIndices.Any()) return;
             int currentIndex = m_LayerList.selectedIndices.First();
              // Check bounds carefully against the *current* count after potential refresh
             if (currentIndex < 0 || currentIndex >= m_CurrentElements.Count - 1) return; // Bottom or invalid

             CardElement elementToMove = m_CurrentElements[currentIndex];

             // Move in the CardLayout
             m_CurrentCardLayout.ReorderElement(elementToMove, currentIndex + 1);

             // Refresh list and maintain selection
             int newIndex = currentIndex + 1;
             RefreshComponentList();
             m_LayerList.SetSelection(newIndex);
             m_LayerList.ScrollToItem(newIndex);
             OnLayoutChanged?.Invoke();
        }

        private void UpdateButtonStates()
        {
             // Add null check for list view before accessing index
             bool elementSelected = m_LayerList != null && m_LayerList.selectedIndices.Any() && m_CurrentElements != null && m_LayerList.selectedIndices.First() < m_CurrentElements.Count;
             int selectedIndex = elementSelected ? m_LayerList.selectedIndices.First() : -1;

             bool canMoveUp = elementSelected && selectedIndex > 0;
             // Ensure Count is checked *after* null check
             bool canMoveDown = elementSelected && m_CurrentElements != null && selectedIndex < m_CurrentElements.Count - 1;

             // Add null checks for buttons
             m_RemoveComponentButton?.SetEnabled(elementSelected);
             m_MoveUpButton?.SetEnabled(canMoveUp);
             m_MoveDownButton?.SetEnabled(canMoveDown);

             // Add button is always enabled if a card is active
             m_AddComponentButton?.SetEnabled(m_CurrentCardLayout != null);
        }

        // --- Visibility Control (Using Display Style) ---
        public void ShowUI()
        {
             if (m_Container == null) return;
            // Use classes for visibility control
            m_Container.RemoveFromClassList("hidden"); // Remove hidden
            m_Container.AddToClassList(VisibleClassName);
            m_Container.pickingMode = PickingMode.Position; // Make sure it's interactable

             // Re-focus the list if it exists, useful after showing
             // m_LayerList?.Focus();
        }

        public void HideUI()
        {
            if (m_Container == null) return;
            // Use classes for visibility control
            m_Container.RemoveFromClassList(VisibleClassName);
            m_Container.AddToClassList("hidden"); // Add hidden
            m_Container.pickingMode = PickingMode.Ignore; // Make it non-interactable
        }

        /// <summary>
        /// Selects an element in the layer list by index.
        /// </summary>
        public void SelectElement(int index)
        {
            if (m_CurrentElements == null || index < 0 || index >= m_CurrentElements.Count)
                return;

            m_LayerList.SetSelection(index);
        }
    }
}

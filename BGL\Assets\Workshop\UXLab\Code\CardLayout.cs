using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System; // Added for Type
using Workshop.UXLab.Data; // Added for data structures
using TMPro; // Added for TextMeshPro
using UnityEngine.UIElements; // Added for UI Toolkit

#if UNITY_EDITOR
using UnityEditor; // Needed for AssetDatabase
#endif

namespace Workshop.UXLab
{
    /// <summary>
    /// Manages the collection of UI elements associated with a card.
    /// Resides on the same GameObject as the CardMeshGenerator.
    /// Applies layout data from a CardLayoutData asset.
    /// </summary>
    [RequireComponent(typeof(CardMeshGenerator))]
    public class CardLayout : MonoBehaviour
    {
        private CardLayoutData m_AppliedLayoutData; // Keep track of the applied layout
        private CardRenderTarget m_RenderTarget; // Reference to the card's render target
        private List<CardElement> m_UIElements = new List<CardElement>(); // List of UI elements

        /// <summary>
        /// Gets the currently applied CardLayoutData asset.
        /// </summary>
        public CardLayoutData AppliedLayoutData => m_AppliedLayoutData;

        /// <summary>
        /// Gets the list of UI elements.
        /// </summary>
        public IReadOnlyList<CardElement> UIElements => m_UIElements;

        // --- Public Property to get CardMeshGenerator ---
        private CardMeshGenerator m_CardMeshGenerator;
        public CardMeshGenerator CardMeshGenerator
        {
            get
            {
                // Lazy load and cache the component
                if (m_CardMeshGenerator == null)
                {
                    m_CardMeshGenerator = GetComponent<CardMeshGenerator>();
                }
                return m_CardMeshGenerator;
            }
        }
        // -------------------------------------------------

        /// <summary>
        /// Initializes the render target for this card layout.
        /// </summary>
        private void InitializeRenderTarget()
        {
            if (m_RenderTarget != null) return;

            // Get the CardRenderSystem from TableManager
            TableManager tableManager = TableManager.Instance;
            if (tableManager == null)
            {
                Debug.LogError("Cannot initialize render target: TableManager instance not found", this);
                return;
            }

            CardRenderSystem renderSystem = tableManager.GetSystem<CardRenderSystem>();
            if (renderSystem == null)
            {
                Debug.LogError("Cannot initialize render target: CardRenderSystem not found in TableManager", this);
                return;
            }

            // Get or create a render target for the card
            m_RenderTarget = renderSystem.GetRenderTarget(CardMeshGenerator);
            if (m_RenderTarget == null)
            {
                Debug.LogError("Failed to get render target for card", this);
                return;
            }
        }

        /// <summary>
        /// Applies a card layout definition from a CardLayoutData asset.
        /// Clears existing elements and creates new ones based on the data.
        /// </summary>
        /// <param name="layoutData">The layout data asset to apply.</param>
        public void ApplyLayoutData(CardLayoutData layoutData)
        {
            if (layoutData == null)
            {
                Debug.LogError("Cannot apply null CardLayoutData.", this);
                return;
            }
            m_AppliedLayoutData = layoutData;

            // Initialize render target if needed
            InitializeRenderTarget();
            if (m_RenderTarget == null)
            {
                Debug.LogError("Cannot apply layout: Render target initialization failed.", this);
                return;
            }

            // Clear existing elements first
            ClearElements();

            // Create elements from data
            if (layoutData.Elements != null)
            {
                for (int i = 0; i < layoutData.Elements.Count; i++)
                {
                    CardElementData elementData = layoutData.Elements[i];
                    if (elementData != null)
                    {
                        CreateUIElementFromData(elementData, i);
                    }
                    else
                    {
                        //Debug.LogWarning($"Null element data found at index {i} in layout '{layoutData.name}'. Skipping.", this);
                    }
                }
            }
            Debug.Log($"Applied layout '{layoutData.LayoutName}' with {layoutData.Elements?.Count ?? 0} elements.", this);
            NotifyLayoutModified(null);
        }

        /// <summary>
        /// Removes all UI elements from the render target.
        /// </summary>
        public void ClearElements()
        {
            // Remove all UI elements
            foreach (var element in m_UIElements)
            {
                if (element != null)
                {
                    element.Remove();
                }
            }

            // Clear the list
            m_UIElements.Clear();
        }

        /// <summary>
        /// Creates and configures a CardElement based on provided data.
        /// </summary>
        private CardElement CreateUIElementFromData(CardElementData data, int index)
        {
            if (data == null) return null;

            // Ensure render target is initialized
            if (m_RenderTarget == null)
            {
                InitializeRenderTarget();
                if (m_RenderTarget == null)
                {
                    Debug.LogError("Cannot create UI element: Render target initialization failed.", this);
                    return null;
                }
            }

            // --- Get Card Dimensions (in MM) ---
            CardMeshGenerator cardMeshGen = this.CardMeshGenerator; // Use the cached property
            if (cardMeshGen == null)
            {
                Debug.LogError("CardLayout requires a CardMeshGenerator component on the same GameObject to calculate percentage layouts.", this);
                return null;
            }

            // Get dimensions from CardSizeData
            float cardWidthMM = cardMeshGen.CurrentSizeData.Width;
            float cardHeightMM = cardMeshGen.CurrentSizeData.Height;
            // -------------------------------------

            // --- Calculate Position/Size in MM using LengthValue system ---
            // Convert position offset from LengthValue to millimeters
            float elementOffsetX_mm = data.PositionX.ToMillimeters(cardWidthMM);
            float elementOffsetY_mm = data.PositionY.ToMillimeters(cardHeightMM);
            Vector3 elementOffsetMM = new Vector3(elementOffsetX_mm, elementOffsetY_mm, 0f);

            // Convert size from LengthValue to millimeters
            float widthMM = data.Width.ToMillimeters(cardWidthMM);
            float heightMM = data.Height.ToMillimeters(cardHeightMM);

            // Calculate anchor position in MM (relative to card center 0,0)
            Vector2 cardAnchorPointRelativeToCardCenterMM = GetAnchorOffset(data.Anchor, cardWidthMM, cardHeightMM);

            // Calculate the element's anchor point relative to the card's center
            // This is (card's anchor) + (element's offset from its anchor, specified in data.Position)
            Vector2 elementAnchorRelativeToCardCenterMM = new Vector2(
                cardAnchorPointRelativeToCardCenterMM.x + elementOffsetMM.x,
                cardAnchorPointRelativeToCardCenterMM.y + elementOffsetMM.y
            );

            Vector2 positionMM = elementAnchorRelativeToCardCenterMM;
            // -----------------------------------------------

            CardElement element = null;
            Type elementType = data.GetElementType();

            // Create the appropriate UI element type
            if ((elementType == typeof(CardImageElement) || elementType.Name == "ImageCardElement") && data is ImageElementData imageElementData)
            {
                // Load texture
                Texture2D texture = null;
                #if UNITY_EDITOR
                if (!string.IsNullOrEmpty(imageElementData.TextureGuid))
                {
                    string path = AssetDatabase.GUIDToAssetPath(imageElementData.TextureGuid);
                    if (!string.IsNullOrEmpty(path))
                    {
                        texture = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                        if (texture == null)
                        {
                            Debug.LogWarning($"Could not load Texture2D from path '{path}' (GUID: {imageElementData.TextureGuid}) for element '{data.ElementName}'.", this);
                        }
                    }
                    else
                    {
                        Debug.LogWarning($"Could not find asset path for Texture GUID '{imageElementData.TextureGuid}' for element '{data.ElementName}'.", this);
                    }
                }
                #else
                // Runtime loading (e.g., Addressables, Resources) would go here
                if (!string.IsNullOrEmpty(imageElementData.TextureGuid)) {
                    Debug.LogWarning($"Runtime loading for Texture GUID {imageElementData.TextureGuid} not implemented.");
                }
                #endif

                // Create image element
                CardImageElement imageElement = new CardImageElement(data.ElementName ?? $"Element_{index}", m_RenderTarget, texture);
                imageElement.WidthMM = widthMM;
                imageElement.HeightMM = heightMM;
                imageElement.PositionMM = positionMM;
                imageElement.RotationDegrees = data.RotationDegrees;
                imageElement.TintColor = imageElementData.TintColor;
                imageElement.IsVisible = data.IsVisible;
                imageElement.Update();

                element = imageElement;
            }
            else if ((elementType == typeof(CardTextElement) || elementType.Name == "TextCardElement") && data is TextElementData textElementData)
            {
                // Create text element
                CardTextElement textElement = new CardTextElement(data.ElementName ?? $"Element_{index}", m_RenderTarget, textElementData.TextContent);
                textElement.WidthMM = widthMM;
                textElement.HeightMM = heightMM;
                textElement.PositionMM = positionMM;
                textElement.RotationDegrees = data.RotationDegrees;
                textElement.FontSize = textElementData.FontSize;
                textElement.TextColor = textElementData.FontColor;
                textElement.Alignment = textElementData.Alignment;
                textElement.AutoFit = true; // Enable auto-fit by default
                textElement.IsVisible = data.IsVisible;

                // Load font (not directly supported by CardTextElement, but could be added)
                #if UNITY_EDITOR
                if (!string.IsNullOrEmpty(textElementData.FontName))
                {
                    // Note: CardTextElement doesn't directly support TMP_FontAsset
                    // We could potentially convert to Font if needed or extend CardTextElement
                    Debug.LogWarning($"TMP_FontAsset loading not implemented for CardTextElement. Using default font for '{data.ElementName}'.", this);
                }
                #endif

                textElement.Update();
                element = textElement;
            }
            else
            {
                Debug.LogWarning($"UI element creation for type '{elementType?.Name ?? "null"}' not implemented. Skipping element '{data.ElementName}'.", this);
                return null;
            }

            // Add to the list of UI elements
            if (element != null)
            {
                m_UIElements.Add(element);

                // Register with the helper
                CardElementHelper.RegisterElement(element, this);
            }

            return element;
        }

        /// <summary>
        /// Calculates the offset (in mm) of a specific anchor point relative to the card's top left (0,0).
        /// </summary>
        public static Vector2 GetAnchorOffset(CardAnchorPoint anchor, float cardWidthMM, float cardHeightMM)
        {
            switch (anchor)
            {
                case CardAnchorPoint.TopLeft:       return new Vector2(0f, 0f);
                case CardAnchorPoint.TopCenter:     return new Vector2(cardWidthMM * 0.5f, 0f);
                case CardAnchorPoint.TopRight:      return new Vector2(cardWidthMM, 0f);
                case CardAnchorPoint.MiddleLeft:    return new Vector2(0f, cardHeightMM * 0.5f);
                case CardAnchorPoint.MiddleCenter:  return new Vector2(cardWidthMM * 0.5f, cardHeightMM * 0.5f);
                case CardAnchorPoint.MiddleRight:   return new Vector2(cardWidthMM, cardHeightMM * 0.5f);
                case CardAnchorPoint.BottomLeft:    return new Vector2(0f, cardHeightMM);
                case CardAnchorPoint.BottomCenter:  return new Vector2(cardWidthMM * 0.5f, cardHeightMM);
                case CardAnchorPoint.BottomRight:   return new Vector2(cardWidthMM, cardHeightMM);
                default:                            return Vector2.zero; // Default to top-left
            }
        }

        /// <summary>
        /// Gets all UI elements in this card layout.
        /// </summary>
        /// <returns>A list of CardElement instances.</returns>
        public List<CardElement> GetElements()
        {
            return new List<CardElement>(m_UIElements);
        }

        /// <summary>
        /// Gets all UI elements in this card layout.
        /// Alias for GetElements() for compatibility.
        /// </summary>
        /// <returns>A list of CardElement instances.</returns>
        public List<CardElement> GetUIElements()
        {
            return GetElements();
        }

        /// <summary>
        /// Gets the index of an element in the layout.
        /// </summary>
        /// <param name="element">The element to find.</param>
        /// <returns>The index of the element, or -1 if not found.</returns>
        public int GetElementIndex(CardElement element)
        {
            return m_UIElements.IndexOf(element);
        }

        /// <summary>
        /// Finds a specific UI element by its name.
        /// </summary>
        /// <param name="elementName">The name to search for.</param>
        /// <returns>The found CardElement or null.</returns>
        public CardElement GetElementByName(string elementName)
        {
            return m_UIElements.FirstOrDefault(e => e.ElementName == elementName);
        }

        /// <summary>
        /// Removes a UI element from the layout.
        /// </summary>
        public void RemoveElement(CardElement element)
        {
            if (element != null && m_UIElements.Contains(element))
            {
                // Remove from the render target
                element.Remove();

                // Unregister from the helper
                CardElementHelper.UnregisterElement(element);

                // Remove from the list
                m_UIElements.Remove(element);

                NotifyLayoutModified(null);
                Debug.Log($"Removed element '{element.ElementName}'.", this);
            }
            else if (element != null)
            {
                Debug.LogWarning($"Cannot remove element '{element.ElementName}' as it's not part of this layout.", this);
            }
        }

        /// <summary>
        /// Changes the order of a UI element.
        /// </summary>
        public void ReorderElement(CardElement element, int newIndex)
        {
            if (element != null && m_UIElements.Contains(element))
            {
                int currentIndex = m_UIElements.IndexOf(element);
                if (currentIndex != newIndex)
                {
                    // Clamp index to valid range
                    newIndex = Mathf.Clamp(newIndex, 0, m_UIElements.Count - 1);

                    // Remove and re-insert at new index
                    m_UIElements.Remove(element);
                    m_UIElements.Insert(newIndex, element);

                    // Update the render order in the UI document
                    if (m_RenderTarget != null && m_RenderTarget.UIDocument != null)
                    {
                        // This would require additional implementation in CardRenderTarget
                        // to support changing the order of elements in the visual tree

                        // For now, just update the element to refresh it
                        element.Update();
                    }

                    NotifyLayoutModified(null);
                    Debug.Log($"Reordered element '{element.ElementName}' from index {currentIndex} to {newIndex}.", this);
                }
            }
            else if (element != null)
            {
                Debug.LogWarning($"Cannot reorder element '{element.ElementName}' as it's not part of this layout.", this);
            }
        }

        /// <summary>
        /// Called when layout operations or element modifications occur.
        /// Can be used to trigger UI updates or mark data as dirty.
        /// </summary>
        /// <param name="modifiedElement">The specific element modified, or null if it's a general layout change.</param>
        public void NotifyLayoutModified(CardElement modifiedElement = null)
        {
            if (modifiedElement != null)
            {
                // TODO: Find the corresponding data in m_AppliedLayoutData.Elements and update it?
                // This is tricky - linking runtime UI element back to serialized data requires care.
                Debug.Log($"CardLayout: Element '{modifiedElement.ElementName}' reported modification.", this);
            }
            else
            {
                Debug.Log($"CardLayout: General layout modification notified.", this);
            }

            // TODO: Raise an event (e.g., public event Action OnLayoutModified;)
            // TODO: Mark the associated CardDefinition/CardLayoutData as dirty for saving?
        }

        /// <summary>
        /// Creates a new element from data and adds it to the layout.
        /// Used by UI controls like the Layers Editor.
        /// </summary>
        /// <param name="data">The data defining the element to add.</param>
        /// <returns>The newly created CardElement, or null on failure.</returns>
        public CardElement AddElementFromData(CardElementData data)
        {
            if (data == null)
            {
                Debug.LogError("Cannot add element from null data.", this);
                return null;
            }

            // Determine the correct index (append to the end)
            int newIndex = m_UIElements.Count;

            CardElement newElement = CreateUIElementFromData(data, newIndex);

            if (newElement != null)
            {
                Debug.Log($"Added new element '{newElement.ElementName}' from data.", this);
                NotifyLayoutModified(newElement);
            }
            // CreateUIElementFromData already logs errors if it fails

            return newElement;
        }

        /// <summary>
        /// Updates all UI elements in the layout.
        /// </summary>
        public void UpdateAllElements()
        {
            foreach (var element in m_UIElements)
            {
                if (element != null)
                {
                    element.Update();
                }
            }
        }

        /// <summary>
        /// Called when the card size changes to update all elements.
        /// </summary>
        public void OnCardSizeChanged()
        {
            // Update all elements when the card size changes
            UpdateAllElements();
        }

        // Helper for world space editing
        private CardElementWorldHelper m_WorldSpaceHelper;

        /// <summary>
        /// Gets the world space helper for this card layout.
        /// </summary>
        public CardElementWorldHelper WorldSpaceHelper
        {
            get
            {
                if (m_WorldSpaceHelper == null)
                {
                    m_WorldSpaceHelper = new CardElementWorldHelper(this);
                }
                return m_WorldSpaceHelper;
            }
        }

        /// <summary>
        /// Called when the component is enabled.
        /// </summary>
        private void OnEnable()
        {
            // Initialize render target if needed
            InitializeRenderTarget();
        }

        /// <summary>
        /// Called when the component is disabled.
        /// </summary>
        private void OnDisable()
        {
            // Clean up world space handles
            if (m_WorldSpaceHelper != null)
            {
                m_WorldSpaceHelper.RemoveAllWorldSpaceHandles();
            }
        }

        /// <summary>
        /// Converts TextMeshPro TextAlignmentOptions to Unity's TextAnchor
        /// </summary>
        private TextAnchor ConvertTMPToUnityTextAnchor(TextAlignmentOptions alignment)
        {
            switch (alignment)
            {
                case TextAlignmentOptions.TopLeft:
                    return TextAnchor.UpperLeft;
                case TextAlignmentOptions.Top:
                    return TextAnchor.UpperCenter;
                case TextAlignmentOptions.TopRight:
                    return TextAnchor.UpperRight;
                case TextAlignmentOptions.Left:
                    return TextAnchor.MiddleLeft;
                case TextAlignmentOptions.Center:
                    return TextAnchor.MiddleCenter;
                case TextAlignmentOptions.Right:
                    return TextAnchor.MiddleRight;
                case TextAlignmentOptions.BottomLeft:
                    return TextAnchor.LowerLeft;
                case TextAlignmentOptions.Bottom:
                    return TextAnchor.LowerCenter;
                case TextAlignmentOptions.BottomRight:
                    return TextAnchor.LowerRight;
                default:
                    return TextAnchor.MiddleCenter;
            }
        }

        /// <summary>
        /// Calculates the vector from a given anchor point on an element to that element's geometric center.
        /// Assumes Y-up coordinate system for the element.
        /// </summary>
        /// <param name="anchor">The anchor point on the element.</param>
        /// <param name="elementWidthMM">The width of the element in millimeters.</param>
        /// <param name="elementHeightMM">The height of the element in millimeters.</param>
        /// <returns>A Vector2 representing the offset from the anchor to the center.</returns>
        public static Vector2 GetVectorFromAnchorToCenter(CardAnchorPoint anchor, float elementWidthMM, float elementHeightMM)
        {
            float halfW = elementWidthMM * 0.5f;
            float halfH = elementHeightMM * 0.5f;

            switch (anchor)
            {
                case CardAnchorPoint.TopLeft:       return new Vector2(halfW, -halfH);
                case CardAnchorPoint.TopCenter:     return new Vector2(0f,    -halfH);
                case CardAnchorPoint.TopRight:      return new Vector2(-halfW, -halfH);
                case CardAnchorPoint.MiddleLeft:    return new Vector2(halfW,  0f);
                case CardAnchorPoint.MiddleCenter:  return new Vector2(0f,     0f);
                case CardAnchorPoint.MiddleRight:   return new Vector2(-halfW, 0f);
                case CardAnchorPoint.BottomLeft:    return new Vector2(halfW,  halfH);
                case CardAnchorPoint.BottomCenter:  return new Vector2(0f,     halfH);
                case CardAnchorPoint.BottomRight:   return new Vector2(-halfW, halfH);
                default:                            return Vector2.zero;
            }
        }
    }
}

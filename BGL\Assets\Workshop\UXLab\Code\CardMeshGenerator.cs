using UnityEngine;
using System.Collections.Generic;
using Workshop.UXLab.Data; // Added for CardSizeData
using System;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Workshop.UXLab
{
    /// <summary>
    /// Component that generates a card mesh based on applied CardSizeData.
    /// Expects dimensions in CardSizeData to be in millimeters (mm).
    /// Generates mesh geometry using meters.
    /// </summary>
    [RequireComponent(typeof(MeshFilter))]
    [RequireComponent(typeof(MeshRenderer))]
    [RequireComponent(typeof(MeshCollider))]
    [ExecuteAlways]
    public class CardMeshGenerator : MonoBehaviour
    {
        // Removed internal state fields like _width, _height, _thickness, _cornerRadius, _unitSpace
        // State is now driven by ApplySizeData

        [SerializeField] private int m_CornerSegments = 8; // Keep segments configurable
        [SerializeField] private bool m_ShowGizmos = true;

        // Add a separate field for Inspector editing
        [Space(10)]
        [Tooltip("Editable size data. Press 'Apply Size Data' in the Inspector to see changes.")]
        [SerializeField] private CardSizeData m_EditorSizeData = new CardSizeData(); // Initialize to prevent nulls

        // Public getter for the editor data
        public CardSizeData EditorSizeData => m_EditorSizeData;

        private MeshFilter m_MeshFilter;
        private Mesh m_Mesh;
        private MeshCollider m_MeshCollider;
        [SerializeField]
        private CardSizeData m_CurrentSizeData; // Store the applied data
        [SerializeField]
        private bool m_HasAppliedData = false; // Flag to check if data has been applied

        /// <summary>
        /// Gets the currently applied CardSizeData.
        /// Returns default if no data has been applied yet.
        /// </summary>
        public CardSizeData CurrentSizeData => m_HasAppliedData ? m_CurrentSizeData : null;

        // Removed public properties for Width, Height, Thickness, CornerRadius, CurrentUnitSpace
        // These are now controlled via ApplySizeData

        public int CornerSegments
        {
            get => m_CornerSegments;
            set
            {
                m_CornerSegments = Mathf.Max(1, value);
                if (m_HasAppliedData) // Only regenerate if data was applied
                    GenerateMesh();
            }
        }

        private void Awake() // Changed from OnEnable to Awake for component fetching
        {
            m_MeshFilter = GetComponent<MeshFilter>();
            m_MeshCollider = GetComponent<MeshCollider>();
            EnsureMeshExists();
        }

        private void EnsureMeshExists()
        {
            if (m_MeshFilter == null) m_MeshFilter = GetComponent<MeshFilter>();
            if (m_MeshCollider == null) m_MeshCollider = GetComponent<MeshCollider>();

             // Use sharedMesh to persist in editor, but allow unique instance if needed
            if (m_MeshFilter.sharedMesh == null || m_MeshFilter.sharedMesh.name != "CardMesh_Instance")
            {
                m_Mesh = new Mesh();
                m_Mesh.name = "CardMesh_Instance"; // Use instance name to avoid modifying asset
                m_MeshFilter.sharedMesh = m_Mesh;
            }
            else
            {
                 // If using ExecuteAlways, we might want a unique mesh instance per component
                 // Check if we already have an instance, otherwise create one from shared
                 if (m_Mesh == null || m_Mesh != m_MeshFilter.sharedMesh)
                 {
                     // Instantiate the mesh if it's shared or null
                     m_Mesh = Instantiate(m_MeshFilter.sharedMesh);
                     m_Mesh.name = "CardMesh_Instance";
                     m_MeshFilter.mesh = m_Mesh; // Assign to mesh property, not sharedMesh
                 }
                 // else: m_Mesh is already our unique instance
            }

            if (m_MeshCollider.sharedMesh != m_Mesh) // Ensure collider uses the same mesh instance
            {
                 m_MeshCollider.sharedMesh = m_Mesh;
            }
        }

        // Removed OnValidate as dimension validation is now implicit in CardSizeData
        // and regeneration is triggered by ApplySizeData

        /// <summary>
        /// Applies the physical card dimensions and generates the mesh.
        /// </summary>
        /// <param name="sizeData">The size data (expected in millimeters).</param>
        public void ApplySizeData(CardSizeData sizeData)
        {
            if (sizeData == null)
            {
                Debug.LogWarning("ApplySizeData called with null data. Skipping.", this);
                return;
            }

            // Store the actual applied data
            m_CurrentSizeData = sizeData;
            m_HasAppliedData = true;
            EnsureMeshExists(); // Make sure mesh is ready before generating
            GenerateMesh();

            // Keep the editor data in sync with the applied data
            // Use Clone to avoid reference sharing
            if (m_EditorSizeData == null || !AreSizeDataEqual(m_EditorSizeData, m_CurrentSizeData))
            {
                m_EditorSizeData = CloneSizeData(m_CurrentSizeData);
            }
#if UNITY_EDITOR
            // Mark dirty so inspector updates if changed externally
            UnityEditor.EditorUtility.SetDirty(this);
#endif
        }

        public void GenerateMesh()
        {
            if (!m_HasAppliedData)
            {
                Debug.LogWarning("GenerateMesh called before ApplySizeData. Skipping.", this);
                 if (m_Mesh != null) m_Mesh.Clear(); // Clear mesh if no data
                return; // Don't generate without data
            }
            if (m_Mesh == null)
            {
                 Debug.LogError("Mesh instance is null. Cannot generate mesh.", this);
                 EnsureMeshExists(); // Try to recreate
                 if (m_Mesh == null) return;
            }

            // --- Get dimensions in MM from stored data ---
            float widthMM = m_CurrentSizeData.Width;
            float heightMM = m_CurrentSizeData.Height;
            float thicknessMM = m_CurrentSizeData.Thickness;
            // TODO: Handle non-uniform corners later
            float cornerRadiusMM = m_CurrentSizeData.UseUniformCorners
                                   ? m_CurrentSizeData.UniformCornerRadius
                                   : m_CurrentSizeData.TopLeftCornerRadius; // Placeholder: use TL for now

            // Clamp corner radius based on smallest dimension in MM
            float maxRadiusMM = Mathf.Min(widthMM, heightMM) * 0.5f;
            cornerRadiusMM = Mathf.Clamp(cornerRadiusMM, 0f, maxRadiusMM);
            // -------------------------------------------

            // --- Convert dimensions to METERS for mesh generation ---
            float widthMeters = UnitConverter.MmToMeters(widthMM);
            float heightMeters = UnitConverter.MmToMeters(heightMM);
            float thicknessMeters = UnitConverter.MmToMeters(thicknessMM);
            float cornerRadiusMeters = UnitConverter.MmToMeters(cornerRadiusMM);
            // -------------------------------------------------------

            List<Vector3> vertices = new List<Vector3>();
            List<Vector3> normals = new List<Vector3>();
            List<Vector2> uvs = new List<Vector2>();

            // Create separate triangle lists for each submesh
            List<int> frontFaceTriangles = new List<int>();
            List<int> backFaceTriangles = new List<int>();
            List<int> sideTriangles = new List<int>();

            // --- Calculate half dimensions in METERS ---
            float halfWidthMeters = widthMeters * 0.5f;
            float halfHeightMeters = heightMeters * 0.5f;
            float halfThicknessMeters = thicknessMeters * 0.5f;
            // -------------------------------------------

            // Generate the card mesh (passing METERS)
            GenerateCardMesh(vertices, normals, uvs,
                             frontFaceTriangles, backFaceTriangles, sideTriangles,
                             halfWidthMeters, halfHeightMeters, halfThicknessMeters,
                             cornerRadiusMeters, m_CornerSegments,
                             widthMeters, heightMeters);

            // Assign to mesh
            m_Mesh.Clear(); // Clear previous geometry
            m_Mesh.SetVertices(vertices);
            m_Mesh.SetNormals(normals);
            m_Mesh.SetUVs(0, uvs);

            // Create submeshes for front, back, and sides
            m_Mesh.subMeshCount = 3;
            m_Mesh.SetTriangles(frontFaceTriangles, 0); // Front face (submesh 0)
            m_Mesh.SetTriangles(backFaceTriangles, 1);  // Back face (submesh 1)
            m_Mesh.SetTriangles(sideTriangles, 2);      // Side faces (submesh 2)

            m_Mesh.RecalculateBounds();

            // Update MeshCollider
            if (m_MeshCollider != null)
            {
                m_MeshCollider.sharedMesh = null; // Required before assigning new mesh instance
                m_MeshCollider.sharedMesh = m_Mesh;
            }

            // Ensure MeshRenderer has enough materials
            MeshRenderer renderer = GetComponent<MeshRenderer>();
            if (renderer != null && renderer.sharedMaterials.Length < 3)
            {
                Material[] currentMaterials = renderer.sharedMaterials;
                Material[] newMaterials = new Material[3];

                // Copy existing materials
                for (int i = 0; i < currentMaterials.Length && i < 3; i++)
                {
                    newMaterials[i] = currentMaterials[i];
                }

                // Fill in any missing materials with the first material
                for (int i = currentMaterials.Length; i < 3; i++)
                {
                    if (currentMaterials.Length > 0)
                    {
                        newMaterials[i] = currentMaterials[0];
                    }
                }

                renderer.sharedMaterials = newMaterials;
            }
        }

        // Removed GetUnitMultiplier()

        // Updated GenerateCardMesh to use separate triangle lists for submeshes
        private void GenerateCardMesh(List<Vector3> vertices, List<Vector3> normals, List<Vector2> uvs,
                                     List<int> frontFaceTriangles, List<int> backFaceTriangles, List<int> sideTriangles,
                                     float halfWidth, float halfHeight, float halfThickness, float cornerRadius, int cornerSegments,
                                     float width, float height)
        {
            // Create front face vertices
            int backFaceBaseIndex = vertices.Count;
            float innerWidth = halfWidth - cornerRadius;
            float innerHeight = halfHeight - cornerRadius;

            // Center vertex (for central rectangle)
            vertices.Add(new Vector3(0, 0, halfThickness));
            normals.Add(Vector3.forward);
            uvs.Add(new Vector2(0.5f, 0.5f));

            // Corner centers (where curved corners begin)
            Vector2[] cornerCenters = new Vector2[]
            {
                new Vector2(innerWidth, innerHeight),      // Top Right
                new Vector2(-innerWidth, innerHeight),     // Top Left
                new Vector2(-innerWidth, -innerHeight),    // Bottom Left
                new Vector2(innerWidth, -innerHeight)      // Bottom Right
            };

            // Add corner center vertices
            foreach (var center in cornerCenters)
            {
                vertices.Add(new Vector3(center.x, center.y, halfThickness));
                normals.Add(Vector3.forward);
                uvs.Add(new Vector2((center.x / width) + 0.5f, (center.y / height) + 0.5f));
            }

            // Store corner arc start indices
            int[] cornerArcStartIndices = new int[4];
            float[] startAngles = new float[]
            {
                0f,               // Top Right - 0 degrees
                Mathf.PI * 0.5f,  // Top Left - 90 degrees
                Mathf.PI,         // Bottom Left - 180 degrees
                Mathf.PI * 1.5f   // Bottom Right - 270 degrees
            };

            // Generate corner arcs
            for (int corner = 0; corner < 4; corner++)
            {
                cornerArcStartIndices[corner] = vertices.Count;
                float startAngle = startAngles[corner];
                float angleStep = (Mathf.PI * 0.5f) / cornerSegments;

                for (int i = 0; i <= cornerSegments; i++)
                {
                    float angle = startAngle + i * angleStep;
                    float x = cornerCenters[corner].x + cornerRadius * Mathf.Cos(angle);
                    float y = cornerCenters[corner].y + cornerRadius * Mathf.Sin(angle);

                    vertices.Add(new Vector3(x, y, halfThickness));
                    normals.Add(Vector3.forward);
                    uvs.Add(new Vector2((x / width) + 0.5f, (y / height) + 0.5f));
                }
            }

            // Create triangles for central rectangle (connecting center to corner centers)
            backFaceTriangles.Add(backFaceBaseIndex); // Center
            backFaceTriangles.Add(backFaceBaseIndex + 1); // Top right
            backFaceTriangles.Add(backFaceBaseIndex + 2); // Top left

            backFaceTriangles.Add(backFaceBaseIndex); // Center
            backFaceTriangles.Add(backFaceBaseIndex + 2); // Top left
            backFaceTriangles.Add(backFaceBaseIndex + 3); // Bottom left

            backFaceTriangles.Add(backFaceBaseIndex); // Center
            backFaceTriangles.Add(backFaceBaseIndex + 3); // Bottom left
            backFaceTriangles.Add(backFaceBaseIndex + 4); // Bottom right

            backFaceTriangles.Add(backFaceBaseIndex); // Center
            backFaceTriangles.Add(backFaceBaseIndex + 4); // Bottom right
            backFaceTriangles.Add(backFaceBaseIndex + 1); // Top right

            // Create triangles for corner arcs
            for (int corner = 0; corner < 4; corner++)
            {
                int cornerCenterIndex = backFaceBaseIndex + corner + 1;
                int arcStartIndex = cornerArcStartIndices[corner];

                for (int i = 0; i < cornerSegments; i++)
                {
                    backFaceTriangles.Add(cornerCenterIndex);
                    backFaceTriangles.Add(arcStartIndex + i);
                    backFaceTriangles.Add(arcStartIndex + i + 1);
                }
            }

            // Connect corner arcs to create the outer edges
            for (int corner = 0; corner < 4; corner++)
            {
                int currentCorner = corner;
                int nextCorner = (corner + 1) % 4;

                int currentArcStart = cornerArcStartIndices[currentCorner];
                int currentArcEnd = currentArcStart + cornerSegments;
                int nextArcStart = cornerArcStartIndices[nextCorner];

                // Get indices for corner center vertices (corner center vertices start at frontFaceBaseIndex + 1)
                int currentCornerCenterIdx = backFaceBaseIndex + currentCorner + 1;
                int nextCornerCenterIdx = backFaceBaseIndex + nextCorner + 1;

                // Create triangles for the straight edge connecting adjacent corner arcs
                backFaceTriangles.Add(currentCornerCenterIdx); // Corner center
                backFaceTriangles.Add(currentArcEnd); // Last vertex of current arc
                backFaceTriangles.Add(nextArcStart); // First vertex of next arc

                // Additional triangle to ensure proper connection
                backFaceTriangles.Add(currentCornerCenterIdx); // Corner center
                backFaceTriangles.Add(nextArcStart); // First vertex of next arc
                backFaceTriangles.Add(nextCornerCenterIdx); // Next corner center
            }

            // Store front face data to duplicate for back face
            int frontVertexCount = vertices.Count - backFaceBaseIndex;
            List<Vector3> frontVertices = new List<Vector3>(frontVertexCount);
            List<Vector3> frontNormals = new List<Vector3>(frontVertexCount);
            List<Vector2> frontUVs = new List<Vector2>(frontVertexCount);

            for (int i = 0; i < frontVertexCount; i++)
            {
                frontVertices.Add(vertices[backFaceBaseIndex + i]);
                frontNormals.Add(normals[backFaceBaseIndex + i]);
                frontUVs.Add(uvs[backFaceBaseIndex + i]);
            }

            // Create back face
            int frontFaceBaseIndex = vertices.Count;

            // Add back face vertices (mirror of front face)
            for (int i = 0; i < frontVertexCount; i++)
            {
                Vector3 vertex = frontVertices[i];
                vertices.Add(new Vector3(vertex.x, vertex.y, -halfThickness));
                normals.Add(Vector3.back);
                uvs.Add(frontUVs[i]);
            }

            // Add back face triangles (reverse winding order)
            for (int i = 0; i < backFaceTriangles.Count; i += 3)
            {
                frontFaceTriangles.Add(frontFaceBaseIndex + (backFaceTriangles[i] - backFaceBaseIndex));
                frontFaceTriangles.Add(frontFaceBaseIndex + (backFaceTriangles[i + 2] - backFaceBaseIndex));
                frontFaceTriangles.Add(frontFaceBaseIndex + (backFaceTriangles[i + 1] - backFaceBaseIndex));
            }

            // Create the side faces connecting front and back
            CreateSideFaces(vertices, normals, uvs, sideTriangles, cornerArcStartIndices,
                           cornerSegments, backFaceBaseIndex, frontFaceBaseIndex);
        }

        // Updated CreateSideFaces to use sideTriangles list
        private void CreateSideFaces(List<Vector3> vertices, List<Vector3> normals, List<Vector2> uvs,
                                    List<int> sideTriangles, int[] cornerArcStartIndices, int cornerSegments,
                                    int frontFaceBaseIndex, int backFaceBaseIndex)
        {
            // Create edges around all four sides
            for (int corner = 0; corner < 4; corner++)
            {
                int nextCorner = (corner + 1) % 4;

                // Get indices for the current corner arc
                int frontArcStart = cornerArcStartIndices[corner];
                int frontArcEnd = frontArcStart + cornerSegments;

                // Get indices for the next corner arc
                int nextFrontArcStart = cornerArcStartIndices[nextCorner];

                // Create edge faces for the rounded corner
                for (int i = 0; i < cornerSegments; i++)
                {
                    int frontCurrent = frontArcStart + i;
                    int frontNext = frontCurrent + 1;
                    int backCurrent = backFaceBaseIndex + (frontCurrent - frontFaceBaseIndex);
                    int backNext = backFaceBaseIndex + (frontNext - frontFaceBaseIndex);

                    // Front to back face - first triangle (clockwise when viewed from outside)
                    sideTriangles.Add(backCurrent);
                    sideTriangles.Add(frontNext);
                    sideTriangles.Add(frontCurrent);

                    // Front to back face - second triangle
                    sideTriangles.Add(backCurrent);
                    sideTriangles.Add(backNext);
                    sideTriangles.Add(frontNext);

                    // Add correct normals for these edges
                    Vector3 frontPos = vertices[frontCurrent];
                    Vector3 normal = new Vector3(frontPos.x, frontPos.y, 0).normalized;

                    // We would need to add these normals to the normals list if we were creating new vertices
                    // But since we're reusing existing vertices, Unity will average the normals automatically
                }

                // Create edge face for the straight section between corners
                int frontCurrentEnd = frontArcEnd;
                int frontNextStart = nextFrontArcStart;
                int backCurrentEnd = backFaceBaseIndex + (frontCurrentEnd - frontFaceBaseIndex);
                int backNextStart = backFaceBaseIndex + (frontNextStart - frontFaceBaseIndex);

                // Straight section - first triangle
                sideTriangles.Add(backCurrentEnd);
                sideTriangles.Add(frontNextStart);
                sideTriangles.Add(frontCurrentEnd);

                // Straight section - second triangle
                sideTriangles.Add(backCurrentEnd);
                sideTriangles.Add(backNextStart);
                sideTriangles.Add(frontNextStart);
            }
        }

        #if UNITY_EDITOR
        // Gizmo drawing needs update to use stored data and convert mm to meters
        private void OnDrawGizmosSelected()
        {
            if (!m_ShowGizmos || !m_HasAppliedData)
                return;

            // Get dimensions in MM from stored data
             float widthMM = m_CurrentSizeData.Width;
             float heightMM = m_CurrentSizeData.Height;
             float thicknessMM = m_CurrentSizeData.Thickness;
             // TODO: Handle non-uniform corners later
             float cornerRadiusMM = m_CurrentSizeData.UseUniformCorners
                                    ? m_CurrentSizeData.UniformCornerRadius
                                    : m_CurrentSizeData.TopLeftCornerRadius; // Placeholder

             // Clamp corner radius based on smallest dimension in MM
             float maxRadiusMM = Mathf.Min(widthMM, heightMM) * 0.5f;
             cornerRadiusMM = Mathf.Clamp(cornerRadiusMM, 0f, maxRadiusMM);

            // --- Convert dimensions to METERS for Gizmo drawing ---
            float widthMeters = UnitConverter.MmToMeters(widthMM);
            float heightMeters = UnitConverter.MmToMeters(heightMM);
            float thicknessMeters = UnitConverter.MmToMeters(thicknessMM);
            float cornerRadiusMeters = UnitConverter.MmToMeters(cornerRadiusMM);
            // -------------------------------------------------------

            Gizmos.color = new Color(0.2f, 0.8f, 1f, 0.8f);
            Matrix4x4 originalMatrix = Gizmos.matrix;
            Gizmos.matrix = transform.localToWorldMatrix;

            DrawCardGizmo(widthMeters, heightMeters, thicknessMeters, cornerRadiusMeters, m_CornerSegments);

            Gizmos.matrix = originalMatrix;
        }

        // DrawCardGizmo and helper methods remain the same, expecting METERS
        private void DrawCardGizmo(float width, float height, float thickness, float cornerRadius, int cornerSegments)
        {
            float halfWidth = width * 0.5f;
            float halfHeight = height * 0.5f;
            float halfThickness = thickness * 0.5f;

            // Calculate inner rect (excluding corners)
            float innerWidth = halfWidth - cornerRadius;
            float innerHeight = halfHeight - cornerRadius;

            // Corner centers
            Vector3[] cornerCenters = new Vector3[]
            {
                new Vector3(innerWidth, innerHeight, 0),      // Top Right
                new Vector3(-innerWidth, innerHeight, 0),     // Top Left
                new Vector3(-innerWidth, -innerHeight, 0),    // Bottom Left
                new Vector3(innerWidth, -innerHeight, 0)      // Bottom Right
            };

            // Draw the front outline
            DrawRoundedRectangle(cornerCenters, cornerRadius, cornerSegments, halfThickness);

            // Draw the back outline
            DrawRoundedRectangle(cornerCenters, cornerRadius, cornerSegments, -halfThickness);

            // Draw thickness lines connecting front and back
            Gizmos.color = new Color(0.2f, 0.8f, 1f, 0.4f);
            for (int i = 0; i < 4; i++)
            {
                 // TODO: Use correct corner radius if non-uniform
                Gizmos.DrawLine(
                    new Vector3(cornerCenters[i].x, cornerCenters[i].y, halfThickness),
                    new Vector3(cornerCenters[i].x, cornerCenters[i].y, -halfThickness));
            }
        }

        private void DrawRoundedRectangle(Vector3[] cornerCenters, float cornerRadius, int cornerSegments, float z)
        {
            float[] startAngles = new float[]
            {
                0f,               // Top Right - 0 degrees
                Mathf.PI * 0.5f,  // Top Left - 90 degrees
                Mathf.PI,         // Bottom Left - 180 degrees
                Mathf.PI * 1.5f   // Bottom Right - 270 degrees
            };

            // Draw the corner arcs and connecting lines
            for (int corner = 0; corner < 4; corner++)
            {
                int nextCorner = (corner + 1) % 4;
                float startAngle = startAngles[corner];
                float endAngle = startAngles[nextCorner];

                if (corner == 3) endAngle += Mathf.PI * 2; // Complete the circle

                // Draw corner arc
                Vector3 center = cornerCenters[corner];
                center.z = z;
                // TODO: Use correct corner radius if non-uniform
                DrawArc(center, cornerRadius, startAngle, startAngle + Mathf.PI/2, cornerSegments);

                // Draw connecting line to next corner
                 // TODO: Use correct corner radius if non-uniform for start/end points
                Vector3 arcEnd = center + new Vector3(cornerRadius * Mathf.Cos(startAngle + Mathf.PI/2),
                                                      cornerRadius * Mathf.Sin(startAngle + Mathf.PI/2), 0);
                Vector3 nextCenter = cornerCenters[nextCorner];
                nextCenter.z = z;
                Vector3 nextArcStart = nextCenter + new Vector3(cornerRadius * Mathf.Cos(startAngles[nextCorner]),
                                                               cornerRadius * Mathf.Sin(startAngles[nextCorner]), 0);

                Gizmos.DrawLine(arcEnd, nextArcStart);
            }
        }

        private void DrawArc(Vector3 center, float radius, float startAngle, float endAngle, int segments)
        {
           // ... (Existing Gizmo drawing logic using meter values) ...
             float angleStep = (endAngle - startAngle) / segments;
            Vector3 previousPoint = center + new Vector3(radius * Mathf.Cos(startAngle), radius * Mathf.Sin(startAngle), 0);

            for (int i = 1; i <= segments; i++)
            {
                float angle = startAngle + i * angleStep;
                Vector3 point = center + new Vector3(radius * Mathf.Cos(angle), radius * Mathf.Sin(angle), 0);
                Gizmos.DrawLine(previousPoint, point);
                previousPoint = point;
            }
        }
        #endif

        // --- Helper Methods for Editor Sync ---
        private CardSizeData CloneSizeData(CardSizeData source)
        {
            if (source == null) return new CardSizeData(); // Return default if source is null

            CardSizeData clone = new CardSizeData();
            clone.Width = source.Width;
            clone.Height = source.Height;
            clone.Thickness = source.Thickness;
            clone.UseUniformCorners = source.UseUniformCorners;
            clone.UniformCornerRadius = source.UniformCornerRadius;
            clone.TopLeftCornerRadius = source.TopLeftCornerRadius;
            clone.TopRightCornerRadius = source.TopRightCornerRadius;
            clone.BottomRightCornerRadius = source.BottomRightCornerRadius;
            clone.BottomLeftCornerRadius = source.BottomLeftCornerRadius;
            // Note: Cutouts are not cloned here, as they are complex.
            // Consider a deeper clone if editor needs to modify cutouts directly.
            clone.Cutouts = new List<CardCutoutData>(source.Cutouts ?? new List<CardCutoutData>()); // Shallow copy of list
            return clone;
        }

        private bool AreSizeDataEqual(CardSizeData a, CardSizeData b)
        {
            if (a == null || b == null) return a == b; // True if both null, false if one is null

            // Check basic dimensions with tolerance
            const float tolerance = 0.001f; // Tolerance for float comparison
            if (Mathf.Abs(a.Width - b.Width) > tolerance ||
                Mathf.Abs(a.Height - b.Height) > tolerance ||
                Mathf.Abs(a.Thickness - b.Thickness) > tolerance)
            {
                return false;
            }

            // Check corner properties
            if (a.UseUniformCorners != b.UseUniformCorners) return false;
            if (a.UseUniformCorners)
            {
                 if (Mathf.Abs(a.UniformCornerRadius - b.UniformCornerRadius) > tolerance) return false;
            }
            else
            {
                if (Mathf.Abs(a.TopLeftCornerRadius - b.TopLeftCornerRadius) > tolerance ||
                    Mathf.Abs(a.TopRightCornerRadius - b.TopRightCornerRadius) > tolerance ||
                    Mathf.Abs(a.BottomRightCornerRadius - b.BottomRightCornerRadius) > tolerance ||
                    Mathf.Abs(a.BottomLeftCornerRadius - b.BottomLeftCornerRadius) > tolerance) return false;
            }

            // Note: Cutout comparison is skipped for simplicity
            // if (a.Cutouts?.Count != b.Cutouts?.Count) return false;
            // Need deep comparison if cutouts matter for equality check.

            return true;
        }
        // --- End Helper Methods ---
    }
}

using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;

namespace Workshop.UXLab
{
  /// <summary>
  /// System that manages rendering card elements using UIToolkit to a RenderTexture.
  /// This approach replaces the 3D object-based card elements with UI elements rendered to a texture.
  /// </summary>
  [CreateAssetMenu(fileName = "CardRenderSystem", menuName = "Workshop/UXLab/Systems/CardRenderSystem")]
  public class CardRenderSystem : TableSystem
  {
    [Header("Render Settings")]
    [Tooltip("Resolution in pixels per millimeter. Higher values create larger textures with more detail.")]
    [SerializeField] private int m_DefaultPixelsPerMm = 10; // Default resolution

    [Tooltip("Whether to use anti-aliasing for smoother edges.")]
    [SerializeField] private bool m_UseAntiAliasing = true;

    [Tooltip("Texture filtering mode. Bilinear provides good quality with reasonable performance.")]
    [SerializeField] private FilterMode m_FilterMode = FilterMode.Bilinear;

    [Tooltip("Whether to automatically adjust the render texture when the card size changes.")]
    [SerializeField] private bool m_AutoAdjustRenderTexture = true;

    [Header("Panel Settings")] [SerializeField]
    private PanelSettings m_PanelSettingsTemplate;

    // Internal state
    private Dictionary<CardMeshGenerator, CardRenderTarget> m_RenderTargets =
      new Dictionary<CardMeshGenerator, CardRenderTarget>();

    private GameObject m_HostGameObject;

    /// <summary>
    /// Initialize the system with a reference to the table manager.
    /// Creates a GameObject to host the MonoBehaviour functionality.
    /// </summary>
    public override void Init(TableManager tableManager)
    {
      base.Init(tableManager);

      // Create a GameObject to host the UIDocuments
      m_HostGameObject = new GameObject("CardRenderSystem_Host");

      // If we have a panel settings template, use it, otherwise try to get from TableManager
      if (m_PanelSettingsTemplate == null && m_TableManager != null)
      {
        m_PanelSettingsTemplate = m_TableManager.UIPanelSettings;
        if (m_PanelSettingsTemplate == null)
        {
          Debug.LogWarning(
            "CardRenderSystem: No PanelSettings template provided and TableManager.UIPanelSettings is null.");
        }
      }

      Debug.Log("CardRenderSystem initialized successfully.");
    }

    /// <summary>
    /// Shutdown the system and clean up resources.
    /// </summary>
    public override void Shutdown()
    {
      // Clean up all render targets
      foreach (var target in m_RenderTargets.Values)
      {
        target.Dispose();
      }

      m_RenderTargets.Clear();

      // Destroy the host GameObject
      if (m_HostGameObject != null)
      {
        UnityEngine.Object.Destroy(m_HostGameObject);
        m_HostGameObject = null;
      }

      base.Shutdown();
    }

    /// <summary>
    /// Creates or gets an existing render target for the specified card mesh generator.
    /// </summary>
    /// <param name="cardMesh">The card mesh generator to create a render target for</param>
    /// <returns>The CardRenderTarget instance</returns>
    public CardRenderTarget GetRenderTarget(CardMeshGenerator cardMesh)
    {
      if (cardMesh == null)
      {
        Debug.LogError("Cannot create render target for null CardMeshGenerator");
        return null;
      }

      // Return existing render target if it exists
      if (m_RenderTargets.TryGetValue(cardMesh, out CardRenderTarget existingTarget))
      {
        return existingTarget;
      }

      // Create new render target
      CardRenderTarget newTarget =
        new CardRenderTarget(cardMesh, m_DefaultPixelsPerMm, m_UseAntiAliasing, m_FilterMode);
      m_RenderTargets.Add(cardMesh, newTarget);

      // Create UIDocument for the render target
      CreateUIDocumentForTarget(newTarget);

      return newTarget;
    }

    /// <summary>
    /// Creates a UIDocument for the specified render target.
    /// </summary>
    private void CreateUIDocumentForTarget(CardRenderTarget renderTarget)
    {
      if (renderTarget == null || m_HostGameObject == null) return;

      // Create GameObject to host the UIDocument
      GameObject uiHost = new GameObject($"CardUI_{renderTarget.CardMesh.gameObject.name}");
      uiHost.transform.SetParent(m_HostGameObject.transform);

      // Add UIDocument component
      UIDocument uiDocument = uiHost.AddComponent<UIDocument>();

      // Create a new PanelSettings instance based on the template
      PanelSettings panelSettings = CreatePanelSettings(renderTarget);
      uiDocument.panelSettings = panelSettings;

      // Initialize the CardUIDocument
      renderTarget.InitializeUIDocument(uiDocument, panelSettings);
    }

    /// <summary>
    /// Creates a new PanelSettings instance for the render target.
    /// </summary>
    private PanelSettings CreatePanelSettings(CardRenderTarget renderTarget)
    {
      // Create a new PanelSettings instance
      PanelSettings panelSettings = ScriptableObject.CreateInstance<PanelSettings>();
      panelSettings.name = renderTarget.CardMesh.gameObject.name;

      // Set the target texture
      panelSettings.targetTexture = renderTarget.RenderTexture;

      // Set scale mode to ConstantPixelSize
      panelSettings.scaleMode = PanelScaleMode.ConstantPixelSize;

      // Use a scale of 1.0 since we're directly converting mm to pixels in the UI elements
      panelSettings.scale = renderTarget.PixelsPerMm;

      // Clear the color between renders.
      panelSettings.clearColor = true;

      // Set screen match mode
      /*panelSettings.screenMatchMode = PanelScreenMatchMode.MatchWidthOrHeight;
      panelSettings.match = 0.5f; // Match both width and height equally*/

      // Copy theme settings from template if available
      if (m_PanelSettingsTemplate != null)
      {
        panelSettings.themeStyleSheet = m_PanelSettingsTemplate.themeStyleSheet;
        // Note: themeUssClassName might not be available in all Unity versions
        // panelSettings.themeUssClassName = m_PanelSettingsTemplate.themeUssClassName;

        // Copy other relevant settings from the template
        // but keep our scale settings
      }

      return panelSettings;
    }

    /// <summary>
    /// Removes a render target for the specified card mesh generator.
    /// </summary>
    public void RemoveRenderTarget(CardMeshGenerator cardMesh)
    {
      if (cardMesh == null) return;

      if (m_RenderTargets.TryGetValue(cardMesh, out CardRenderTarget target))
      {
        target.Dispose();
        m_RenderTargets.Remove(cardMesh);
      }
    }

    /// <summary>
    /// Updates the resolution (pixels per mm) for a render target.
    /// </summary>
    /// <param name="cardMesh">The card mesh generator whose render target to update</param>
    /// <param name="pixelsPerMm">The new pixels per mm value</param>
    /// <returns>True if the update was successful, false otherwise</returns>
    public bool UpdateRenderTargetResolution(CardMeshGenerator cardMesh, int pixelsPerMm)
    {
      if (cardMesh == null || pixelsPerMm <= 0) return false;

      // Check if we have a render target for this card mesh
      if (!m_RenderTargets.TryGetValue(cardMesh, out CardRenderTarget target))
      {
        Debug.LogWarning($"No render target found for card mesh {cardMesh.name}");
        return false;
      }

      // Remove the old render target
      target.Dispose();
      m_RenderTargets.Remove(cardMesh);

      // Create a new render target with the updated pixels per mm
      CardRenderTarget newTarget = new CardRenderTarget(cardMesh, pixelsPerMm, m_UseAntiAliasing, m_FilterMode);
      m_RenderTargets.Add(cardMesh, newTarget);

      // Create a new UIDocument for the target
      CreateUIDocumentForTarget(newTarget);

      Debug.Log($"Updated render target resolution for card {cardMesh.name} to {pixelsPerMm} pixels/mm");
      return true;
    }


  }
}

using System;
using UnityEngine;
using UnityEngine.UIElements;
using Workshop.UXLab.Data;
using LengthUnit = UnityEngine.UIElements.LengthUnit;

namespace Workshop.UXLab
{
    /// <summary>
    /// Manages a RenderTexture target for a card, handling resolution and UIDocument setup.
    /// </summary>
    public class CardRenderTarget : IDisposable
    {
        // Properties
        public CardMeshGenerator CardMesh { get; private set; }
        public RenderTexture RenderTexture { get; private set; }
        public UIDocument UIDocument { get; private set; }
        public PanelSettings PanelSettings { get; private set; }
        public int PixelsPerMm { get; private set; }

        // Internal state
        private Material m_CardMaterial;
        private bool m_Disposed = false;

        /// <summary>
        /// Creates a new CardRenderTarget for the specified card mesh generator.
        /// </summary>
        /// <param name="cardMesh">The card mesh generator to create a render target for</param>
        /// <param name="pixelsPerMm">Resolution in pixels per millimeter</param>
        /// <param name="useAntiAliasing">Whether to use anti-aliasing</param>
        /// <param name="filterMode">Texture filter mode</param>
        public CardRenderTarget(CardMeshGenerator cardMesh, int pixelsPerMm, bool useAntiAliasing, FilterMode filterMode)
        {
            CardMesh = cardMesh;
            PixelsPerMm = pixelsPerMm;

            // Create the render texture
            CreateRenderTexture(useAntiAliasing, filterMode);

            // Get the card material
            m_CardMaterial = cardMesh.GetComponent<Renderer>()?.material;
            if (m_CardMaterial != null)
            {
                // Apply the render texture to the card material
                m_CardMaterial.mainTexture = RenderTexture;
            }
            else
            {
                Debug.LogError($"Failed to get material from card mesh {cardMesh.name}");
            }
        }

        /// <summary>
        /// Creates a RenderTexture based on the card dimensions.
        /// </summary>
        private void CreateRenderTexture(bool useAntiAliasing, FilterMode filterMode)
        {
            // Get card dimensions in millimeters
            CardSizeData sizeData = CardMesh.CurrentSizeData;
            float widthMm = sizeData.Width;
            float heightMm = sizeData.Height;

            // Calculate texture dimensions in pixels
            int widthPixels = Mathf.RoundToInt(widthMm * PixelsPerMm);
            int heightPixels = Mathf.RoundToInt(heightMm * PixelsPerMm);

            // Ensure minimum size
            widthPixels = Mathf.Max(widthPixels, 32);
            heightPixels = Mathf.Max(heightPixels, 32);

            // Ensure dimensions are even numbers (helps with some rendering issues)
            //widthPixels = widthPixels + (widthPixels % 2);
            //heightPixels = heightPixels + (heightPixels % 2);

            // Create the render texture
            RenderTexture = new RenderTexture(widthPixels, heightPixels, 0, RenderTextureFormat.ARGB32);
            RenderTexture.name = $"CardRenderTexture_{CardMesh.name}";
            RenderTexture.filterMode = filterMode;
            RenderTexture.antiAliasing = useAntiAliasing ? 4 : 1;
            RenderTexture.Create();

            Debug.Log($"Created RenderTexture for card {CardMesh.name}: {widthPixels}x{heightPixels} pixels (at {PixelsPerMm} pixels/mm)");
        }

        /// <summary>
        /// Initializes the UIDocument for this render target.
        /// </summary>
        public void InitializeUIDocument(UIDocument uiDocument, PanelSettings panelSettings)
        {
            UIDocument = uiDocument;
            PanelSettings = panelSettings;

            // Create root element
            VisualElement root = new VisualElement();
            root.style.width = new StyleLength(new Length(100, LengthUnit.Percent));
            root.style.height = new StyleLength(new Length(100, LengthUnit.Percent));
            root.style.backgroundColor = new StyleColor(new Color(0, 0, 0, 0)); // Transparent background
            root.pickingMode = PickingMode.Ignore;

            // Set as root visual element
            UIDocument.rootVisualElement.Add(root);
        }

        /// <summary>
        /// Updates the render texture dimensions when the card size changes.
        /// </summary>
        public void UpdateRenderTextureDimensions()
        {
            if (RenderTexture == null || CardMesh == null) return;

            // Get card dimensions in millimeters
            CardSizeData sizeData = CardMesh.CurrentSizeData;
            float widthMm = sizeData.Width;
            float heightMm = sizeData.Height;

            // Calculate texture dimensions in pixels
            int widthPixels = Mathf.RoundToInt(widthMm * PixelsPerMm);
            int heightPixels = Mathf.RoundToInt(heightMm * PixelsPerMm);

            // Ensure minimum size
            widthPixels = Mathf.Max(widthPixels, 32);
            heightPixels = Mathf.Max(heightPixels, 32);

            // Ensure dimensions are even numbers (helps with some rendering issues)
            //widthPixels = widthPixels + (widthPixels % 2);
            //heightPixels = heightPixels + (heightPixels % 2);

            // Check if dimensions have changed
            if (RenderTexture.width != widthPixels || RenderTexture.height != heightPixels)
            {
                // Release the old texture
                RenderTexture.Release();

                // Update dimensions
                RenderTexture.width = widthPixels;
                RenderTexture.height = heightPixels;

                // Recreate the texture
                RenderTexture.Create();

                Debug.Log($"Updated RenderTexture dimensions for card {CardMesh.name}: {widthPixels}x{heightPixels} pixels (at {PixelsPerMm} pixels/mm)");
            }
        }

        /// <summary>
        /// Adds a UI element to the card's UI document.
        /// </summary>
        public void AddElement(VisualElement element)
        {
            if (UIDocument == null || element == null) return;

            UIDocument.rootVisualElement.Add(element);
        }

        /// <summary>
        /// Disposes of the render target resources.
        /// </summary>
        public void Dispose()
        {
            if (m_Disposed) return;

            // Release the render texture
            if (RenderTexture != null)
            {
                RenderTexture.Release();
                UnityEngine.Object.Destroy(RenderTexture);
                RenderTexture = null;
            }

            // Destroy the UIDocument GameObject
            if (UIDocument != null)
            {
                UnityEngine.Object.Destroy(UIDocument.gameObject);
                UIDocument = null;
            }

            // Destroy the panel settings
            if (PanelSettings != null)
            {
                UnityEngine.Object.Destroy(PanelSettings);
                PanelSettings = null;
            }

            m_Disposed = true;
        }
    }
}

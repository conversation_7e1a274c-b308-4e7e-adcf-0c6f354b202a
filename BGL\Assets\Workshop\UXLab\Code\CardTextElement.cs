using UnityEngine;
using UnityEngine.UIElements;

namespace Workshop.UXLab
{
    /// <summary>
    /// UI element that displays text on a card.
    /// </summary>
    public class CardTextElement : CardElement
    {
        // Properties
        public string Text { get; set; } = "Default Text";
        public Color TextColor { get; set; } = Color.black;
        public Color FontColor { get { return TextColor; } set { TextColor = value; } } // Alias for compatibility
        public float FontSize { get; set; } = 12f;
        public bool AutoFit { get; set; } = true;
        public TextAnchor Alignment { get; set; } = TextAnchor.MiddleCenter;
        public TextAnchor TextAlignment { get { return Alignment; } set { Alignment = value; } } // Alias for compatibility
        public Font FontAsset { get; set; } = null; // For compatibility
        public bool Bold { get; set; } = false;
        public bool Italic { get; set; } = false;

        // UI Elements
        private Label m_Label;

        /// <summary>
        /// Creates a new CardTextElement.
        /// </summary>
        /// <param name="name">The name of the element</param>
        /// <param name="renderTarget">The render target to add the element to</param>
        /// <param name="text">The text to display</param>
        public CardTextElement(string name, CardRenderTarget renderTarget, string text = "Default Text")
            : base(name, renderTarget)
        {
            Text = text;

            // Create the label element
            m_Label = new Label(Text);
            m_Label.style.width = new StyleLength(new Length(100, LengthUnit.Percent));
            m_Label.style.height = new StyleLength(new Length(100, LengthUnit.Percent));
            m_Label.style.unityTextAlign = GetTextAlignment(Alignment);
            m_Label.pickingMode = PickingMode.Ignore;

            // Add to root element
            m_RootElement.Add(m_Label);

            // Update content
            UpdateContent();
        }

        /// <summary>
        /// Updates the text content.
        /// </summary>
        public override void UpdateContent()
        {
            if (m_Label == null) return;

            // Set the text
            m_Label.text = Text;

            // Set the color
            m_Label.style.color = new StyleColor(TextColor);

            // Set the font size
            m_Label.style.fontSize = FontSize;

            // Set the alignment
            m_Label.style.unityTextAlign = GetTextAlignment(Alignment);

            // Set font style
            m_Label.style.unityFontStyleAndWeight = GetFontStyle();

            // Set auto-fit
            if (AutoFit)
            {
                m_Label.style.whiteSpace = WhiteSpace.Normal;
                m_Label.style.overflow = Overflow.Hidden;
            }
            else
            {
                m_Label.style.whiteSpace = WhiteSpace.NoWrap;
                m_Label.style.overflow = Overflow.Visible;
            }
        }

        /// <summary>
        /// Converts TextAnchor to TextAnchor for UIElements.
        /// </summary>
        private TextAnchor GetTextAlignment(TextAnchor alignment)
        {
            switch (alignment)
            {
                case TextAnchor.UpperLeft:
                    return TextAnchor.UpperLeft;
                case TextAnchor.UpperCenter:
                    return TextAnchor.UpperCenter;
                case TextAnchor.UpperRight:
                    return TextAnchor.UpperRight;
                case TextAnchor.MiddleLeft:
                    return TextAnchor.MiddleLeft;
                case TextAnchor.MiddleCenter:
                    return TextAnchor.MiddleCenter;
                case TextAnchor.MiddleRight:
                    return TextAnchor.MiddleRight;
                case TextAnchor.LowerLeft:
                    return TextAnchor.LowerLeft;
                case TextAnchor.LowerCenter:
                    return TextAnchor.LowerCenter;
                case TextAnchor.LowerRight:
                    return TextAnchor.LowerRight;
                default:
                    return TextAnchor.MiddleCenter;
            }
        }

        /// <summary>
        /// Gets the font style based on Bold and Italic properties.
        /// </summary>
        private FontStyle GetFontStyle()
        {
            if (Bold && Italic)
                return FontStyle.BoldAndItalic;
            else if (Bold)
                return FontStyle.Bold;
            else if (Italic)
                return FontStyle.Italic;
            else
                return FontStyle.Normal;
        }
    }
}

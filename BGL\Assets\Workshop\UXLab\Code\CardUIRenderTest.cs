using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using Workshop.UXLab.Data;

namespace Workshop.UXLab
{
    /// <summary>
    /// Test component to demonstrate the UIToolkit-based card rendering approach.
    /// </summary>
    public class CardUIRenderTest : MonoBehaviour
    {
        [Header("Card References")]
        [SerializeField] private CardMeshGenerator m_CardMesh;
        [SerializeField] private CardRenderSystem m_RenderSystem;

        [Header("Test Assets")]
        [SerializeField] private Texture2D m_TestImage;
        [SerializeField] private Font m_TestFont;

        [Header("Render Settings")]
        [Tooltip("Whether to show the rendered texture in the editor after rendering.")]
        [SerializeField] private bool m_ShowRenderedTexture = true;

        [Tooltip("Path where the rendered texture will be saved.")]
        [SerializeField] private string m_SavePath = "Assets/Workshop/UXLab/Textures/CardRender.png";

        [Tooltip("Resolution in pixels per millimeter. Higher values create larger textures with more detail.")]
        [Range(1, 30)]
        [SerializeField] private int m_PixelsPerMm = 10;

        // Internal state
        private CardRenderTarget m_RenderTarget;
        private List<CardElement> m_Elements = new List<CardElement>();

        private void Start()
        {
            if (m_CardMesh == null)
            {
                Debug.LogError("CardMesh reference is missing");
                return;
            }

            if (m_RenderSystem == null)
            {
                // Try to get the CardRenderSystem from the TableManager
                TableManager tableManager = TableManager.Instance;
                if (tableManager != null)
                {
                    m_RenderSystem = tableManager.GetSystem<CardRenderSystem>();
                    if (m_RenderSystem == null)
                    {
                        Debug.LogError("CardRenderSystem not found in TableManager");
                        return;
                    }
                }
                else
                {
                    Debug.LogError("TableManager instance not found");
                    return;
                }
            }

            // Get or create the render target
            m_RenderTarget = m_RenderSystem.GetRenderTarget(m_CardMesh);
            if (m_RenderTarget == null)
            {
                Debug.LogError("Failed to create render target");
                return;
            }

            // Create test elements
            CreateTestElements();
        }

        /// <summary>
        /// Creates test elements to demonstrate the UI rendering.
        /// </summary>
        private void CreateTestElements()
        {
            // Get card dimensions
            CardSizeData cardSize = m_CardMesh.CurrentSizeData;
            float cardWidthMm = cardSize.Width;
            float cardHeightMm = cardSize.Height;

            // Create a background image
            CardImageElement background = new CardImageElement("Background", m_RenderTarget);
            background.WidthMM = cardWidthMm;
            background.HeightMM = cardHeightMm;
            background.PositionMM = Vector2.zero;
            background.TintColor = new Color(0.9f, 0.9f, 0.9f);
            background.Update();
            m_Elements.Add(background);

            // Create a test image
            if (m_TestImage != null)
            {
                CardImageElement testImage = new CardImageElement("TestImage", m_RenderTarget, m_TestImage);
                testImage.WidthMM = cardWidthMm * 0.8f;
                testImage.HeightMM = cardHeightMm * 0.5f;
                testImage.PositionMM = new Vector2(0, -cardHeightMm * 0.2f);
                testImage.Update();
                m_Elements.Add(testImage);
            }

            // Create a title text
            CardTextElement titleText = new CardTextElement("TitleText", m_RenderTarget, "Card Title");
            titleText.WidthMM = cardWidthMm * 0.9f;
            titleText.HeightMM = cardHeightMm * 0.1f;
            titleText.PositionMM = new Vector2(0, -cardHeightMm * 0.4f);
            titleText.FontSize = 8;
            titleText.Bold = true;
            titleText.TextColor = Color.black;
            titleText.Update();
            m_Elements.Add(titleText);

            // Create a description text
            CardTextElement descText = new CardTextElement("DescriptionText", m_RenderTarget,
                "This is a test card rendered using UIToolkit. The text and images are rendered to a texture and applied to the card mesh.");
            descText.WidthMM = cardWidthMm * 0.9f;
            descText.HeightMM = cardHeightMm * 0.2f;
            descText.PositionMM = new Vector2(0, cardHeightMm * 0.3f);
            descText.FontSize = 6;
            descText.TextColor = Color.black;
            descText.Alignment = TextAnchor.UpperCenter;
            descText.Update();
            m_Elements.Add(descText);
        }

        /// <summary>
        /// Updates the render target when the card size changes.
        /// </summary>
        private void Update()
        {
            if (m_RenderTarget != null)
            {
                // Update the render texture dimensions if the card size has changed
                m_RenderTarget.UpdateRenderTextureDimensions();

                // Update all elements
                foreach (var element in m_Elements)
                {
                    element.Update();
                }
            }
        }

        /// <summary>
        /// Updates the resolution of the render target.
        /// </summary>
        /// <param name="pixelsPerMm">The new pixels per mm value</param>
        public void UpdateResolution(int pixelsPerMm)
        {
            if (m_RenderSystem == null || m_CardMesh == null)
            {
                Debug.LogError("Cannot update resolution: RenderSystem or CardMesh is null");
                return;
            }

            // Update the resolution
            if (m_RenderSystem.UpdateRenderTargetResolution(m_CardMesh, pixelsPerMm))
            {
                // Get the updated render target
                m_RenderTarget = m_RenderSystem.GetRenderTarget(m_CardMesh);

                // Recreate the elements
                RecreateElements();
            }
        }

        /// <summary>
        /// Recreates all elements after a resolution change.
        /// </summary>
        private void RecreateElements()
        {
            // Remove all existing elements
            foreach (var element in m_Elements)
            {
                element.Remove();
            }
            m_Elements.Clear();

            // Create new elements
            CreateTestElements();
        }

        /// <summary>
        /// Renders the current state of the card to a texture and optionally saves it.
        /// </summary>
        public void RenderToTexture()
        {
            if (m_RenderTarget == null || m_RenderTarget.RenderTexture == null)
            {
                Debug.LogError("Cannot render to texture: RenderTarget or RenderTexture is null");
                return;
            }

            // Update the resolution if it has changed
            if (m_RenderTarget.PixelsPerMm != m_PixelsPerMm)
            {
                UpdateResolution(m_PixelsPerMm);
            }

            // Make sure all elements are up to date
            foreach (var element in m_Elements)
            {
                element.Update();
            }

            // Create a temporary RenderTexture to read from
            RenderTexture sourceTexture = m_RenderTarget.RenderTexture;
            RenderTexture tempRT = RenderTexture.GetTemporary(
                sourceTexture.width,
                sourceTexture.height,
                0,
                RenderTextureFormat.ARGB32
            );

            // Copy the render texture to the temporary one
            Graphics.Blit(sourceTexture, tempRT);

            // Create a new Texture2D and read the pixels
            Texture2D texture = new Texture2D(tempRT.width, tempRT.height, TextureFormat.RGBA32, false);
            RenderTexture.active = tempRT;
            texture.ReadPixels(new Rect(0, 0, tempRT.width, tempRT.height), 0, 0);
            texture.Apply();
            RenderTexture.active = null;
            RenderTexture.ReleaseTemporary(tempRT);

            // Save the texture if a path is provided
            if (!string.IsNullOrEmpty(m_SavePath))
            {
                SaveTexture(texture, m_SavePath);
            }

            // Show the texture if requested
            if (m_ShowRenderedTexture)
            {
                ShowTexture(texture);
            }
            else
            {
                Destroy(texture);
            }
        }

        /// <summary>
        /// Saves the texture to the specified path.
        /// </summary>
        private void SaveTexture(Texture2D texture, string path)
        {
#if UNITY_EDITOR
            try
            {
                // Ensure the directory exists
                string directory = System.IO.Path.GetDirectoryName(path);
                if (!System.IO.Directory.Exists(directory))
                {
                    System.IO.Directory.CreateDirectory(directory);
                }

                // Convert the texture to PNG
                byte[] bytes = texture.EncodeToPNG();

                // Write the bytes to the file
                System.IO.File.WriteAllBytes(path, bytes);

                // Refresh the asset database
                UnityEditor.AssetDatabase.Refresh();

                Debug.Log($"Texture saved to {path}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to save texture: {e.Message}");
            }
#else
            Debug.LogWarning("Saving textures is only supported in the Unity Editor");
#endif
        }

        /// <summary>
        /// Shows the texture in a preview window.
        /// </summary>
        private void ShowTexture(Texture2D texture)
        {
#if UNITY_EDITOR
            // Create a preview window to show the texture
            UnityEditor.EditorUtility.DisplayDialog("Rendered Texture", "The texture has been rendered successfully.", "OK");
            UnityEditor.EditorGUIUtility.PingObject(texture);
#endif
        }

        /// <summary>
        /// Cleans up resources when the component is destroyed.
        /// </summary>
        private void OnDestroy()
        {
            // Remove all elements
            foreach (var element in m_Elements)
            {
                element.Remove();
            }
            m_Elements.Clear();

            // Remove the render target
            if (m_RenderSystem != null && m_CardMesh != null)
            {
                m_RenderSystem.RemoveRenderTarget(m_CardMesh);
            }
        }
    }
}

using UnityEngine;
using UnityEngine.UIElements;
using Unity.AppUI.Core;
using System;
using Unity.AppUI.UI;

namespace Workshop.UXLab
{
    /// <summary>
    /// A draggable panel that stays within screen boundaries.
    /// </summary>
    [UxmlElement]
    public partial class DraggablePanel : ExVisualElement
    {
        private Vector2 m_DragStartPosition;
        private bool m_IsDragging = false;
        private VisualElement m_DragHandle;
        private VisualElement m_Shadow;
        private VisualElement m_ContentContainer;
        
        // Property name constants
        private const string kDraggablePanelClass = "draggable-panel";
        private const string kDragHandleClass = "drag-handle";
        private const string kShadowName = "panel-shadow";
        private const string kDraggablePanelContainerName = "draggable-panel__content-container";

        /// <summary>
        /// The CSS class name to use for the drag handle element.
        /// By default, this is set to the panel container.
        /// </summary>
        [UxmlAttribute]
        public string DragHandleClassName { get; set; } = kDraggablePanelContainerName;

        /// <summary>
        /// The minimum distance to keep from screen edges in pixels.
        /// </summary>
        [UxmlAttribute]
        public float EdgePadding { get; set; } = 10f;

        /// <summary>
        /// Gets the content container element where all children will be added.
        /// </summary>
        public override VisualElement contentContainer => m_ContentContainer;

        /// <summary>
        /// Event triggered when the panel begins to be dragged.
        /// </summary>
        public event EventHandler<EventArgs> DragStarted;

        /// <summary>
        /// Event triggered when the panel finishes being dragged.
        /// </summary>
        public event EventHandler<EventArgs> DragEnded;

        // Constructor
        public DraggablePanel()
        {
            // Add class for styling
            AddToClassList(kDraggablePanelClass);

            // Load and apply the UXML template
            VisualTreeAsset template = Resources.Load<VisualTreeAsset>("UI/DraggablePanelLayout");
            if (template != null)
            {
                template.CloneTree(this);
                
                // Get references to our elements
                m_Shadow = this.Q(kShadowName);
                m_ContentContainer = this.Q(kDraggablePanelContainerName);
                m_DragHandle = m_ContentContainer;
                
                // Apply the StyleSheet
                StyleSheet styleSheet = Resources.Load<StyleSheet>("UI/DraggablePanelStylesheet");
                if (styleSheet != null)
                {
                    styleSheets.Add(styleSheet);
                }
                else
                {
                    Debug.LogError("DraggablePanel: Could not load stylesheet from Resources/UI/DraggablePanelStylesheet");
                }
            }
            else
            {
                Debug.LogError("DraggablePanel: Could not load layout from Resources/UI/DraggablePanelLayout");
            }
            
            // Only set the absolute positioning - rest is handled by USS
            style.position = Position.Absolute;
            
            // Register for after-panel-attach callback to find drag handle
            //RegisterCallback<AttachToPanelEvent>(OnFirstAttach);
            
            // Register pointer events directly on this element
            m_DragHandle.RegisterCallback<PointerDownEvent>(OnPointerDown);
            m_DragHandle.RegisterCallback<PointerMoveEvent>(OnPointerMove);
            m_DragHandle.RegisterCallback<PointerUpEvent>(OnPointerUp);
            m_DragHandle.RegisterCallback<PointerCaptureOutEvent>(OnPointerCaptureOut);
        }
        
        // One-time callback to find the drag handle after the panel is attached
        private void OnFirstAttach(AttachToPanelEvent evt)
        {
            // Look for drag handle using the specified class name or by name if it's one of our known elements
            m_DragHandle = this.Q(className: DragHandleClassName) ?? this.Q(DragHandleClassName);
            
            // If no drag handle found, use the container if available, otherwise the panel itself
            if (m_DragHandle == null)
            {
                var container = this.Q(kDraggablePanelContainerName);
                if (container != null)
                {
                    m_DragHandle = container;
                    Debug.Log($"DraggablePanel: Using container element as drag handle");
                }
                else
                {
                    Debug.LogWarning($"DraggablePanel: No element with class or name '{DragHandleClassName}' found. Using the entire panel as drag handle.");
                    m_DragHandle = this;
                }
            }
            else
            {
                Debug.Log($"DraggablePanel: Found drag handle with class/name '{DragHandleClassName}'");
            }
            
            // Make sure the drag handle has proper picking mode
            m_DragHandle.pickingMode = PickingMode.Position;
            
            // Add cursor style to drag handle
            m_DragHandle.AddToClassList(kDragHandleClass);
            
            // Register pointer callbacks on the drag handle instead of the panel
            m_DragHandle.RegisterCallback<PointerDownEvent>(OnPointerDown);
            m_DragHandle.RegisterCallback<PointerMoveEvent>(OnPointerMove);
            m_DragHandle.RegisterCallback<PointerUpEvent>(OnPointerUp);
            m_DragHandle.RegisterCallback<PointerCaptureOutEvent>(OnPointerCaptureOut);
            
            // We only need this callback once
            UnregisterCallback<AttachToPanelEvent>(OnFirstAttach);
        }

        private void OnPointerDown(PointerDownEvent evt)
        {
            if (evt.button == 0) // Left pointer button
            {
                Debug.Log($"DraggablePanel: Pointer down detected: {evt.button}");
                m_IsDragging = true;
                
                // Convert Vector3 to Vector2 to avoid ambiguous operator error
                Vector2 pointerPosition = new Vector2(evt.position.x, evt.position.y);
                Vector2 elementPosition = new Vector2(this.resolvedStyle.left, this.resolvedStyle.top);
                m_DragStartPosition = pointerPosition - elementPosition;
                
                m_DragHandle.CapturePointer(evt.pointerId);
                evt.StopPropagation();
                
                // Trigger drag started event
                DragStarted?.Invoke(this, EventArgs.Empty);
            }
        }

        private void OnPointerMove(PointerMoveEvent evt)
        {
            if (m_IsDragging && m_DragHandle.HasPointerCapture(evt.pointerId))
            {
                // Convert Vector3 to Vector2 to avoid ambiguous operator error
                Vector2 pointerPosition = new Vector2(evt.position.x, evt.position.y);
                Vector2 newPosition = pointerPosition - m_DragStartPosition;
                
                // Get window bounds (use screen size as fallback)
                float rootWidth = (panel?.visualTree?.resolvedStyle.width ?? Screen.width);
                float rootHeight = (panel?.visualTree?.resolvedStyle.height ?? Screen.height);
                
                // Constrain to window bounds
                float minX = EdgePadding;
                float minY = EdgePadding;
                float maxX = rootWidth - this.resolvedStyle.width - EdgePadding;
                float maxY = rootHeight - this.resolvedStyle.height - EdgePadding;
                
                // Ensure we don't move off-screen
                newPosition.x = Mathf.Clamp(newPosition.x, minX, maxX);
                newPosition.y = Mathf.Clamp(newPosition.y, minY, maxY);
                
                // Apply new position
                this.style.left = newPosition.x;
                this.style.top = newPosition.y;
                
                evt.StopPropagation();
            }
        }

        private void OnPointerUp(PointerUpEvent evt)
        {
            if (m_DragHandle.HasPointerCapture(evt.pointerId))
            {
                Debug.Log("DraggablePanel: Pointer up - ending drag");
                m_IsDragging = false;
                m_DragHandle.ReleasePointer(evt.pointerId);
                evt.StopPropagation();
                
                // Trigger drag ended event
                DragEnded?.Invoke(this, EventArgs.Empty);
            }
        }
        
        private void OnPointerCaptureOut(PointerCaptureOutEvent evt)
        {
            // This handles cases when pointer capture is lost unexpectedly
            Debug.Log("DraggablePanel: Pointer capture lost");
            m_IsDragging = false;
            
            // Trigger drag ended event
            DragEnded?.Invoke(this, EventArgs.Empty);
        }
    }
} 
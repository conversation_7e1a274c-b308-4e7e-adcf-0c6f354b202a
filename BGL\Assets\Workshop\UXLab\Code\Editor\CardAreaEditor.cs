using UnityEngine;
using UnityEditor;

namespace Workshop.UXLab.Editor
{
    [CustomEditor(typeof(CardArea))]
    public class CardAreaEditor : UnityEditor.Editor
    {
        private SerializedProperty m_UnitSpace;
        private SerializedProperty m_InnerSize;
        private SerializedProperty m_ThresholdDistance;
        private SerializedProperty m_InnerColor;
        private SerializedProperty m_OuterColor;
        private SerializedProperty m_BorderColor;
        private SerializedProperty m_BorderWidth;
        private SerializedProperty m_ShowVisualization;
        private SerializedProperty m_CenterCardsOnDrop;

        // Cached values for display
        private Vector2 m_InnerSizeWorld;
        private float m_ThresholdDistanceWorld;
        private CardArea m_CardArea;

        private void OnEnable()
        {
            m_UnitSpace = serializedObject.FindProperty("m_UnitSpace");
            m_InnerSize = serializedObject.FindProperty("m_InnerSize");
            m_ThresholdDistance = serializedObject.FindProperty("m_ThresholdDistance");
            m_InnerColor = serializedObject.FindProperty("m_InnerColor");
            m_OuterColor = serializedObject.FindProperty("m_OuterColor");
            m_BorderColor = serializedObject.FindProperty("m_BorderColor");
            m_BorderWidth = serializedObject.FindProperty("m_BorderWidth");
            m_ShowVisualization = serializedObject.FindProperty("m_ShowVisualization");
            m_CenterCardsOnDrop = serializedObject.FindProperty("m_CenterCardsOnDrop");

            m_CardArea = (CardArea)target;
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            EditorGUILayout.LabelField("Area Settings", EditorStyles.boldLabel);

            // Unit space dropdown
            EditorGUI.BeginChangeCheck();
            EditorGUILayout.PropertyField(m_UnitSpace, new GUIContent("Unit Space", "The unit space to use for size measurements"));
            bool unitSpaceChanged = EditorGUI.EndChangeCheck();

            // Get the current unit space
            UnitSpace unitSpace = (UnitSpace)m_UnitSpace.enumValueIndex;
            string unitLabel = UnitConverter.kUnitLabelsShort[unitSpace];

            // Inner size with unit label
            EditorGUILayout.PropertyField(m_InnerSize, new GUIContent($"Inner Size {unitLabel}", $"The size of the inner boundary (white area) in {unitSpace}"));

            // Threshold distance with unit label
            EditorGUILayout.PropertyField(m_ThresholdDistance, new GUIContent($"Threshold Distance {unitLabel}", $"The threshold distance beyond the inner boundary (yellow area width) in {unitSpace}"));

            // Show the converted world units (meters)
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Converted World Units (Meters)", EditorStyles.boldLabel);

            // Calculate the world units
            Vector2 innerSizeMm = UnitConverter.ToMillimeters(m_InnerSize.vector2Value, unitSpace);
            float thresholdDistanceMm = UnitConverter.ToMillimeters(m_ThresholdDistance.floatValue, unitSpace);

            m_InnerSizeWorld = new Vector2(
                UnitConverter.MmToMeters(innerSizeMm.x),
                UnitConverter.MmToMeters(innerSizeMm.y)
            );
            m_ThresholdDistanceWorld = UnitConverter.MmToMeters(thresholdDistanceMm);

            // Display the converted values (read-only)
            GUI.enabled = false;
            EditorGUILayout.Vector2Field("Inner Size (m)", m_InnerSizeWorld);
            EditorGUILayout.FloatField("Threshold Distance (m)", m_ThresholdDistanceWorld);
            GUI.enabled = true;

            // Visualization settings
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Visualization", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(m_ShowVisualization, new GUIContent("Show Visualization", "Whether to show the area visualization"));

            if (m_ShowVisualization.boolValue)
            {
                EditorGUILayout.PropertyField(m_InnerColor, new GUIContent("Inner Color", "Color for the inner boundary visualization"));
                EditorGUILayout.PropertyField(m_OuterColor, new GUIContent("Outer Color", "Color for the outer boundary visualization"));
                EditorGUILayout.PropertyField(m_BorderColor, new GUIContent("Border Color", "Color for the border"));
                EditorGUILayout.PropertyField(m_BorderWidth, new GUIContent("Border Width", "Width of the border"));
            }

            // Card behavior settings
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Card Behavior", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(m_CenterCardsOnDrop, new GUIContent("Center Cards On Drop", "Whether to center cards in the area when dropped"));

            serializedObject.ApplyModifiedProperties();

            // Force a repaint if the unit space changed
            if (unitSpaceChanged)
            {
                SceneView.RepaintAll();
            }
        }

        [MenuItem("GameObject/UX Lab/Card Area", false, 10)]
        static void CreateCardArea(MenuCommand menuCommand)
        {
            // Create a new GameObject
            GameObject go = new GameObject("CardArea");

            // Add the CardArea component
            go.AddComponent<CardArea>();

            // Ensure it gets reparented if this was a context click (otherwise does nothing)
            GameObjectUtility.SetParentAndAlign(go, menuCommand.context as GameObject);

            // Register the creation in the undo system
            Undo.RegisterCreatedObjectUndo(go, "Create " + go.name);

            // Select the new GameObject
            Selection.activeObject = go;
        }
    }
}

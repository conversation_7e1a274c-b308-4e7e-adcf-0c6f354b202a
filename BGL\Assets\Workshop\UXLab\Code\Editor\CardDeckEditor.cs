using UnityEngine;
using UnityEditor;

namespace Workshop.UXLab.Editor
{
    [CustomEditor(typeof(CardDeck))]
    public class CardDeckEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            // Draw the default inspector fields
            DrawDefaultInspector();

            CardDeck deck = (CardDeck)target;

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Deck Controls", EditorStyles.boldLabel);

            // Display current card count
            EditorGUILayout.LabelField("Current Card Count", deck.CardCount.ToString());

            // Add Card Button
            if (GUILayout.Button("Add New Card"))
            {
                if (Application.isPlaying || deck.gameObject.scene.IsValid()) // Prevent adding to prefab asset directly
                {
                    CardLayout newCard = deck.AddNewCard();
                    if (newCard != null)
                    {
                        Undo.RegisterCreatedObjectUndo(newCard.gameObject, "Add New Card to Deck");
                        EditorUtility.SetDirty(deck); // Mark deck as dirty
                    }
                }
                else
                {
                    Debug.LogWarning("Cannot add cards to a prefab asset directly. Add cards to an instance in the scene.");
                }
            }

            // Draw Card Button
            GUI.enabled = deck.CardCount > 0;
            if (GUILayout.Button("Draw Card"))
            {
                if (Application.isPlaying || deck.gameObject.scene.IsValid())
                {
                    CardLayout drawnCard = deck.DrawCard();
                    if (drawnCard != null)
                    {
                         // If not playing, register Undo for the transform change (unparenting)
                         if (!Application.isPlaying)
                         {
                             Undo.SetTransformParent(drawnCard.transform, null, "Draw Card from Deck");
                         }
                         EditorUtility.SetDirty(deck);
                         // Optional: Select the drawn card in hierarchy
                         // Selection.activeGameObject = drawnCard.gameObject;
                    }
                }
                else
                {
                     Debug.LogWarning("Cannot draw cards from a prefab asset directly.");
                }
            }
            GUI.enabled = true;

            // Shuffle Button
            GUI.enabled = deck.CardCount > 1;
            if (GUILayout.Button("Shuffle Deck"))
            {
                if (Application.isPlaying || deck.gameObject.scene.IsValid())
                {
                    Undo.RecordObject(deck, "Shuffle Deck"); // Record state before shuffling list
                     // Record transforms of all cards before shuffling positions
                    foreach (Transform child in deck.transform)
                    {
                        if(child.TryGetComponent<CardLayout>(out _))
                            Undo.RecordObject(child, "Shuffle Deck Transform");
                    }
                    deck.Shuffle();
                    EditorUtility.SetDirty(deck);
                }
                else
                {
                     Debug.LogWarning("Cannot shuffle cards in a prefab asset directly.");
                }
            }
            GUI.enabled = true;

            // Flip Deck Button
            GUI.enabled = deck.CardCount > 1;
            if (GUILayout.Button("Flip Deck (Reverse Order)"))
            {
                if (Application.isPlaying || deck.gameObject.scene.IsValid())
                {
                    Undo.RecordObject(deck, "Flip Deck");
                    // Record transforms of all cards before flipping positions
                    foreach (Transform child in deck.transform)
                    {
                        if(child.TryGetComponent<CardLayout>(out _))
                            Undo.RecordObject(child, "Flip Deck Transform");
                    }
                    deck.FlipDeck();
                    EditorUtility.SetDirty(deck);
                }
                else
                {
                     Debug.LogWarning("Cannot flip cards in a prefab asset directly.");
                }
            }
            GUI.enabled = true;

             // Update Layout Button
            if (GUILayout.Button("Update Layout Manually"))
            {
                if (Application.isPlaying || deck.gameObject.scene.IsValid())
                {
                     // Record transforms of all cards before updating layout
                    foreach (Transform child in deck.transform)
                    {
                         if(child.TryGetComponent<CardLayout>(out _))
                            Undo.RecordObject(child, "Update Deck Layout");
                    }
                    deck.UpdateLayout();
                    EditorUtility.SetDirty(deck);
                }
                else
                {
                     Debug.LogWarning("Cannot update layout for a prefab asset directly.");
                }
            }

            // Clear Deck Button
            GUI.enabled = deck.CardCount > 0;
            if (GUILayout.Button("Clear Deck (Destroy Cards)"))
            {
                if (Application.isPlaying || deck.gameObject.scene.IsValid())
                {
                    if (EditorUtility.DisplayDialog("Confirm Clear Deck",
                        "Are you sure you want to remove and destroy all card GameObjects in this deck?",
                        "Yes, Clear Deck", "Cancel"))
                    {
                        // Record the deck state before clearing
                        Undo.RecordObject(deck, "Clear Deck");
                        // Record destruction of child cards
                        foreach (Transform child in deck.transform)
                        {
                            if(child.TryGetComponent<CardLayout>(out _))
                                Undo.DestroyObjectImmediate(child.gameObject);
                        }
                        deck.ClearDeck(); // ClearDeck handles destroying objects
                        EditorUtility.SetDirty(deck);
                    }
                }
                else
                {
                     Debug.LogWarning("Cannot clear cards from a prefab asset directly.");
                }
            }
            GUI.enabled = true;

            // Apply changes and handle undo
            if (GUI.changed)
            {
                EditorUtility.SetDirty(deck);
            }
        }
    }
}

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using Workshop.UXLab.Data;

namespace Workshop.UXLab.Editor
{
    [CustomEditor(typeof(CardMeshGenerator))]
    public class CardMeshGeneratorEditor : UnityEditor.Editor
    {
        private SerializedProperty m_CornerSegmentsProperty;
        private SerializedProperty m_ShowGizmosProperty;
        private SerializedProperty m_EditorSizeDataProperty;
        
        private void OnEnable()
        {
            m_CornerSegmentsProperty = serializedObject.FindProperty("m_CornerSegments");
            m_ShowGizmosProperty = serializedObject.FindProperty("m_ShowGizmos");
            m_EditorSizeDataProperty = serializedObject.FindProperty("m_EditorSizeData");
        }
        
        private void OnDisable()
        {
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();
            CardMeshGenerator generator = (CardMeshGenerator)target;
            
            EditorGUILayout.Space();
            
            EditorGUILayout.PropertyField(m_CornerSegmentsProperty, new GUIContent("Corner Segments"));
            
            EditorGUILayout.Space();
            
            EditorGUILayout.PropertyField(m_ShowGizmosProperty, new GUIContent("Show Render Bounds Gizmo"));
            
            EditorGUILayout.Space();
            
            EditorGUILayout.LabelField("Current Applied Size (Read-Only)", EditorStyles.boldLabel);
            if (generator.CurrentSizeData != null && generator.CurrentSizeData.Width > 0)
            {
                CardSizeData currentData = generator.CurrentSizeData;
                EditorGUILayout.LabelField("  Width:", $"{currentData.Width:F2} mm");
                EditorGUILayout.LabelField("  Height:", $"{currentData.Height:F2} mm");
                EditorGUILayout.LabelField("  Thickness:", $"{currentData.Thickness:F3} mm");
                EditorGUILayout.LabelField("  Corner Radius:", $"{currentData.UniformCornerRadius:F2} mm (Uniform)");
            }
            else
            {
                EditorGUILayout.LabelField("  No size data applied yet.");
            }

            EditorGUILayout.Space();
            
            EditorGUILayout.LabelField("Editable Size Data", EditorStyles.boldLabel);
            
            if (m_EditorSizeDataProperty != null)
            {
                EditorGUILayout.PropertyField(m_EditorSizeDataProperty, GUIContent.none, true);
                }
                else
            {
                EditorGUILayout.HelpBox("Could not find m_EditorSizeData property.", MessageType.Error);
            }

            EditorGUILayout.Space();
            
            if (GUILayout.Button("Apply Size Data"))
            {
                serializedObject.ApplyModifiedProperties();
                
                if (m_EditorSizeDataProperty != null)
                {
                    object editorDataObj = m_EditorSizeDataProperty.boxedValue;
                    
                    if (editorDataObj is CardSizeData editorData)
                    {
                        generator.ApplySizeData(CloneSizeData(editorData));
                    }
                    else
                    {
                        Debug.LogError("m_EditorSizeData property value is not of type CardSizeData.");
                    }
                }
            }
            
            serializedObject.ApplyModifiedProperties();
        }
        
        private CardSizeData CloneSizeData(CardSizeData source)
        {
            if (source == null) return new CardSizeData();
            
            CardSizeData clone = new CardSizeData();
            clone.Width = source.Width;
            clone.Height = source.Height;
            clone.Thickness = source.Thickness;
            clone.UseUniformCorners = source.UseUniformCorners;
            clone.UniformCornerRadius = source.UniformCornerRadius;
            clone.TopLeftCornerRadius = source.TopLeftCornerRadius;
            clone.TopRightCornerRadius = source.TopRightCornerRadius;
            clone.BottomRightCornerRadius = source.BottomRightCornerRadius;
            clone.BottomLeftCornerRadius = source.BottomLeftCornerRadius;
            clone.Cutouts = new List<CardCutoutData>(source.Cutouts ?? new List<CardCutoutData>());
            return clone;
        }
    }
} 
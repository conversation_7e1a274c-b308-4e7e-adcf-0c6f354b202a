using UnityEngine;

namespace Workshop.UXLab
{
    // This enum defines the different types of handles
    public enum HandleType
    {
        // Corners
        TopLeft,
        TopRight,
        BottomLeft,
        BottomRight,
        // Edges (Optional)
        Top,
        Bottom,
        Left,
        Right,
        // Center (Optional - for moving)
        Rotate, // Added for rotation
        Move
    }

    /// <summary>
    /// Simple component to identify a GameObject as an interaction handle
    /// and specify its type.
    /// </summary>
    public class HandleIdentifier : MonoBehaviour
    {
        public HandleType Type;
        // Optional: Reference back to the element this handle controls?
        // public CardElement TargetElement;
    }
}

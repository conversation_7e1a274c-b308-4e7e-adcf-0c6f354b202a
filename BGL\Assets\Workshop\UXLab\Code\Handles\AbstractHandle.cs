using UnityEngine;

namespace Workshop.UXLab.Handles
{
    /// <summary>
    /// Base class for handle implementations.
    /// </summary>
    public abstract class AbstractHandle : IHandle
    {
        // IHandle implementation
        public CardElement TargetElement { get; protected set; }
        public GameObject HandleGameObject { get; protected set; }
        public bool IsActive { get; protected set; }
        public HandleType HandleType { get; protected set; }
        
        // Common fields
        protected Transform m_Container;
        protected float m_HandleSize;
        protected Vector3 m_DragOffset;
        protected Vector3 m_ElementStartPosition;
        protected float m_ElementStartRotation;
        protected float m_ElementStartWidth;
        protected float m_ElementStartHeight;
        
        /// <summary>
        /// Initialize the handle with the target element and container.
        /// </summary>
        /// <param name="element">The element to edit</param>
        /// <param name="container">The container for all handles</param>
        /// <param name="handlePrefab">The prefab to use for the handle</param>
        /// <param name="handleSize">The size of the handle</param>
        public virtual void Setup(CardElement element, Transform container, GameObject handlePrefab, float handleSize)
        {
            TargetElement = element;
            m_Container = container;
            m_HandleSize = handleSize;
            
            // Create the handle GameObject
            HandleGameObject = Object.Instantiate(handlePrefab, container);
            HandleGameObject.name = $"Handle_{HandleType}";
            
            // Set the handle size
            HandleGameObject.transform.localScale = Vector3.one * handleSize;
            
            // Add identifier component
            var identifier = HandleGameObject.AddComponent<HandleIdentifier>();
            identifier.Type = HandleType;
            
            // Position the handle
            UpdateHandlePosition();
            
            IsActive = true;
        }
        
        /// <summary>
        /// Clean up the handle when it's no longer needed.
        /// </summary>
        public virtual void Cleanup()
        {
            if (HandleGameObject != null)
            {
                Object.Destroy(HandleGameObject);
                HandleGameObject = null;
            }
            
            IsActive = false;
        }
        
        /// <summary>
        /// Update the handle's position and state.
        /// </summary>
        public virtual void UpdateHandle()
        {
            if (!IsActive || HandleGameObject == null || TargetElement == null)
                return;
                
            UpdateHandlePosition();
        }
        
        /// <summary>
        /// Update the handle's position based on the element's properties.
        /// </summary>
        protected abstract void UpdateHandlePosition();
        
        /// <summary>
        /// Called when the handle is clicked.
        /// </summary>
        /// <param name="ray">The ray from the camera to the mouse position</param>
        /// <returns>True if the handle was clicked, false otherwise</returns>
        public virtual bool OnHandleClicked(Ray ray)
        {
            if (!IsActive || HandleGameObject == null || TargetElement == null)
                return false;
                
            // Store the element's current state
            m_ElementStartPosition = new Vector3(
                TargetElement.PositionMM.x,
                TargetElement.PositionMM.y,
                0
            );
            m_ElementStartRotation = TargetElement.RotationDegrees;
            m_ElementStartWidth = TargetElement.WidthMM;
            m_ElementStartHeight = TargetElement.HeightMM;
            
            // Calculate drag offset
            if (ray.direction != Vector3.zero)
            {
                Plane plane = new Plane(Vector3.forward, HandleGameObject.transform.position);
                if (plane.Raycast(ray, out float enter))
                {
                    Vector3 hitPoint = ray.GetPoint(enter);
                    m_DragOffset = HandleGameObject.transform.position - hitPoint;
                }
            }
            
            return true;
        }
        
        /// <summary>
        /// Called when the handle is being dragged.
        /// </summary>
        /// <param name="ray">The ray from the camera to the mouse position</param>
        /// <param name="dragPlane">The plane to raycast against</param>
        /// <returns>True if the handle was dragged, false otherwise</returns>
        public abstract bool OnHandleDragged(Ray ray, Plane dragPlane);
        
        /// <summary>
        /// Called when the handle is released.
        /// </summary>
        public virtual void OnHandleReleased()
        {
            // Notify layout of changes
            CardLayout layout = CardElementHelper.GetCardLayout(TargetElement);
            if (layout != null)
            {
                layout.NotifyLayoutModified(null);
            }
        }
        
        /// <summary>
        /// Get the world position of the opposite corner for resize operations.
        /// </summary>
        /// <returns>The world position of the opposite corner</returns>
        protected Vector3 GetOppositeCornerWorld()
        {
            if (TargetElement == null || m_Container == null)
                return Vector3.zero;
                
            // Get element size in meters
            float widthMeters = UnitConverter.MmToMeters(TargetElement.WidthMM);
            float heightMeters = UnitConverter.MmToMeters(TargetElement.HeightMM);
            float halfWidth = widthMeters * 0.5f;
            float halfHeight = heightMeters * 0.5f;
            
            // Calculate the opposite corner position in local space
            Vector3 oppositeLocalPos = Vector3.zero;
            switch (HandleType)
            {
                case HandleType.TopLeft: oppositeLocalPos = new Vector3(halfWidth, -halfHeight, 0); break;
                case HandleType.TopRight: oppositeLocalPos = new Vector3(-halfWidth, -halfHeight, 0); break;
                case HandleType.BottomLeft: oppositeLocalPos = new Vector3(halfWidth, halfHeight, 0); break;
                case HandleType.BottomRight: oppositeLocalPos = new Vector3(-halfWidth, halfHeight, 0); break;
                default: oppositeLocalPos = Vector3.zero; break;
            }
            
            // Convert to world space
            return m_Container.TransformPoint(oppositeLocalPos);
        }
        
        /// <summary>
        /// Ensure the element is rendered with the UI Toolkit system.
        /// </summary>
        protected void EnsureElementRendered()
        {
            if (TargetElement == null) return;
            
            // Get the parent card layout
            CardLayout cardLayout = CardElementHelper.GetCardLayout(TargetElement);
            if (cardLayout == null) return;
            
            // Get the card mesh generator
            CardMeshGenerator cardMesh = cardLayout.GetComponent<CardMeshGenerator>();
            if (cardMesh == null) return;
            
            // Get the CardRenderSystem
            CardRenderSystem renderSystem = Object.FindObjectOfType<TableManager>()?.GetSystem<CardRenderSystem>();
            if (renderSystem == null) return;
            
            // Get or create a render target for the card
            CardRenderTarget renderTarget = renderSystem.GetRenderTarget(cardMesh);
            if (renderTarget == null) return;
            
            // Update the material texture
            Renderer renderer = cardMesh.GetComponent<Renderer>();
            if (renderer != null && renderer.material != null && renderTarget.RenderTexture != null)
            {
                renderer.material.mainTexture = renderTarget.RenderTexture;
            }
        }
    }
}

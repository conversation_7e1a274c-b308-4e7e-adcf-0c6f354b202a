using System.Collections.Generic;
using UnityEngine;

namespace Workshop.UXLab.Handles
{
    /// <summary>
    /// System for managing handles for editing card elements.
    /// </summary>
    [CreateAssetMenu(fileName = "HandleSystem", menuName = "Workshop/UXLab/Systems/HandleSystem")]
    public class HandleSystem : TableSystem
    {
        [Header("Handle Settings")]
        [SerializeField] private GameObject m_HandlePrefab; // Prefab for handles (e.g., a small cube)
        [SerializeField] private float m_HandleSize = 0.01f; // Scale factor for the handle prefab

        // Active handles
        private List<IHandle> m_ActiveHandles = new List<IHandle>();
        private GameObject m_HandlesContainer;
        private CardElement m_CurrentTargetElement;

        // Handle factories
        private Dictionary<HandleType, System.Type> m_HandleTypeMap = new Dictionary<HandleType, System.Type>();

        // Drag state
        private IHandle m_DraggedHandle;
        private Plane m_DragPlane;
        private Vector3 m_DragOffset;

        /// <summary>
        /// Initialize the handle system.
        /// </summary>
        public override void Init(TableManager tableManager)
        {
            base.Init(tableManager);

            // Ensure we have a handle prefab
            EnsureHandlePrefab();

            // Register default handle types
            RegisterHandleType<SizeHandle>(HandleType.TopLeft);
            RegisterHandleType<SizeHandle>(HandleType.TopRight);
            RegisterHandleType<SizeHandle>(HandleType.BottomLeft);
            RegisterHandleType<SizeHandle>(HandleType.BottomRight);
            RegisterHandleType<RotationHandle>(HandleType.Rotate);
            RegisterHandleType<MoveHandle>(HandleType.Move);

            Debug.Log($"HandleSystem initialized with {m_HandleTypeMap.Count} handle types.");
        }

        /// <summary>
        /// Ensures that a handle prefab is available, creating a default one if necessary.
        /// </summary>
        private void EnsureHandlePrefab()
        {
            if (m_HandlePrefab == null)
            {
                // Create a simple cube as the default handle prefab
                GameObject prefab = GameObject.CreatePrimitive(PrimitiveType.Cube);
                prefab.name = "DefaultHandlePrefab";

                // Set up the prefab
                prefab.transform.localScale = Vector3.one * 0.01f;

                // Add a material with a distinct color
                Renderer renderer = prefab.GetComponent<Renderer>();
                if (renderer != null)
                {
                    Material material = new Material(Shader.Find("Standard"));
                    material.color = Color.cyan;
                    renderer.material = material;
                }

                // Hide the prefab in the hierarchy
                prefab.hideFlags = HideFlags.HideAndDontSave;

                // Assign the prefab
                m_HandlePrefab = prefab;

                Debug.Log("Created default handle prefab.");
            }
        }

        /// <summary>
        /// Register a handle type for a specific handle type enum value.
        /// </summary>
        /// <typeparam name="T">The handle implementation type</typeparam>
        /// <param name="handleType">The handle type enum value</param>
        public void RegisterHandleType<T>(HandleType handleType) where T : class, IHandle, new()
        {
            m_HandleTypeMap[handleType] = typeof(T);
        }

        /// <summary>
        /// Show handles for the specified element.
        /// </summary>
        /// <param name="element">The element to show handles for</param>
        /// <param name="parentTransform">The parent transform for the handles container</param>
        /// <param name="handleTypes">The types of handles to show (null for all registered types)</param>
        public void ShowHandles(CardElement element, Transform parentTransform, HandleType[] handleTypes = null)
        {
            // Clean up any existing handles
            ClearHandles();

            if (element == null || m_HandlePrefab == null)
            {
                Debug.LogWarning("Cannot show handles: Element or Handle Prefab is null.");
                return;
            }

            m_CurrentTargetElement = element;

            // Create a container for all handles
            m_HandlesContainer = new GameObject("ElementHandles");
            m_HandlesContainer.transform.SetParent(parentTransform, false);

            // Position the container at the element's position
            Vector3 elementPositionMeters = new Vector3(
                UnitConverter.MmToMeters(element.PositionMM.x),
                UnitConverter.MmToMeters(element.PositionMM.y),
                -0.0001f // Small negative Z offset
            );
            m_HandlesContainer.transform.localPosition = elementPositionMeters;
            m_HandlesContainer.transform.localRotation = Quaternion.Euler(0, 0, element.RotationDegrees);
            m_HandlesContainer.transform.localScale = Vector3.one;

            // Create handles
            if (handleTypes == null)
            {
                // Create all registered handle types
                foreach (var kvp in m_HandleTypeMap)
                {
                    CreateHandle(kvp.Key, element);
                }
            }
            else
            {
                // Create only the specified handle types
                foreach (var handleType in handleTypes)
                {
                    CreateHandle(handleType, element);
                }
            }

            Debug.Log($"Showing {m_ActiveHandles.Count} handles for {element.ElementName}");
        }

        /// <summary>
        /// Get the default handle types to show for an element.
        /// </summary>
        /// <returns>An array of handle types</returns>
        public HandleType[] GetDefaultHandleTypes()
        {
            return new HandleType[]
            {
                HandleType.TopLeft,
                HandleType.TopRight,
                HandleType.BottomLeft,
                HandleType.BottomRight,
                HandleType.Rotate,
                HandleType.Move
            };
        }

        /// <summary>
        /// Get the current target element.
        /// </summary>
        /// <returns>The current target element, or null if no element is being edited</returns>
        public CardElement GetCurrentTargetElement()
        {
            return m_CurrentTargetElement;
        }

        /// <summary>
        /// Ensure the element is rendered with the UI Toolkit system.
        /// </summary>
        /// <param name="element">The element to ensure is rendered</param>
        public void EnsureElementRendered(CardElement element)
        {
            if (element == null) return;

            // Get the parent card layout
            CardLayout cardLayout = CardElementHelper.GetCardLayout(element);
            if (cardLayout == null) return;

            // Get the card mesh generator
            CardMeshGenerator cardMesh = cardLayout.GetComponent<CardMeshGenerator>();
            if (cardMesh == null) return;

            // Get the CardRenderSystem
            CardRenderSystem renderSystem = m_TableManager.GetSystem<CardRenderSystem>();
            if (renderSystem == null) return;

            // Get or create a render target for the card
            CardRenderTarget renderTarget = renderSystem.GetRenderTarget(cardMesh);
            if (renderTarget == null) return;

            // Update the material texture
            Renderer renderer = cardMesh.GetComponent<Renderer>();
            if (renderer != null && renderer.material != null && renderTarget.RenderTexture != null)
            {
                renderer.material.mainTexture = renderTarget.RenderTexture;
            }
        }

        /// <summary>
        /// Create a handle of the specified type for the element.
        /// </summary>
        /// <param name="handleType">The type of handle to create</param>
        /// <param name="element">The element to create the handle for</param>
        /// <returns>The created handle, or null if creation failed</returns>
        private IHandle CreateHandle(HandleType handleType, CardElement element)
        {
            if (!m_HandleTypeMap.TryGetValue(handleType, out System.Type handleImplType))
            {
                Debug.LogWarning($"No handle implementation registered for handle type {handleType}");
                return null;
            }

            // Create the handle instance
            IHandle handle = System.Activator.CreateInstance(handleImplType) as IHandle;
            if (handle == null)
            {
                Debug.LogError($"Failed to create handle of type {handleImplType}");
                return null;
            }

            // Set up the handle
            handle.Setup(element, m_HandlesContainer.transform, m_HandlePrefab, m_HandleSize);

            // Add to active handles
            m_ActiveHandles.Add(handle);

            return handle;
        }

        /// <summary>
        /// Clear all active handles.
        /// </summary>
        public void ClearHandles()
        {
            // Clean up each handle
            foreach (var handle in m_ActiveHandles)
            {
                handle.Cleanup();
            }

            // Clear the list
            m_ActiveHandles.Clear();

            // Destroy the container
            if (m_HandlesContainer != null)
            {
                Object.Destroy(m_HandlesContainer);
                m_HandlesContainer = null;
            }

            m_CurrentTargetElement = null;
            m_DraggedHandle = null;
        }

        /// <summary>
        /// Update all active handles.
        /// </summary>
        public void UpdateHandles()
        {
            if (m_CurrentTargetElement == null || m_HandlesContainer == null)
                return;

            // Update container position and rotation
            Vector3 elementPositionMeters = new Vector3(
                UnitConverter.MmToMeters(m_CurrentTargetElement.PositionMM.x),
                UnitConverter.MmToMeters(m_CurrentTargetElement.PositionMM.y),
                -0.0001f // Small negative Z offset
            );
            m_HandlesContainer.transform.localPosition = elementPositionMeters;
            m_HandlesContainer.transform.localRotation = Quaternion.Euler(0, 0, m_CurrentTargetElement.RotationDegrees);

            // Update each handle
            foreach (var handle in m_ActiveHandles)
            {
                handle.UpdateHandle();
            }
        }

        /// <summary>
        /// Handle a click on a handle.
        /// </summary>
        /// <param name="handleGameObject">The GameObject that was clicked</param>
        /// <param name="ray">The ray from the camera to the mouse position</param>
        /// <param name="cardTransform">The transform of the card</param>
        /// <returns>True if a handle was clicked, false otherwise</returns>
        public bool HandleClicked(GameObject handleGameObject, Ray ray, Transform cardTransform)
        {
            // Find the handle that was clicked
            foreach (var handle in m_ActiveHandles)
            {
                if (handle.HandleGameObject == handleGameObject)
                {
                    m_DraggedHandle = handle;

                    // Create a drag plane
                    if (cardTransform != null)
                    {
                        // Define plane using 3 world corners of the card
                        Vector3 p0 = cardTransform.TransformPoint(new Vector3(-0.5f, -0.5f, 0)); // Bottom-Left
                        Vector3 p1 = cardTransform.TransformPoint(new Vector3(0.5f, -0.5f, 0));  // Bottom-Right
                        Vector3 p2 = cardTransform.TransformPoint(new Vector3(-0.5f, 0.5f, 0));   // Top-Left
                        m_DragPlane = new Plane(p0, p2, p1);
                    }

                    // Notify the handle
                    return handle.OnHandleClicked(ray);
                }
            }

            return false;
        }

        /// <summary>
        /// Handle dragging of the active handle.
        /// </summary>
        /// <param name="ray">The ray from the camera to the mouse position</param>
        /// <returns>True if a handle was dragged, false otherwise</returns>
        public bool HandleDragged(Ray ray)
        {
            if (m_DraggedHandle != null)
            {
                return m_DraggedHandle.OnHandleDragged(ray, m_DragPlane);
            }

            return false;
        }

        /// <summary>
        /// Handle release of the active handle.
        /// </summary>
        public void HandleReleased()
        {
            if (m_DraggedHandle != null)
            {
                m_DraggedHandle.OnHandleReleased();
                m_DraggedHandle = null;
            }
        }

        /// <summary>
        /// Get the currently dragged handle, if any.
        /// </summary>
        /// <returns>The dragged handle, or null if no handle is being dragged</returns>
        public IHandle GetDraggedHandle()
        {
            return m_DraggedHandle;
        }

        /// <summary>
        /// Check if a handle is currently being dragged.
        /// </summary>
        /// <returns>True if a handle is being dragged, false otherwise</returns>
        public bool IsHandleDragging()
        {
            return m_DraggedHandle != null;
        }

        /// <summary>
        /// Shutdown the system and clean up any resources.
        /// </summary>
        public override void Shutdown()
        {
            if (!m_IsInitialized)
                return;

            // Clean up any active handles
            ClearHandles();

            // Clean up the default handle prefab if we created one
            if (m_HandlePrefab != null && m_HandlePrefab.hideFlags == HideFlags.HideAndDontSave)
            {
                GameObject.DestroyImmediate(m_HandlePrefab);
                m_HandlePrefab = null;
            }

            base.Shutdown();
        }
    }
}

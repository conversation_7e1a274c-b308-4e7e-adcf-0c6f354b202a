using UnityEngine;

namespace Workshop.UXLab.Handles
{
    /// <summary>
    /// Interface for all handle implementations that can be used to edit card elements.
    /// </summary>
    public interface IHandle
    {
        /// <summary>
        /// The element this handle is editing.
        /// </summary>
        CardElement TargetElement { get; }

        /// <summary>
        /// The GameObject representing this handle in the scene.
        /// </summary>
        GameObject HandleGameObject { get; }

        /// <summary>
        /// Whether this handle is currently active.
        /// </summary>
        bool IsActive { get; }

        /// <summary>
        /// The type of this handle.
        /// </summary>
        HandleType HandleType { get; }

        /// <summary>
        /// Initialize the handle with the target element and container.
        /// </summary>
        /// <param name="element">The element to edit</param>
        /// <param name="container">The container for all handles</param>
        /// <param name="handlePrefab">The prefab to use for the handle</param>
        /// <param name="handleSize">The size of the handle</param>
        void Setup(CardElement element, Transform container, GameObject handlePrefab, float handleSize);

        /// <summary>
        /// Clean up the handle when it's no longer needed.
        /// </summary>
        void Cleanup();

        /// <summary>
        /// Update the handle's position and state.
        /// </summary>
        void UpdateHandle();

        /// <summary>
        /// Called when the handle is clicked.
        /// </summary>
        /// <param name="ray">The ray from the camera to the mouse position</param>
        /// <returns>True if the handle was clicked, false otherwise</returns>
        bool OnHandleClicked(Ray ray);

        /// <summary>
        /// Called when the handle is being dragged.
        /// </summary>
        /// <param name="ray">The ray from the camera to the mouse position</param>
        /// <param name="dragPlane">The plane to raycast against</param>
        /// <returns>True if the handle was dragged, false otherwise</returns>
        bool OnHandleDragged(Ray ray, Plane dragPlane);

        /// <summary>
        /// Called when the handle is released.
        /// </summary>
        void OnHandleReleased();
    }
}

using UnityEngine;

namespace Workshop.UXLab.Handles
{
    /// <summary>
    /// Handle implementation for moving elements.
    /// </summary>
    public class MoveHandle : AbstractHandle
    {
        /// <summary>
        /// Initialize the handle with the target element and container.
        /// </summary>
        /// <param name="element">The element to edit</param>
        /// <param name="container">The container for all handles</param>
        /// <param name="handlePrefab">The prefab to use for the handle</param>
        /// <param name="handleSize">The size of the handle</param>
        public override void Setup(CardElement element, Transform container, GameObject handlePrefab, float handleSize)
        {
            // Set the handle type
            HandleType = HandleType.Move;
            
            base.Setup(element, container, handlePrefab, handleSize);
            
            // Make the move handle visually distinct (e.g., different color or shape)
            // This would depend on the handle prefab's capabilities
            // For now, we'll just scale it differently
            HandleGameObject.transform.localScale = Vector3.one * handleSize * 1.2f;
        }
        
        /// <summary>
        /// Update the handle's position based on the element's properties.
        /// </summary>
        protected override void UpdateHandlePosition()
        {
            if (HandleGameObject == null || TargetElement == null)
                return;
                
            // Position the move handle at the center of the element
            Vector3 localPos = Vector3.zero;
            
            // Update the handle position
            HandleGameObject.transform.localPosition = localPos;
            HandleGameObject.transform.localRotation = Quaternion.identity;
        }
        
        /// <summary>
        /// Called when the handle is being dragged.
        /// </summary>
        /// <param name="ray">The ray from the camera to the mouse position</param>
        /// <param name="dragPlane">The plane to raycast against</param>
        /// <returns>True if the handle was dragged, false otherwise</returns>
        public override bool OnHandleDragged(Ray ray, Plane dragPlane)
        {
            if (!IsActive || HandleGameObject == null || TargetElement == null)
                return false;
                
            // Raycast against the drag plane
            if (dragPlane.Raycast(ray, out float enter))
            {
                Vector3 hitPointWorld = ray.GetPoint(enter);
                Vector3 targetHandleWorldPos = hitPointWorld + m_DragOffset;
                
                // Get the card layout transform
                CardLayout cardLayout = CardElementHelper.GetCardLayout(TargetElement);
                if (cardLayout == null)
                    return false;
                    
                Transform cardLayoutTransform = cardLayout.transform;
                
                // Convert target handle position to card's local space
                Vector3 targetHandleLocalPos = cardLayoutTransform.InverseTransformPoint(targetHandleWorldPos);
                
                // Keep the Z offset consistent
                targetHandleLocalPos.z = -0.0001f;
                
                // Convert from meters to millimeters for CardElement properties
                Vector2 newPositionMM = new Vector2(
                    UnitConverter.MetersToMm(targetHandleLocalPos.x),
                    UnitConverter.MetersToMm(targetHandleLocalPos.y)
                );
                
                // Set the position on the CardElement
                TargetElement.PositionMM = newPositionMM;
                
                // Update the UI element to refresh the visual representation
                TargetElement.Update();
                
                // Ensure the element is rendered
                EnsureElementRendered();
                
                // Update the container's position
                m_Container.localPosition = targetHandleLocalPos;
                
                return true;
            }
            
            return false;
        }
    }
}

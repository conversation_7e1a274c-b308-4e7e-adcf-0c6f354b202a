using UnityEngine;

namespace Workshop.UXLab.Handles
{
    /// <summary>
    /// Handle implementation for rotating elements.
    /// </summary>
    public class RotationHandle : AbstractHandle
    {
        private Vector3 m_ElementCenterWorld;
        
        /// <summary>
        /// Initialize the handle with the target element and container.
        /// </summary>
        /// <param name="element">The element to edit</param>
        /// <param name="container">The container for all handles</param>
        /// <param name="handlePrefab">The prefab to use for the handle</param>
        /// <param name="handleSize">The size of the handle</param>
        public override void Setup(CardElement element, Transform container, GameObject handlePrefab, float handleSize)
        {
            // Set the handle type
            HandleType = HandleType.Rotate;
            
            base.Setup(element, container, handlePrefab, handleSize);
        }
        
        /// <summary>
        /// Update the handle's position based on the element's properties.
        /// </summary>
        protected override void UpdateHandlePosition()
        {
            if (HandleGameObject == null || TargetElement == null)
                return;
                
            // Get element size in meters
            float heightMeters = UnitConverter.MmToMeters(TargetElement.HeightMM);
            float halfHeight = heightMeters * 0.5f;
            
            // Position the rotation handle above the element
            Vector3 localPos = new Vector3(0, halfHeight + m_HandleSize * 2, 0);
            
            // Update the handle position
            HandleGameObject.transform.localPosition = localPos;
            HandleGameObject.transform.localRotation = Quaternion.identity;
        }
        
        /// <summary>
        /// Called when the handle is clicked.
        /// </summary>
        /// <param name="ray">The ray from the camera to the mouse position</param>
        /// <returns>True if the handle was clicked, false otherwise</returns>
        public override bool OnHandleClicked(Ray ray)
        {
            if (!base.OnHandleClicked(ray))
                return false;
                
            // Store the element's center in world space
            m_ElementCenterWorld = m_Container.position;
            
            return true;
        }
        
        /// <summary>
        /// Called when the handle is being dragged.
        /// </summary>
        /// <param name="ray">The ray from the camera to the mouse position</param>
        /// <param name="dragPlane">The plane to raycast against</param>
        /// <returns>True if the handle was dragged, false otherwise</returns>
        public override bool OnHandleDragged(Ray ray, Plane dragPlane)
        {
            if (!IsActive || HandleGameObject == null || TargetElement == null)
                return false;
                
            // Raycast against the drag plane
            if (dragPlane.Raycast(ray, out float enter))
            {
                Vector3 hitPointWorld = ray.GetPoint(enter);
                Vector3 targetHandleWorldPos = hitPointWorld + m_DragOffset;
                
                // Calculate the vector from the center to the target handle position
                Vector3 centerToHandle = targetHandleWorldPos - m_ElementCenterWorld;
                
                // Calculate the angle in degrees (in the XY plane)
                float angle = Mathf.Atan2(centerToHandle.y, centerToHandle.x) * Mathf.Rad2Deg;
                
                // Adjust angle to match Unity's rotation system (0 degrees is up, clockwise rotation)
                angle = 90f - angle;
                
                // Normalize the angle to 0-360 range
                while (angle < 0) angle += 360f;
                while (angle >= 360f) angle -= 360f;
                
                // Set the rotation on the CardElement
                TargetElement.RotationDegrees = angle;
                
                // Update the UI element to refresh the visual representation
                TargetElement.Update();
                
                // Ensure the element is rendered
                EnsureElementRendered();
                
                // Update the container's rotation
                m_Container.localRotation = Quaternion.Euler(0, 0, angle);
                
                return true;
            }
            
            return false;
        }
    }
}

using UnityEngine;

namespace Workshop.UXLab.Handles
{
    /// <summary>
    /// Handle implementation for resizing elements.
    /// </summary>
    public class SizeHandle : AbstractHandle
    {
        private Vector3 m_OppositeCornerWorld;
        
        /// <summary>
        /// Initialize the handle with the target element and container.
        /// </summary>
        /// <param name="element">The element to edit</param>
        /// <param name="container">The container for all handles</param>
        /// <param name="handlePrefab">The prefab to use for the handle</param>
        /// <param name="handleSize">The size of the handle</param>
        public override void Setup(CardElement element, Transform container, GameObject handlePrefab, float handleSize)
        {
            // Set the handle type based on the position
            HandleType = HandleType.TopLeft; // Default
            
            base.Setup(element, container, handlePrefab, handleSize);
        }
        
        /// <summary>
        /// Update the handle's position based on the element's properties.
        /// </summary>
        protected override void UpdateHandlePosition()
        {
            if (HandleGameObject == null || TargetElement == null)
                return;
                
            // Get element size in meters
            float widthMeters = UnitConverter.MmToMeters(TargetElement.WidthMM);
            float heightMeters = UnitConverter.MmToMeters(TargetElement.HeightMM);
            float halfWidth = widthMeters * 0.5f;
            float halfHeight = heightMeters * 0.5f;
            
            // Set the handle position based on its type
            Vector3 localPos = Vector3.zero;
            switch (HandleType)
            {
                case HandleType.TopLeft:     localPos = new Vector3(-halfWidth, halfHeight, 0); break;
                case HandleType.TopRight:    localPos = new Vector3(halfWidth, halfHeight, 0); break;
                case HandleType.BottomLeft:  localPos = new Vector3(-halfWidth, -halfHeight, 0); break;
                case HandleType.BottomRight: localPos = new Vector3(halfWidth, -halfHeight, 0); break;
                default: localPos = Vector3.zero; break;
            }
            
            // Update the handle position
            HandleGameObject.transform.localPosition = localPos;
            HandleGameObject.transform.localRotation = Quaternion.identity;
        }
        
        /// <summary>
        /// Called when the handle is clicked.
        /// </summary>
        /// <param name="ray">The ray from the camera to the mouse position</param>
        /// <returns>True if the handle was clicked, false otherwise</returns>
        public override bool OnHandleClicked(Ray ray)
        {
            if (!base.OnHandleClicked(ray))
                return false;
                
            // Store the opposite corner position
            m_OppositeCornerWorld = GetOppositeCornerWorld();
            
            return true;
        }
        
        /// <summary>
        /// Called when the handle is being dragged.
        /// </summary>
        /// <param name="ray">The ray from the camera to the mouse position</param>
        /// <param name="dragPlane">The plane to raycast against</param>
        /// <returns>True if the handle was dragged, false otherwise</returns>
        public override bool OnHandleDragged(Ray ray, Plane dragPlane)
        {
            if (!IsActive || HandleGameObject == null || TargetElement == null)
                return false;
                
            // Raycast against the drag plane
            if (dragPlane.Raycast(ray, out float enter))
            {
                Vector3 hitPointWorld = ray.GetPoint(enter);
                Vector3 targetHandleWorldPos = hitPointWorld + m_DragOffset;
                
                // Get the card layout transform
                CardLayout cardLayout = CardElementHelper.GetCardLayout(TargetElement);
                if (cardLayout == null)
                    return false;
                    
                Transform cardLayoutTransform = cardLayout.transform;
                
                // Convert target handle position and opposite corner to card's local space
                Vector3 targetHandleLocalPos = cardLayoutTransform.InverseTransformPoint(targetHandleWorldPos);
                Vector3 oppositeCornerLocalPos = cardLayoutTransform.InverseTransformPoint(m_OppositeCornerWorld);
                
                // Calculate the new center of the element in the card's local space
                Vector3 newElementCenterLocal = (targetHandleLocalPos + oppositeCornerLocalPos) * 0.5f;
                newElementCenterLocal.z = -0.0001f; // Consistent small Z offset
                
                // Calculate the new size of the element in the card's local space
                Vector3 sizeVectorLocal = targetHandleLocalPos - oppositeCornerLocalPos;
                float newWidthMeters = Mathf.Max(Mathf.Abs(sizeVectorLocal.x), 0.001f); // Min width 1mm
                float newHeightMeters = Mathf.Max(Mathf.Abs(sizeVectorLocal.y), 0.001f); // Min height 1mm
                
                // Convert dimensions from meters to millimeters for CardElement properties
                float newWidthMM = UnitConverter.MetersToMm(newWidthMeters);
                float newHeightMM = UnitConverter.MetersToMm(newHeightMeters);
                
                // The newPositionMM for CardElement is its center relative to the card's center, in millimeters
                Vector2 newPositionMM = new Vector2(
                    UnitConverter.MetersToMm(newElementCenterLocal.x),
                    UnitConverter.MetersToMm(newElementCenterLocal.y)
                );
                
                // Set properties on the CardElement
                TargetElement.WidthMM = newWidthMM;
                TargetElement.HeightMM = newHeightMM;
                TargetElement.PositionMM = newPositionMM;
                
                // Update the UI element to refresh the visual representation
                TargetElement.Update();
                
                // Ensure the element is rendered
                EnsureElementRendered();
                
                // Update the handle position
                UpdateHandlePosition();
                
                return true;
            }
            
            return false;
        }
    }
}

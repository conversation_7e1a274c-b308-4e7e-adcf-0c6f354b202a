using UnityEngine;
using UnityEngine.UIElements;
using Unity.AppUI.UI; // For AppUI specific controls
using Unity.AppUI.Core; // If needed for AppUI specifics
using System;
using System.Collections.Generic; // For List and Dictionary
using System.Linq;
using Workshop.UXLab.Data; // For Card data
using System.Globalization; // For CultureInfo
using TMPro; // For TextAlignmentOptions
using System.IO; // For file operations

// Resolve ambiguity if needed
using Button = Unity.AppUI.UI.Button;
using Toggle = Unity.AppUI.UI.Toggle;
using TextField = Unity.AppUI.UI.TextField;
using Vector2Field = Unity.AppUI.UI.Vector2Field;
using FloatField = Unity.AppUI.UI.FloatField;
using Dropdown = Unity.AppUI.UI.Dropdown;
using ListView = UnityEngine.UIElements.ListView;
// Use explicit alias for UIElements TextElement to avoid conflict with AppUI Text
using TextElement = UnityEngine.UIElements.TextElement;
using AppUIToggle = Unity.AppUI.UI.Toggle; // Alias AppUI Toggle specifically if needed
using TextArea = Unity.AppUI.UI.TextArea;
using MenuItem = Unity.AppUI.UI.MenuItem; // Alias for menu handling
using Accordion = Unity.AppUI.UI.Accordion; // Add using for Accordion
using AccordionItem = Unity.AppUI.UI.AccordionItem; // Add using for AccordionItem

namespace Workshop.UXLab
{
    public class IntegratedCardEditorControl : VisualElement
    {
        // --- Constants ---
        private const string UxmlPath = "UI/IntegratedCardEditorLayout";
        private const string UssPath = "UI/IntegratedCardEditorStyles";
        private const string VisibleClassName = "visible";

        // --- UI Elements ---
        private DraggablePanel m_Container;

        // Layout Section
        private Accordion m_LayoutAccordion;
        private AccordionItem m_SizeAccordionItem;
        private AccordionItem m_LayoutDetailsAccordionItem;
        private Vector2Field m_CardSizeField;
        private Dropdown m_UnitDropdown;
        private Toggle m_SafeZoneToggle;
        private FloatField m_SafeZoneDistanceField;
        private Toggle m_BackgroundToggle;
        // Add other layout controls as needed...

        // Elements Section
        private Accordion m_ElementsAccordion;
        private AccordionItem m_ElementsListAccordionItem;
        private ListView m_ElementList;
        private Button m_AddElementButton;
        private Button m_RemoveElementButton;

        // Element Properties Section
        private VisualElement m_ElementPropertiesSection;
        private VisualElement m_ElementPropertiesContent;

        // Save/Load Buttons
        private Button m_SaveButton;
        private Button m_LoadButton;

        // --- References ---
        private CardLayout m_CurrentCardLayout;
        private CardElement m_CurrentElement;
        private List<CardElement> m_CurrentElementsList = new List<CardElement>();
        private UnitSpace m_CurrentUnitSpace = UnitSpace.Millimeters; // Default unit
        private bool m_IgnoreValueChange = false;

        // --- Actions / Events (Provided by external system, e.g., CardEditorSystem) ---
        public event Action<CardElement> OnElementSelected; // Inform system about selection change
        public event Action OnLayoutChanged; // Inform system about general changes
        public event Action<CardElement> OnElementModified; // Inform system element properties changed
        private Action<CardElement> m_RequestShowHandlesAction;
        private Action m_RequestClearHandlesAction;
        private Action<CardElement> m_RequestHandleUpdateAction;

        // --- Constants ---
        private List<string> m_UnitOptions;
        private const int ADD_IMAGE_ACTION = 1;
        private const int ADD_TEXT_ACTION = 2;

        // --- Drag State (For ListView reordering) ---
        private List<int> m_DraggedIndices = null; // Indices being dragged

        public IntegratedCardEditorControl() // Constructor
        {
            // Load UXML
            var visualTree = Resources.Load<VisualTreeAsset>(UxmlPath);
            if (visualTree != null)
            {
                visualTree.CloneTree(this);
                // Apply Styles
                StyleSheet styleSheet = Resources.Load<StyleSheet>(UssPath);
                if (styleSheet != null) styleSheets.Add(styleSheet);
                else Debug.LogError($"IntegratedCardEditorControl: Could not load USS from {UssPath}");
            }
            else
            {
                Debug.LogError($"IntegratedCardEditorControl: Failed to load UXML from {UxmlPath}");
                this.Add(new Label("Error loading Integrated Card Editor UI."));
                return;
            }

            // Create a wrapper panel for proper background and picking mode
            // var wrapperPanel = new Panel(); // REMOVED
            // wrapperPanel.name = "integrated-card-editor-wrapper-panel"; // REMOVED
            // wrapperPanel.style.position = Position.Absolute; // REMOVED
            // wrapperPanel.style.top = 0; // REMOVED
            // wrapperPanel.style.left = 0; // REMOVED
            // wrapperPanel.style.right = 0; // REMOVED
            // wrapperPanel.style.bottom = 0; // REMOVED
            // wrapperPanel.style.backgroundColor = Color.clear; // REMOVED
            // wrapperPanel.pickingMode = PickingMode.Ignore; // Ignore clicks on the wrapper itself // REMOVED
            // this.Add(wrapperPanel); // REMOVED

            // Find UI elements (Querying from 'this')
            m_Container = this.Q<DraggablePanel>("integrated-card-editor-container");
            m_LayoutAccordion = this.Q<Accordion>("layout-accordion");
            m_SizeAccordionItem = this.Q<AccordionItem>("size-accordion-item");
            m_LayoutDetailsAccordionItem = this.Q<AccordionItem>("layout-details-accordion-item");
            m_CardSizeField = this.Q<Vector2Field>("card-size-field");
            m_UnitDropdown = this.Q<Dropdown>("unit-dropdown");
            m_SafeZoneToggle = this.Q<Toggle>("safe-zone-toggle");
            m_SafeZoneDistanceField = this.Q<FloatField>("safe-zone-distance-field");
            m_BackgroundToggle = this.Q<Toggle>("background-toggle");
            m_ElementsAccordion = this.Q<Accordion>("elements-accordion");
            m_ElementsListAccordionItem = this.Q<AccordionItem>("elements-list-accordion-item");
            m_ElementList = this.Q<ListView>("element-list");
            m_AddElementButton = this.Q<Button>("add-element-button");
            m_RemoveElementButton = this.Q<Button>("remove-element-button");
            m_ElementPropertiesSection = this.Q<VisualElement>("element-properties-section");
            m_ElementPropertiesContent = this.Q<VisualElement>("element-properties-content");
            m_SaveButton = this.Q<Button>("save-button");
            m_LoadButton = this.Q<Button>("load-button");

            // Basic null checks
            if (m_Container == null || m_LayoutAccordion == null || m_SizeAccordionItem == null ||
                m_LayoutDetailsAccordionItem == null || m_CardSizeField == null ||
                m_UnitDropdown == null || m_SafeZoneToggle == null || m_SafeZoneDistanceField == null ||
                m_BackgroundToggle == null || m_ElementsAccordion == null || m_ElementsListAccordionItem == null ||
                m_ElementList == null || m_AddElementButton == null || m_RemoveElementButton == null ||
                m_ElementPropertiesSection == null || m_ElementPropertiesContent == null)
            {
                Debug.LogError("IntegratedCardEditorControl: Could not find all required UI elements. Check UXML names.");
                return;
            }

            // Create save/load buttons if they don't exist in the UXML
            if (m_SaveButton == null || m_LoadButton == null)
            {
                // Create a container for the buttons
                var buttonContainer = new VisualElement();
                buttonContainer.name = "save-load-button-container";
                buttonContainer.AddToClassList("button-container");
                buttonContainer.style.flexDirection = FlexDirection.Row;
                buttonContainer.style.justifyContent = Justify.SpaceBetween;
                buttonContainer.style.marginTop = 10;

                // Create save button if it doesn't exist
                if (m_SaveButton == null)
                {
                    m_SaveButton = new Button();
                    m_SaveButton.name = "save-button";
                    m_SaveButton.title = "Save to JSON";
                    m_SaveButton.style.flexGrow = 1;
                    m_SaveButton.style.marginRight = 5;
                    buttonContainer.Add(m_SaveButton);
                }

                // Create load button if it doesn't exist
                if (m_LoadButton == null)
                {
                    m_LoadButton = new Button();
                    m_LoadButton.name = "load-button";
                    m_LoadButton.title = "Load from JSON";
                    m_LoadButton.style.flexGrow = 1;
                    m_LoadButton.style.marginLeft = 5;
                    buttonContainer.Add(m_LoadButton);
                }

                // Add the button container to the layout accordion
                m_LayoutAccordion.Add(buttonContainer);
            }

            // Register Callbacks
            m_UnitDropdown.RegisterValueChangedCallback(OnUnitChanged);
            m_CardSizeField.RegisterValueChangedCallback(OnCardSizeChanged);
            m_SafeZoneToggle.RegisterValueChangedCallback(OnSafeZoneToggleChanged);
            m_SafeZoneDistanceField.RegisterValueChangedCallback(OnSafeZoneDistanceChanged);
            m_BackgroundToggle.RegisterValueChangedCallback(OnBackgroundToggleChanged);
            m_ElementList.selectedIndicesChanged += OnListSelectionChanged;
            m_AddElementButton.clicked += AddNewElement;
            m_RemoveElementButton.clicked += RemoveSelectedElement;
            m_SaveButton.clicked += SaveCardToJson;
            m_LoadButton.clicked += LoadCardFromJson;

            // Initial State
            InitializeUnitDropdown();
            m_Container.AddToClassList("hidden"); // Add hidden by default
            ClearActiveCardLayout();
            UpdateFieldFormatting();
        }

        /// <summary>
        /// Provides actions from the controlling system (e.g., CardEditorSystem).
        /// </summary>
        public void SetupActions(Action<CardElement> showHandles, Action clearHandles, Action<CardElement> updateHandles)
        {
            m_RequestShowHandlesAction = showHandles;
            m_RequestClearHandlesAction = clearHandles;
            m_RequestHandleUpdateAction = updateHandles;
        }

        // --- Initialization & Configuration ---

        private void ConfigureElementListView()
        {
            // Use standard UIElements for list items
            m_ElementList.makeItem = () => {
                var itemRow = new VisualElement() { name = "element-item-row" };
                itemRow.AddToClassList("element-list-item"); // Apply USS style

                // Use AppUI Toggle for consistency if preferred, or standard UIElements Toggle
                var visibilityToggle = new AppUIToggle() { name = "visibility-toggle" };
                visibilityToggle.AddToClassList("element-list-item__visibility-toggle");

                // Use standard UIElements TextElement for the label
                var label = new TextElement() { name = "element-label" };
                label.AddToClassList("element-list-item__label");

                itemRow.Add(visibilityToggle);
                itemRow.Add(label);
                return itemRow;
            };

            // Set up how to bind data to items
            m_ElementList.bindItem = (element, index) => {
                if (m_CurrentElementsList == null || index < 0 || index >= m_CurrentElementsList.Count) return;

                var uiElement = m_CurrentElementsList[index];
                var label = element.Q<TextElement>("element-label");
                var toggle = element.Q<AppUIToggle>("visibility-toggle"); // Query AppUI Toggle

                if (label != null) label.text = uiElement?.ElementName ?? "<Missing Element>";
                if (toggle != null)
                {
                    toggle.UnregisterValueChangedCallback(OnVisibilityToggleChanged); // Avoid duplicate listeners
                    toggle.SetValueWithoutNotify(uiElement?.IsVisible ?? false);
                    toggle.userData = uiElement; // Store element for the callback
                    toggle.RegisterValueChangedCallback(OnVisibilityToggleChanged);
                }
            };

            // Handle selection changes
            m_ElementList.selectedIndicesChanged += OnListSelectionChanged;

            // Handle reordering via built-in mechanism
            // The ListView with reorderable=true should modify the itemsSource list directly.
            // We will react to potential changes by updating sibling indices in UpdateElementList.
        }

        private void InitializeUnitDropdown()
        {
            m_UnitOptions = new List<string>();
            // Use the shared UnitConverter dictionary
            foreach (var unit in UnitConverter.kUnitLabels.OrderBy(kvp => kvp.Key)) // Order by enum value
            {
                 m_UnitOptions.Add(unit.Value);
            }

            m_UnitDropdown.sourceItems = m_UnitOptions;
            m_UnitDropdown.bindItem = (item, index) => {
                item.label = m_UnitOptions[index];
            };

            // Set default selection (e.g., Millimeters)
            m_CurrentUnitSpace = UnitSpace.Millimeters;
            int defaultIndex = m_UnitOptions.IndexOf(UnitConverter.kUnitLabels[m_CurrentUnitSpace]);
            if (defaultIndex >= 0)
            {
                m_UnitDropdown.SetValueWithoutNotify(new int[] { defaultIndex });
            }
        }

        // --- Callback Handlers ---

        private void OnUnitChanged(ChangeEvent<IEnumerable<int>> evt)
        {
             if (m_IgnoreValueChange || !evt.newValue.Any()) return;

             UnitSpace newUnit = GetSelectedUnitSpace();
             if (newUnit == m_CurrentUnitSpace) return; // No actual change

             Debug.Log($"Unit changed from {m_CurrentUnitSpace} to {newUnit}. Updating relevant UI fields.");
             m_CurrentUnitSpace = newUnit;

             m_IgnoreValueChange = true;
             // Update fields that display values in the current unit
             UpdateLayoutFields(); // Re-populates layout fields with correct values for the new unit
             // If an element is selected, update its property fields as well
             if (m_CurrentElement != null)
             {
                GenerateElementPropertyFields(m_CurrentElement);
             }
             UpdateFieldFormatting(); // Apply new formatting (decimal places, unit labels)
             m_IgnoreValueChange = false;
        }

        private void OnCardSizeChanged(ChangeEvent<Vector2> evt)
        {
            // Get CardMeshGenerator via GetComponent
            CardMeshGenerator meshGen = m_CurrentCardLayout?.GetComponent<CardMeshGenerator>();
            if (m_IgnoreValueChange || m_CurrentCardLayout == null || meshGen == null) return;

            Vector2 sizeInCurrentUnit = evt.newValue;

            // Convert size from current UI unit back to millimeters (base unit for CardSizeData)
            float widthMm = UnitConverter.ToMillimeters(sizeInCurrentUnit.x, m_CurrentUnitSpace);
            float heightMm = UnitConverter.ToMillimeters(sizeInCurrentUnit.y, m_CurrentUnitSpace);

            // Clamp values if necessary (e.g., minimum size)
            widthMm = Mathf.Max(1f, widthMm); // Example: Minimum 1mm width
            heightMm = Mathf.Max(1f, heightMm);

            // Check if the value actually changed significantly after conversion/clamping
            if (Mathf.Abs(meshGen.CurrentSizeData.Width - widthMm) > 0.001f || Mathf.Abs(meshGen.CurrentSizeData.Height - heightMm) > 0.001f)
            {
                // Option B: Modify CurrentSizeData
                meshGen.CurrentSizeData.Width = widthMm;
                meshGen.CurrentSizeData.Height = heightMm;
                // Assume CardMeshGenerator detects changes to CurrentSizeData or updates elsewhere
                Debug.Log($"Card size changed via UI to W: {widthMm:F2}mm, H: {heightMm:F2}mm");

                OnLayoutChanged?.Invoke(); // Notify system

                // Read back the potentially clamped value from meshGen and update the UI field
                // to ensure consistency (in case SetDimensions clamps internally)
                float actualWidthMm = meshGen.CurrentSizeData.Width;
                float actualHeightMm = meshGen.CurrentSizeData.Height;
                Vector2 actualSizeInCurrentUnit = new Vector2(
                    UnitConverter.FromMillimeters(actualWidthMm, m_CurrentUnitSpace),
                    UnitConverter.FromMillimeters(actualHeightMm, m_CurrentUnitSpace)
                );

                if (Vector2.Distance(actualSizeInCurrentUnit, sizeInCurrentUnit) > 0.001f)
                {
                    m_IgnoreValueChange = true;
                    m_CardSizeField.SetValueWithoutNotify(actualSizeInCurrentUnit);
                    m_IgnoreValueChange = false;
                }
            }

            UpdateFieldFormatting(); // Update formatting just in case
        }

        private void OnSafeZoneToggleChanged(ChangeEvent<bool> evt)
        {
            if (m_IgnoreValueChange || m_CurrentCardLayout == null) return;

            // Assuming CardLayout has a property like UseSafeZone
            // m_CurrentCardLayout.UseSafeZone = evt.newValue;
            Debug.LogWarning("Safe Zone Toggle: CardLayout.UseSafeZone property not yet implemented or linked."); // Placeholder

            // Enable/disable distance field based on toggle state
            m_SafeZoneDistanceField?.SetEnabled(evt.newValue);

            OnLayoutChanged?.Invoke();
        }

        private void OnSafeZoneDistanceChanged(ChangeEvent<float> evt)
        {
            if (m_IgnoreValueChange || m_CurrentCardLayout == null) return;

            float distanceInCurrentUnit = evt.newValue;
            distanceInCurrentUnit = Mathf.Max(0f, distanceInCurrentUnit); // Ensure non-negative

            // Convert to base unit (e.g., mm)
            float distanceMm = UnitConverter.ToMillimeters(distanceInCurrentUnit, m_CurrentUnitSpace);

            // Assuming CardLayout has a property like SafeZoneDistanceMm
            // if (Mathf.Abs(m_CurrentCardLayout.SafeZoneDistanceMm - distanceMm) > 0.001f)
            // {
            //     m_CurrentCardLayout.SafeZoneDistanceMm = distanceMm;
                 Debug.LogWarning("Safe Zone Distance: CardLayout.SafeZoneDistanceMm property not yet implemented or linked."); // Placeholder
            //     OnLayoutChanged?.Invoke();

            //     // Read back actual value and update UI if clamped
            //     float actualDistanceMm = m_CurrentCardLayout.SafeZoneDistanceMm;
            //     float actualDistanceInCurrentUnit = UnitConverter.FromMillimeters(actualDistanceMm, m_CurrentUnitSpace);
            //     if (Mathf.Abs(actualDistanceInCurrentUnit - distanceInCurrentUnit) > 0.001f)
            //     {
            //         m_IgnoreValueChange = true;
            //         m_SafeZoneDistanceField.SetValueWithoutNotify(actualDistanceInCurrentUnit);
            //         m_IgnoreValueChange = false;
            //     }
            // }

            UpdateFieldFormatting();
        }

        private void OnBackgroundToggleChanged(ChangeEvent<bool> evt)
        {
            if (m_IgnoreValueChange || m_CurrentCardLayout == null) return;

            // Assuming CardLayout has a property like ShowBackground
            // m_CurrentCardLayout.ShowBackground = evt.newValue;
            Debug.LogWarning("Background Toggle: CardLayout.ShowBackground property not yet implemented or linked."); // Placeholder

            OnLayoutChanged?.Invoke();
        }

        private void OnVisibilityToggleChanged(ChangeEvent<bool> evt)
        {
            var toggle = evt.target as AppUIToggle;
            if (toggle?.userData is CardElement element)
            {
                element.IsVisible = evt.newValue;
                OnLayoutChanged?.Invoke(); // Notify system that layout might visually change
                OnElementModified?.Invoke(element); // Notify system element data changed
            }
        }

        private void OnListSelectionChanged(IEnumerable<int> selectedIndices)
        {
            if (m_CurrentCardLayout == null) return;

            int selectedIndex = selectedIndices.Any() ? selectedIndices.First() : -1;
            CardElement selectedElement = null;

            // Ensure list isn't null before accessing
            if (m_CurrentElementsList != null && selectedIndex >= 0 && selectedIndex < m_CurrentElementsList.Count)
            {
                selectedElement = m_CurrentElementsList[selectedIndex];
            }

            // Update internal state and UI based on selection
            if (selectedElement != null)
            {
                SelectElement(selectedElement);
            }
            else
            {
                DeselectElement(); // Explicitly deselect if index is invalid or list empty
            }

            // Update button enable states
            UpdateButtonStates();
        }

        // --- Helper Methods ---

        private void UpdateButtonStates()
        {
             bool elementSelected = m_ElementList != null && m_ElementList.selectedIndices.Any();
             bool cardActive = m_CurrentCardLayout != null;

             // Add null checks for buttons just in case
             m_RemoveElementButton?.SetEnabled(elementSelected && cardActive);
             m_AddElementButton?.SetEnabled(cardActive);

             // Disable layout fields if no card is active?
             m_SizeAccordionItem?.SetEnabled(cardActive);
             m_LayoutDetailsAccordionItem?.SetEnabled(cardActive);
        }

        private void SelectElement(CardElement element)
        {
            if (element == null)
            {
                DeselectElement();
                return;
            }

            m_CurrentElement = element;

            // Show and Populate Properties Section
            m_ElementPropertiesSection.style.display = DisplayStyle.Flex;
            GenerateElementPropertyFields(element); // Populate with specific fields

            // Notify external systems
            OnElementSelected?.Invoke(element);
            m_RequestShowHandlesAction?.Invoke(element);
        }

        private void DeselectElement()
        {
            m_CurrentElement = null;

            // Hide and Clear Properties Section
            if (m_ElementPropertiesSection != null) // Add null check
            {
                m_ElementPropertiesSection.style.display = DisplayStyle.None;
            }
            ClearElementPropertyFields(); // This already clears the content VE, safe if parent was null

            // Clear list selection visually (important!)
            if (m_ElementList != null) // Keep existing null check
            {
                m_ElementList.ClearSelection();
            }

            // Notify external systems
            OnElementSelected?.Invoke(null); // Notify with null
            m_RequestClearHandlesAction?.Invoke();

            // Re-enable layout controls when no element is selected
            m_SizeAccordionItem?.SetEnabled(m_CurrentCardLayout != null);
            m_LayoutDetailsAccordionItem?.SetEnabled(m_CurrentCardLayout != null);
            m_IgnoreValueChange = true;

            // meshGen is already fetched above
            CardMeshGenerator meshGen = m_CurrentCardLayout?.GetComponent<CardMeshGenerator>();
            if (meshGen != null)
            {
                CardSizeData sizeData = meshGen.CurrentSizeData;
                if (sizeData == null) // Add null check for sizeData
                {
                    Debug.LogError("CardMeshGenerator's CurrentSizeData is null. Cannot update layout fields.", meshGen);
                    m_IgnoreValueChange = false;
                    m_SizeAccordionItem?.SetEnabled(false);
                    m_LayoutDetailsAccordionItem?.SetEnabled(false); // Also disable foldout if data is bad
                    return;
                }

                // --- Update Size Fields ---
                float widthMeters = UnitConverter.MmToMeters(sizeData.Width);
                float heightMeters = UnitConverter.MmToMeters(sizeData.Height);

                // Convert to current display unit
                Vector2 sizeInCurrentUnit = new Vector2(
                    UnitConverter.FromMillimeters(sizeData.Width, m_CurrentUnitSpace),
                    UnitConverter.FromMillimeters(sizeData.Height, m_CurrentUnitSpace)
                );
                m_CardSizeField.SetValueWithoutNotify(sizeInCurrentUnit);

                // --- Update Layout Detail Fields ---
                // Example: Safe Zone (assuming properties exist on CardLayout or CardMeshGenerator)
                // m_SafeZoneToggle.SetValueWithoutNotify(m_CurrentCardLayout.UseSafeZone);
                // m_SafeZoneDistanceField.SetValueWithoutNotify(UnitConverter.FromMillimeters(m_CurrentCardLayout.SafeZoneDistanceMm, m_CurrentUnitSpace));
                // m_BackgroundToggle.SetValueWithoutNotify(m_CurrentCardLayout.ShowBackground);

                m_IgnoreValueChange = false;

                // Update formatting based on current unit
                UpdateFieldFormatting();
            }
        }

        private void GenerateElementPropertyFields(CardElement element)
        {
            ClearElementPropertyFields(); // Clear previous fields first
            if (element == null) return;

            m_IgnoreValueChange = true; // Prevent callbacks during setup

            // --- Common Fields (Name, Position, Size, Rotation) ---
            // Name Field
            var nameField = new TextField { /* label = "Name", // Removed */ value = element.ElementName, name = "element-name-field" };
            nameField.AddToClassList("property-field"); // Add class for potential styling
            VisualElement nameContainer = CreatePropertyContainer("Name", nameField);
            nameField.RegisterValueChangedCallback(evt => {
                if (m_IgnoreValueChange || m_CurrentElement == null) return;
                m_CurrentElement.ElementName = evt.newValue;
                int index = m_CurrentElementsList.IndexOf(m_CurrentElement);
                if (index >= 0) m_ElementList.RefreshItem(index); // Use RefreshItem instead of RebuildItemAtIndex
                OnElementModified?.Invoke(m_CurrentElement);
            });
            m_ElementPropertiesContent.Add(nameContainer);

            // Position Field (Needs unit conversion)
            Vector2 positionInCurrentUnit = GetPositionInCurrentUnit(element);
            var positionField = new Vector2Field { /* label = "Position", // Label provided by container */ value = positionInCurrentUnit, name = "element-position-field" };
            positionField.AddToClassList("property-field");
            VisualElement posContainer = CreatePropertyContainer("Position", positionField);
            positionField.RegisterValueChangedCallback(OnElementPositionChanged);
            m_ElementPropertiesContent.Add(posContainer);

            // Size Field (Needs unit conversion)
            Vector2 sizeInCurrentUnit = GetSizeInCurrentUnit(element);
            var sizeField = new Vector2Field { /* label = "Size", // Label provided by container */ value = sizeInCurrentUnit, name = "element-size-field" };
            sizeField.AddToClassList("property-field");
            VisualElement sizeContainer = CreatePropertyContainer("Size", sizeField);
            sizeField.RegisterValueChangedCallback(OnElementSizeChanged);
            m_ElementPropertiesContent.Add(sizeContainer);

            // Rotation Field
            float rotationZ = element.RotationDegrees;
            var rotationField = new FloatField { /* label = "Rotation", // Label provided by container */ value = rotationZ, name = "element-rotation-field" };
            rotationField.AddToClassList("property-field");
            VisualElement rotContainer = CreatePropertyContainer("Rotation", rotationField);
            rotationField.RegisterValueChangedCallback(OnElementRotationChanged);
            m_ElementPropertiesContent.Add(rotContainer);

            // --- Type-Specific Fields ---
            if (element is CardImageElement imageElem)
            {
                // Tint Color
                var tintField = new ColorField { /* label = "Tint Color", */ value = imageElem.TintColor, name = "element-tint-color-field" };
                tintField.AddToClassList("property-field");
                VisualElement tintContainer = CreatePropertyContainer("Tint Color", tintField);
                tintField.RegisterValueChangedCallback(evt => {
                    if (m_IgnoreValueChange || !(m_CurrentElement is CardImageElement currentImageElem)) return;
                    currentImageElem.TintColor = evt.newValue;
                    OnElementModified?.Invoke(currentImageElem);
                });
                 m_ElementPropertiesContent.Add(tintContainer);
                // Add other Image specific fields...
            }
            else if (element is CardTextElement textElem)
            {
                // Text Content
                 var contentField = new TextArea { /* label = "Text", // Removed */ value = textElem.Text, /* multiline = true, // Removed, inherent in TextArea */ name = "element-text-content-field" };
                 contentField.AddToClassList("property-field");
                 contentField.style.minHeight = 60; // Give multiline field some height
                 VisualElement textContainer = CreatePropertyContainer("Text", contentField);
                 contentField.RegisterValueChangedCallback(evt => {
                     if (m_IgnoreValueChange || !(m_CurrentElement is CardTextElement currentTextElem)) return;
                     currentTextElem.Text = evt.newValue;
                     OnElementModified?.Invoke(currentTextElem);
                 });
                 m_ElementPropertiesContent.Add(textContainer);

                 // Font Size (Example - assuming Font Size is unit-agnostic like points)
                 var fontSizeField = new FloatField { value = textElem.FontSize, name = "element-font-size-field" };
                 fontSizeField.AddToClassList("property-field");
                 VisualElement fontSizeContainer = CreatePropertyContainer("Font Size", fontSizeField);
                 fontSizeField.RegisterValueChangedCallback(evt => {
                     if (m_IgnoreValueChange || !(m_CurrentElement is CardTextElement currentTextElem)) return;
                     currentTextElem.FontSize = Mathf.Max(1f, evt.newValue); // Ensure positive font size
                     fontSizeField.SetValueWithoutNotify(currentTextElem.FontSize); // Update UI if clamped
                     OnElementModified?.Invoke(currentTextElem);
                 });
                 m_ElementPropertiesContent.Add(fontSizeContainer);

                // Add other Text specific fields (Color, Alignment, etc.)
            }

            m_IgnoreValueChange = false;

            // Apply formatting after adding fields and setting initial values
            UpdateFieldFormatting();
        }

        /// <summary>
        /// Helper to create a standard container for a property label and its field.
        /// </summary>
        private VisualElement CreatePropertyContainer(string labelText, VisualElement fieldElement)
        {
            var container = new VisualElement();
            container.AddToClassList("input-container"); // Use class from USS

            var label = new TextElement { text = labelText };
            label.AddToClassList("property-label"); // Use class from USS

            // Assume fieldElement already has property-field class
            container.Add(label);
            container.Add(fieldElement);
            return container;
        }

        // --- Element Property Changed Callbacks ---

        private void OnElementPositionChanged(ChangeEvent<Vector2> evt)
        {
            if (m_IgnoreValueChange || m_CurrentElement == null) return;

            // Value from UI is relative to top-left of the card, in m_CurrentUnitSpace
            Vector2 positionInCurrentUnit = evt.newValue;

            // Convert to millimeters
            float posX_mm = UnitConverter.ToMillimeters(positionInCurrentUnit.x, m_CurrentUnitSpace);
            float posY_mm = UnitConverter.ToMillimeters(positionInCurrentUnit.y, m_CurrentUnitSpace);

            // Get card dimensions
            CardLayout parentLayout = CardElementHelper.GetCardLayout(m_CurrentElement);
            CardMeshGenerator parentCardMesh = parentLayout?.GetComponent<CardMeshGenerator>();
            if (parentCardMesh == null)
            {
                Debug.LogWarning("Could not find parent CardMeshGenerator for position calculation.");
                return;
            }

            m_CurrentElement.PositionMM = new Vector2(posX_mm, posY_mm);
            m_CurrentElement.Update(); // Update the visual element

            OnElementModified?.Invoke(m_CurrentElement);
            m_RequestHandleUpdateAction?.Invoke(m_CurrentElement); // Position change requires handle update
        }

        private void OnElementSizeChanged(ChangeEvent<Vector2> evt)
        {
            if (m_IgnoreValueChange || m_CurrentElement == null) return;

            // Value from UI is Width/Height in m_CurrentUnitSpace
            Vector2 sizeInCurrentUnit = evt.newValue;

            // Ensure size is non-negative
            sizeInCurrentUnit.x = Mathf.Max(0.0001f, sizeInCurrentUnit.x);
            sizeInCurrentUnit.y = Mathf.Max(0.0001f, sizeInCurrentUnit.y);

            // Convert to millimeters, then to meters, as element properties work in base units
            float width_mm = UnitConverter.ToMillimeters(sizeInCurrentUnit.x, m_CurrentUnitSpace);
            float height_mm = UnitConverter.ToMillimeters(sizeInCurrentUnit.y, m_CurrentUnitSpace);
            Vector2 sizeMeters = new Vector2(
                UnitConverter.MmToMeters(width_mm),
                UnitConverter.MmToMeters(height_mm)
            );

            // --- Apply Size to CardElement ---
            // Convert from meters to millimeters
            float newWidth_mm = UnitConverter.MetersToMm(sizeMeters.x);
            float newHeight_mm = UnitConverter.MetersToMm(sizeMeters.y);

            // Get current size in millimeters
            float currentWidth_mm = m_CurrentElement.WidthMM;
            float currentHeight_mm = m_CurrentElement.HeightMM;

            // Check if size has changed significantly
            bool sizeChanged = Mathf.Abs(currentWidth_mm - newWidth_mm) > 0.01f ||
                               Mathf.Abs(currentHeight_mm - newHeight_mm) > 0.01f;

            // If size hasn't changed, exit
            if (!sizeChanged)
            {
                return; // No modification needed
            }

            // Apply new size
            m_CurrentElement.WidthMM = newWidth_mm;
            m_CurrentElement.HeightMM = newHeight_mm;

            // Update the UI element to refresh the visual representation
            m_CurrentElement.Update();
            // -----------------------------------------

            // Notify systems about modification
            OnElementModified?.Invoke(m_CurrentElement);
            m_RequestHandleUpdateAction?.Invoke(m_CurrentElement); // Size change requires handle update

            // Update UI field in case value was clamped internally by element properties
            // Get the actual size from the element
            float actualWidth_mm = m_CurrentElement.WidthMM;
            float actualHeight_mm = m_CurrentElement.HeightMM;

            // Convert actual millimeter size back to UI units for potential feedback/clamping update
            Vector2 actualSizeInUIUnits = new Vector2(
                UnitConverter.FromMillimeters(actualWidth_mm, m_CurrentUnitSpace),
                UnitConverter.FromMillimeters(actualHeight_mm, m_CurrentUnitSpace)
            );

            // Find the size field again (since callbacks don't capture local vars)
            var sizeField = m_ElementPropertiesContent.Q<Vector2Field>(name: "element-size-field");
            if (sizeField != null && Vector2.Distance(actualSizeInUIUnits, sizeInCurrentUnit) > 0.0001f)
            {
                 m_IgnoreValueChange = true;
                 sizeField.SetValueWithoutNotify(actualSizeInUIUnits);
                 m_IgnoreValueChange = false;
                 UpdateFieldFormatting(); // Reapply formatting if value changed
             }
        }

        private void OnElementRotationChanged(ChangeEvent<float> evt)
        {
            if (m_IgnoreValueChange || m_CurrentElement == null) return;

            // Value from UI is rotation around Z axis in degrees
            float rotationZ = evt.newValue;

            // Apply to CardElement's RotationDegrees property
            float currentRotation = m_CurrentElement.RotationDegrees;

            // Only apply if changed significantly
            if (Mathf.Abs(Mathf.DeltaAngle(currentRotation, rotationZ)) > 0.01f)
            {
                m_CurrentElement.RotationDegrees = rotationZ;
                m_CurrentElement.Update(); // Update the visual element
                OnElementModified?.Invoke(m_CurrentElement);
                m_RequestHandleUpdateAction?.Invoke(m_CurrentElement); // Rotation change requires handle update
            }
        }

        private void ClearElementPropertyFields()
        {
            // Add null check for the container itself
            if (m_ElementPropertiesContent != null)
            {
                m_ElementPropertiesContent.Clear();
            }
        }

        private void UpdateLayoutFields()
        {
             // Get CardMeshGenerator via GetComponent
             CardMeshGenerator meshGen = m_CurrentCardLayout?.GetComponent<CardMeshGenerator>();

             if (m_CurrentCardLayout == null || meshGen == null)
             {
                 // Disable/clear fields if no layout/meshgen
                 m_SizeAccordionItem?.SetEnabled(false);
                 m_LayoutDetailsAccordionItem?.SetEnabled(false);
                 // TODO: Set fields to default/disabled state
                 return;
             }

             m_SizeAccordionItem?.SetEnabled(true);
             m_LayoutDetailsAccordionItem?.SetEnabled(true);
             m_IgnoreValueChange = true;

             // Check if sizeData is null AFTER confirming meshGen is not null
             CardSizeData sizeData = meshGen.CurrentSizeData;
             if (sizeData == null)
             {
                 Debug.LogError("CardMeshGenerator's CurrentSizeData is null. Cannot update layout fields.", meshGen);
                 m_IgnoreValueChange = false;
                 m_SizeAccordionItem?.SetEnabled(false);
                 m_LayoutDetailsAccordionItem?.SetEnabled(false); // Also disable foldout if data is bad
                 return;
             }

             // --- Update Size Fields ---
             // float widthMeters = UnitConverter.MmToMeters(sizeData.Width); // No longer needed here
             // float heightMeters = UnitConverter.MmToMeters(sizeData.Height);

             // Convert to current display unit
             Vector2 sizeInCurrentUnit = new Vector2(
                 UnitConverter.FromMillimeters(sizeData.Width, m_CurrentUnitSpace),
                 UnitConverter.FromMillimeters(sizeData.Height, m_CurrentUnitSpace)
             );
             m_CardSizeField.SetValueWithoutNotify(sizeInCurrentUnit);

             // --- Update Layout Detail Fields ---
             // Example: Safe Zone (assuming properties exist on CardLayout or CardMeshGenerator)
             // m_SafeZoneToggle.SetValueWithoutNotify(m_CurrentCardLayout.UseSafeZone);
             // m_SafeZoneDistanceField.SetValueWithoutNotify(UnitConverter.FromMillimeters(m_CurrentCardLayout.SafeZoneDistanceMm, m_CurrentUnitSpace));
             // m_BackgroundToggle.SetValueWithoutNotify(m_CurrentCardLayout.ShowBackground);

             m_IgnoreValueChange = false;

             // Update formatting based on current unit
             UpdateFieldFormatting();
        }

        private void UpdateElementList()
        {
             if (m_ElementList == null) return;

             List<int> selectedIndicesBeforeRefresh = m_ElementList.selectedIndices.ToList();
             CardElement selectedElementBeforeRefresh = null;
             if (selectedIndicesBeforeRefresh.Any() && m_CurrentElementsList != null)
             {
                 int firstIndex = selectedIndicesBeforeRefresh.First();
                 if (firstIndex >= 0 && firstIndex < m_CurrentElementsList.Count)
                 {
                     selectedElementBeforeRefresh = m_CurrentElementsList[firstIndex];
                 }
             }

             if (m_CurrentCardLayout != null)
             {
                 // Get elements sorted by sibling index (visual order)
                 m_CurrentElementsList = m_CurrentCardLayout.GetUIElements()
                                          .OrderBy(c => CardElementHelper.GetSiblingIndex(c))
                                          .ToList();
                 m_ElementList.itemsSource = m_CurrentElementsList;
             }
             else
             {
                 m_CurrentElementsList.Clear();
                 m_ElementList.itemsSource = m_CurrentElementsList;
             }
             m_ElementList.Rebuild(); // Crucial after changing itemsSource or order

             // Since ListView reordering modifies itemsSource directly,
             // ensure GameObject sibling indices match the new list order.
             if (m_CurrentCardLayout != null)
             {
                 for (int i = 0; i < m_CurrentElementsList.Count; i++)
                 {
                     // CardElements don't have transform.GetSiblingIndex() or SetSiblingIndex()
                     // The order is managed by the CardLayout component
                 }
             }

             // Attempt to restore selection
             if (selectedElementBeforeRefresh != null)
             {
                 int newIndex = m_CurrentElementsList.IndexOf(selectedElementBeforeRefresh);
                 if (newIndex >= 0)
                 {
                     m_ElementList.SetSelection(newIndex);
                 }
                 else {
                     // If previously selected element is gone, deselect everything
                     DeselectElement();
                 }
             } else {
                 // If nothing was selected before, ensure nothing is selected now
                 DeselectElement();
             }

             UpdateButtonStates();
        }

        public UnitSpace GetSelectedUnitSpace()
        {
            if (m_UnitDropdown == null || m_UnitDropdown.value == null || !m_UnitDropdown.value.Any()) return UnitSpace.Millimeters; // Default
            int index = m_UnitDropdown.value.First();
            if (index >= 0 && index < m_UnitOptions.Count)
            {
                 string selectedLabel = m_UnitOptions[index];
                 // Find UnitSpace from label using UnitConverter
                 foreach (var unitPair in UnitConverter.kUnitLabels)
                 {
                     if (unitPair.Value == selectedLabel) return unitPair.Key;
                 }
            }
            return UnitSpace.Millimeters; // Fallback
        }

        private Vector2 GetPositionInCurrentUnit(CardElement element)
        {
            if (element == null) return Vector2.zero;

            // Get the transform of the element
            Transform elementTransform = CardElementHelper.GetTransform(element);
            if (elementTransform == null) return Vector2.zero;

            // Get card dimensions
            CardLayout parentLayout = CardElementHelper.GetCardLayout(element);
            CardMeshGenerator parentCardMesh = parentLayout?.GetComponent<CardMeshGenerator>();
            if (parentCardMesh == null)
            {
                Debug.LogWarning("Could not find parent CardMeshGenerator for position calculation.");
                return Vector2.zero;
            }

            CardSizeData parentSizeData = parentCardMesh.CurrentSizeData;
            float cardWidthMM = parentSizeData.Width;
            float cardHeightMM = parentSizeData.Height;

            // Get the element's local position in meters (relative to card center)
            Vector3 localPositionMeters = elementTransform.localPosition;

            // Get the element's size in meters
            float widthMeters = UnitConverter.MmToMeters(element.WidthMM);
            float heightMeters = UnitConverter.MmToMeters(element.HeightMM);

            // Calculate the top-left corner position in meters (relative to card center)
            Vector3 topLeftCornerMeters = new Vector3(
                localPositionMeters.x - (widthMeters * 0.5f),
                localPositionMeters.y + (heightMeters * 0.5f),
                localPositionMeters.z
            );

            // Convert to millimeters
            float topLeftX_mm = UnitConverter.MetersToMm(topLeftCornerMeters.x);
            float topLeftY_mm = UnitConverter.MetersToMm(topLeftCornerMeters.y);

            // Convert from card-center-relative to absolute coordinates
            // In the card's coordinate system, (0,0) is the center, (-w/2, h/2) is top-left
            Vector2 positionFromCardTopLeftMM = new Vector2(
                topLeftX_mm + (cardWidthMM * 0.5f),  // Add half width to get distance from left edge
                (cardHeightMM * 0.5f) - topLeftY_mm  // Subtract from half height to get distance from top edge (Y flipped)
            );

            // Convert to current display unit
            Vector2 positionInCurrentUnit = new Vector2(
                UnitConverter.FromMillimeters(positionFromCardTopLeftMM.x, m_CurrentUnitSpace),
                UnitConverter.FromMillimeters(positionFromCardTopLeftMM.y, m_CurrentUnitSpace)
            );

            // Debug log to verify the calculation
            //Debug.Log($"Element position: Center={element.PositionMM}mm, TopLeft={positionFromCardTopLeftMM}mm, UI={positionInCurrentUnit}{UnitConverter.GetUnitSuffix(m_CurrentUnitSpace)}");

            return positionInCurrentUnit;
        }

        private Vector2 GetSizeInCurrentUnit(CardElement element)
        {
            if (element == null) return Vector2.zero;

            // Get size in millimeters from CardElement
            float widthMM = element.WidthMM;
            float heightMM = element.HeightMM;

            // Convert to meters
            float widthMeters = UnitConverter.MmToMeters(widthMM);
            float heightMeters = UnitConverter.MmToMeters(heightMM);

            // Convert meters size to current display unit
            Vector2 sizeInCurrentUnit = new Vector2(
                UnitConverter.FromMillimeters(UnitConverter.MetersToMm(widthMeters), m_CurrentUnitSpace),
                UnitConverter.FromMillimeters(UnitConverter.MetersToMm(heightMeters), m_CurrentUnitSpace)
            );
            return sizeInCurrentUnit;
        }

        /// <summary>
        /// Updates *only* the displayed values of the transform-related fields (Pos, Size, Rot)
        /// based on the current state of the provided element and the selected unit.
        /// Assumes the fields have already been generated by GenerateElementPropertyFields.
        /// </summary>
        public void UpdateDisplayedTransformFields(CardElement element)
        {
            if (element == null || m_CurrentElement != element || m_ElementPropertiesSection.style.display == DisplayStyle.None)
            {
                // Only update if the provided element is the currently selected one and properties are visible
                return;
            }

            // Find existing fields
            var positionField = m_ElementPropertiesContent.Q<Vector2Field>(name: "element-position-field");
            var sizeField = m_ElementPropertiesContent.Q<Vector2Field>(name: "element-size-field");
            var rotationField = m_ElementPropertiesContent.Q<FloatField>(name: "element-rotation-field");

            if (positionField == null && sizeField == null && rotationField == null)
            {
                // Fields haven't been generated or were cleared
                return;
            }

            m_IgnoreValueChange = true; // Prevent callbacks

            // Get updated values in the current display unit
            Vector2 positionInCurrentUnit = GetPositionInCurrentUnit(element);
            Vector2 sizeInCurrentUnit = GetSizeInCurrentUnit(element);
            float rotationZ = element.RotationDegrees;

            // Update field values without triggering change events
            positionField?.SetValueWithoutNotify(positionInCurrentUnit);
            sizeField?.SetValueWithoutNotify(sizeInCurrentUnit);
            rotationField?.SetValueWithoutNotify(rotationZ);

            m_IgnoreValueChange = false;

            // Re-apply formatting
            UpdateFieldFormatting();
        }

        // --- Formatting Helper ---

        private void UpdateFieldFormatting()
        {
            Debug.Log($"Applying field formatting for unit: {m_CurrentUnitSpace}");

            // Format Layout Fields
            UpdateVector2FieldFormatting(m_CardSizeField, m_CurrentUnitSpace);
            UpdateFloatFieldFormatting(m_SafeZoneDistanceField, m_CurrentUnitSpace, isDistance: true);

            // Format Element Property Fields (if visible and populated)
            if (m_CurrentElement != null && m_ElementPropertiesSection.style.display == DisplayStyle.Flex)
            {
                // Find the fields dynamically within m_ElementPropertiesContent
                var positionField = m_ElementPropertiesContent.Q<Vector2Field>(name: "element-position-field"); // Assuming names are set
                var sizeField = m_ElementPropertiesContent.Q<Vector2Field>(name: "element-size-field");
                var rotationField = m_ElementPropertiesContent.Q<FloatField>(name: "element-rotation-field");
                var fontSizeField = m_ElementPropertiesContent.Q<FloatField>(name: "element-font-size-field");

                UpdateVector2FieldFormatting(positionField, m_CurrentUnitSpace);
                UpdateVector2FieldFormatting(sizeField, m_CurrentUnitSpace);
                UpdateFloatFieldFormatting(rotationField, m_CurrentUnitSpace, isRotation: true);
                UpdateFloatFieldFormatting(fontSizeField, m_CurrentUnitSpace, isFontSize: true); // Add Font Size formatting
            }
        }

        /// <summary>
        /// Updates the format string and unit label for a Vector2Field.
        /// </summary>
        private void UpdateVector2FieldFormatting(Vector2Field field, UnitSpace unit)
        {
            if (field == null) return;

            string format = GetFormatStringForUnit(unit);
            // string unitLabel = UnitConverter.kUnitLabelsShort.TryGetValue(unit, out var label) ? label : ""; // Unit label not directly applicable here

            // Attempt to set format string on the Vector2Field directly if the API supports it
            // Or potentially access internal FloatFields IF their names/classes are known and stable.
            // Based on the provided source, direct access seems difficult without reflection.
            // For now, we rely on the internal FloatFields inheriting the format if possible, or display raw values.
            // Consider adding explicit X/Y labels with units if needed: field.Q<FloatField>(...).unit = unitLabel;

            // Set format string on the main field (might cascade to internal fields in some UI systems)
            try {
                field.formatString = format; // Set on the parent Vector2Field
            } catch (Exception ex) {
                 Debug.LogWarning($"Could not set formatString directly on {field.name}: {ex.Message}");
            }
        }

        /// <summary>
        /// Updates the format string and unit label for a FloatField.
        /// </summary>
        private void UpdateFloatFieldFormatting(FloatField field, UnitSpace unit, bool isDistance = false, bool isRotation = false, bool isFontSize = false)
        {
            if (field == null) return;

            if (isRotation)
            {
                field.formatString = "0.0"; // Degrees always
                field.unit = "°";
            }
            else if (isFontSize)
            {
                 field.formatString = "0.0"; // Font size typically has one decimal place
                 field.unit = "pt"; // Assume points
            }
            else // isDistance or other length-based value
            {
                field.formatString = GetFormatStringForUnit(unit);
                field.unit = UnitConverter.kUnitLabelsShort.TryGetValue(unit, out var label) ? label : "";
            }
        }

        /// <summary>
        /// Gets the number format string based on the unit.
        /// </summary>
        private string GetFormatStringForUnit(UnitSpace unit)
        {
            switch (unit)
            {
                case UnitSpace.Millimeters:
                    return "0.00";
                case UnitSpace.Centimeters:
                    return "0.000";
                case UnitSpace.Inches:
                    return "0.0000";
                case UnitSpace.Meters:
                    return "0.0000";
                default:
                    return "G"; // General format
            }
        }

        // --- Element Add/Remove --- // Renamed section

        private void AddNewElement()
        {
            if (m_CurrentCardLayout == null || m_AddElementButton == null) return;

            // Use AppUI MenuBuilder
            var menu = Unity.AppUI.UI.MenuBuilder.Build(m_AddElementButton)
                .AddAction(ADD_IMAGE_ACTION, "Image", "", HandleAddElementSelection)
                .AddAction(ADD_TEXT_ACTION, "Text", "", HandleAddElementSelection);
            // Add more element types here...

            menu.Show();
        }

        // Handler for menu item selection
        private void HandleAddElementSelection(EventBase evt)
        {
             // Get actionId from userData of the clicked MenuItem
             if (!(evt.target is MenuItem clickedItem) || !(clickedItem.userData is int selectedActionId))
             {
                 Debug.LogError("Could not determine selected action ID from menu item.");
                 return;
             }

            if (m_CurrentCardLayout == null) return;

            string selectedTypeName = "";
            CardElement addedElement = null;
            CardElementData elementData = null; // Use base class CardElementData

            try
            {
                switch (selectedActionId)
                {
                    case ADD_IMAGE_ACTION:
                        selectedTypeName = "Image";
                        ImageElementData imageData = new ImageElementData(); // Use specific data type
                        imageData.ElementName = "New Image";
                        imageData.Width = LengthValue.FromMillimeters(63f); // Default width in mm
                        imageData.Height = LengthValue.FromMillimeters(63f); // Default height in mm
                        imageData.IsVisible = true;
                        imageData.PositionX = LengthValue.FromMillimeters(0f); // Default position (center relative)
                        imageData.PositionY = LengthValue.FromMillimeters(0f);
                        imageData.RotationDegrees = 0f;
                        imageData.TintColor = Color.white;
                        elementData = imageData;
                        break;
                    case ADD_TEXT_ACTION:
                        selectedTypeName = "Text";
                        TextElementData textData = new TextElementData(); // Use specific data type
                        textData.ElementName = "New Text";
                        textData.Width = LengthValue.FromMillimeters(40f); // Default width in mm
                        textData.Height = LengthValue.FromMillimeters(20f); // Default height in mm
                        textData.TextContent = "Sample Text";
                        textData.FontSize = 12f;
                        textData.IsVisible = true;
                        textData.PositionX = LengthValue.FromMillimeters(0f);
                        textData.PositionY = LengthValue.FromMillimeters(0f);
                        textData.RotationDegrees = 0f;
                        textData.FontColor = Color.black;
                        textData.Alignment = TextAnchor.MiddleCenter;
                        elementData = textData;
                        break;
                    // Add cases for other component types...
                    default:
                        Debug.LogWarning($"Unknown action ID selected: {selectedActionId}");
                        return;
                }

                 Debug.Log($"Attempting to add element of type {selectedTypeName} via menu.");
                 if (elementData != null)
                 {
                    addedElement = m_CurrentCardLayout.AddElementFromData(elementData);
                 }
            }
            catch (Exception ex)
            {
                 // Ensure selectedTypeName is set even on error for logging
                 if (string.IsNullOrEmpty(selectedTypeName))
                 {
                     if(selectedActionId == ADD_IMAGE_ACTION) selectedTypeName = "Image";
                     else if (selectedActionId == ADD_TEXT_ACTION) selectedTypeName = "Text";
                     // ... add others
                 }
                Debug.LogError($"Error adding element of type {selectedTypeName} via menu: {ex.Message}", m_CurrentCardLayout);
            }

            // If an element was successfully added
            if (addedElement != null)
            {
                UpdateElementList(); // Refresh list view

                // Find the index of the newly added element and select it
                int newIndex = m_CurrentElementsList.IndexOf(addedElement);
                 if(newIndex >= 0)
                 {
                    m_ElementList.SetSelection(newIndex);
                    m_ElementList.ScrollToItem(newIndex); // Ensure it's visible
                 }
                 else
                 {
                    Debug.LogWarning("Added element not found in list after refresh.");
                 }
                 OnLayoutChanged?.Invoke(); // Notify system layout changed
            }
            // UpdateButtonStates is called by UpdateElementList/SetSelection
        }

        private void RemoveSelectedElement()
        {
            if (m_CurrentCardLayout == null || m_ElementList == null || !m_ElementList.selectedIndices.Any()) return;

            int selectedIndex = m_ElementList.selectedIndices.First();
            if (selectedIndex < 0 || selectedIndex >= m_CurrentElementsList.Count) return;

            CardElement selectedElement = m_CurrentElementsList[selectedIndex];
            if (selectedElement != null)
            {
                 Debug.Log($"Removing element: {selectedElement.ElementName}");
                 // Ensure properties panel is cleared BEFORE destroying
                 DeselectElement();

                 // Remove the element from the layout
                 m_CurrentCardLayout.RemoveElement(selectedElement);

                 // Remove from internal list immediately for consistency before full refresh
                 m_CurrentElementsList.RemoveAt(selectedIndex);

                 // Refresh the list view completely after destruction
                 UpdateElementList(); // This will rebuild the list, clear selection, and update buttons
                 OnLayoutChanged?.Invoke(); // Notify system layout changed
            }
            else
            {
                Debug.LogWarning($"Attempted to remove null element at index {selectedIndex}.");
                // Still refresh list in case data is inconsistent
                UpdateElementList();
            }
        }

        // --- Public API ---

        public void SetActiveCardLayout(CardLayout layout)
        {
            m_CurrentCardLayout = layout;
            if (m_CurrentCardLayout != null)
            {
                UpdateLayoutFields();
                UpdateElementList(); // This now handles deselection if needed
                ShowUI();
            }
            else
            {
                ClearActiveCardLayout();
            }
            UpdateButtonStates(); // Update button states after setting/clearing layout
        }

        public void ClearActiveCardLayout()
        {
            m_CurrentCardLayout = null;
            m_CurrentElementsList.Clear();
            if(m_ElementList != null) // Add null check
            {
                m_ElementList.itemsSource = m_CurrentElementsList;
                m_ElementList.Rebuild();
            }
            DeselectElement(); // Ensure properties are hidden and state is cleared
            HideUI();
            UpdateLayoutFields(); // Update layout fields to disabled state
            UpdateButtonStates();
        }

        // --- Visibility Control ---
        public void ShowUI()
        {
            if (m_Container == null) return;
            m_Container.RemoveFromClassList("hidden");
            m_Container.AddToClassList(VisibleClassName);
            m_Container.pickingMode = PickingMode.Position;
        }

        public void HideUI()
        {
            if (m_Container == null) return;
            m_Container.RemoveFromClassList(VisibleClassName);
            m_Container.AddToClassList("hidden");
            m_Container.pickingMode = PickingMode.Ignore;
        }

        // --- Save/Load Methods ---

        /// <summary>
        /// Saves the current card data to a JSON file.
        /// </summary>
        private void SaveCardToJson()
        {
            if (m_CurrentCardLayout == null)
            {
                Debug.LogError("Cannot save: No card is currently selected.");
                return;
            }

            CardMeshGenerator meshGen = m_CurrentCardLayout.GetComponent<CardMeshGenerator>();
            if (meshGen == null)
            {
                Debug.LogError("Cannot save: CardMeshGenerator component not found on the current card.");
                return;
            }

            // Create a temporary CardDefinition to hold the data
            CardDefinition cardDefinition = ScriptableObject.CreateInstance<CardDefinition>();
            cardDefinition.CardName = meshGen.gameObject.name;
            cardDefinition.SizeData = meshGen.CurrentSizeData.Clone(); // Clone to avoid reference issues

            // Create a new CardLayoutData from the current layout
            CardLayoutData layoutData = new CardLayoutData();

            // Get all elements from the current layout
            List<CardElement> elements = m_CurrentCardLayout.GetElements();
            foreach (var element in elements)
            {
                CardElementData elementData = null;

                if (element is CardImageElement imageElement)
                {
                    ImageElementData imageData = new ImageElementData();
                    imageData.ElementName = imageElement.ElementName;
                    imageData.Width = imageElement.WidthMM;
                    imageData.Height = imageElement.HeightMM;
                    imageData.PositionX = LengthValue.FromMillimeters(imageElement.PositionMM.x);
                    imageData.PositionY = LengthValue.FromMillimeters(imageElement.PositionMM.y);
                    imageData.RotationDegrees = imageElement.RotationDegrees;
                    imageData.IsVisible = imageElement.IsVisible;
                    imageData.TintColor = imageElement.TintColor;
                    elementData = imageData;
                }
                else if (element is CardTextElement textElement)
                {
                    TextElementData textData = new TextElementData();
                    textData.ElementName = textElement.ElementName;
                    textData.Width = textElement.WidthMM;
                    textData.Height = textElement.HeightMM;
                    textData.PositionX = LengthValue.FromMillimeters(textElement.PositionMM.x);
                    textData.PositionY = LengthValue.FromMillimeters(textElement.PositionMM.y);
                    textData.RotationDegrees = textElement.RotationDegrees;
                    textData.IsVisible = textElement.IsVisible;
                    textData.TextContent = textElement.Text;
                    textData.FontSize = textElement.FontSize;
                    textData.FontColor = textElement.FontColor;
                    textData.Alignment = textElement.TextAlignment;
                    textData.AutoFit = textElement.AutoFit;
                    if (textElement.FontAsset != null)
                    {
                        textData.FontName = textElement.FontAsset.name;
                    }
                    elementData = textData;
                }

                if (elementData != null)
                {
                    layoutData.Elements.Add(elementData);
                }
            }

            cardDefinition.LayoutData = layoutData;

            // Convert to JSON
            string json = JsonUtility.ToJson(cardDefinition, true); // true for pretty print

            // Determine save path
            string fileName = $"{cardDefinition.CardName}.json";
            string directory = Path.Combine(Application.persistentDataPath, "CardData");
            string filePath = Path.Combine(directory, fileName);

            try
            {
                // Create directory if it doesn't exist
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Write JSON to file
                File.WriteAllText(filePath, json);
                Debug.Log($"Card data saved to: {filePath}");

                // Show a notification
                ShowNotification($"Card saved to: {Path.GetFileName(filePath)}");

                // Clean up ScriptableObjects
                ScriptableObject.Destroy(cardDefinition);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error saving card data: {ex.Message}");
                ShowNotification($"Error saving card: {ex.Message}", true);

                // Clean up ScriptableObjects even on error
                ScriptableObject.Destroy(cardDefinition);
            }
        }

        /// <summary>
        /// Loads card data from a JSON file.
        /// </summary>
        private void LoadCardFromJson()
        {
            if (m_CurrentCardLayout == null)
            {
                Debug.LogError("Cannot load: No card is currently selected.");
                return;
            }

            CardMeshGenerator meshGen = m_CurrentCardLayout.GetComponent<CardMeshGenerator>();
            if (meshGen == null)
            {
                Debug.LogError("Cannot load: CardMeshGenerator component not found on the current card.");
                return;
            }

            // Determine load directory
            string directory = Path.Combine(Application.persistentDataPath, "CardData");
            if (!Directory.Exists(directory))
            {
                Debug.LogError($"Card data directory not found: {directory}");
                return;
            }

            // Get all JSON files in the directory
            string[] jsonFiles = Directory.GetFiles(directory, "*.json");
            if (jsonFiles.Length == 0)
            {
                Debug.LogError($"No JSON files found in: {directory}");
                return;
            }

            // Show a menu with all available JSON files
            var menu = Unity.AppUI.UI.MenuBuilder.Build(m_LoadButton);

            for (int i = 0; i < jsonFiles.Length; i++)
            {
                string filePath = jsonFiles[i];
                string fileName = Path.GetFileNameWithoutExtension(filePath);
                menu.AddAction(i, fileName, "", (evt) => HandleLoadFileSelection(evt, jsonFiles));
            }

            menu.Show();
        }

        /// <summary>
        /// Handles the selection of a JSON file from the load menu.
        /// </summary>
        private void HandleLoadFileSelection(EventBase evt, string[] jsonFiles)
        {
            // Get actionId from userData of the clicked MenuItem
            if (!(evt.target is MenuItem clickedItem) || !(clickedItem.userData is int selectedIndex))
            {
                Debug.LogError("Could not determine selected file index from menu item.");
                return;
            }

            if (selectedIndex < 0 || selectedIndex >= jsonFiles.Length)
            {
                Debug.LogError($"Invalid file index: {selectedIndex}");
                return;
            }

            string filePath = jsonFiles[selectedIndex];
            LoadCardDataFromFile(filePath);
        }

        /// <summary>
        /// Loads card data from the specified JSON file.
        /// </summary>
        private void LoadCardDataFromFile(string filePath)
        {
            if (m_CurrentCardLayout == null || !File.Exists(filePath))
            {
                return;
            }

            CardMeshGenerator meshGen = m_CurrentCardLayout.GetComponent<CardMeshGenerator>();
            if (meshGen == null)
            {
                return;
            }

            try
            {
                // Read JSON from file
                string json = File.ReadAllText(filePath);

                // Convert to CardDefinition
                CardDefinition cardDefinition = ScriptableObject.CreateInstance<CardDefinition>();
                JsonUtility.FromJsonOverwrite(json, cardDefinition);
                if (cardDefinition == null)
                {
                    Debug.LogError("Failed to create CardDefinition from JSON data.");
                    return;
                }

                // Apply the loaded data to the current card
                meshGen.gameObject.name = cardDefinition.CardName;

                // Apply size data
                if (cardDefinition.SizeData != null)
                {
                    meshGen.ApplySizeData(cardDefinition.SizeData);
                }

                // Apply layout data
                if (cardDefinition.LayoutData != null)
                {
                    m_CurrentCardLayout.ClearElements();
                    m_CurrentCardLayout.ApplyLayoutData(cardDefinition.LayoutData);
                }

                // Update UI
                UpdateLayoutFields();
                UpdateElementList();

                Debug.Log($"Card data loaded from: {filePath}");
                ShowNotification($"Card loaded from: {Path.GetFileName(filePath)}");

                // Clean up temporary ScriptableObject
                ScriptableObject.Destroy(cardDefinition);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error loading card data: {ex.Message}");
                ShowNotification($"Error loading card: {ex.Message}", true);
            }
        }

        /// <summary>
        /// Shows a notification message to the user.
        /// </summary>
        /// <param name="message">The message to display.</param>
        /// <param name="isError">Whether this is an error message.</param>
        private void ShowNotification(string message, bool isError = false)
        {
            // Create a notification element
            var notification = new VisualElement();
            notification.name = "notification";
            notification.AddToClassList("notification");
            notification.style.position = Position.Absolute;
            notification.style.bottom = 20;
            notification.style.left = 0;
            notification.style.right = 0;
            notification.style.height = 40;
            notification.style.backgroundColor = isError ? new Color(0.8f, 0.2f, 0.2f, 0.9f) : new Color(0.2f, 0.6f, 0.2f, 0.9f);
            notification.style.borderBottomLeftRadius = notification.style.borderBottomRightRadius = notification.style.borderTopLeftRadius = notification.style.borderTopRightRadius = 5;
            notification.style.paddingLeft = 10;
            notification.style.paddingRight = 10;
            notification.style.marginLeft = 20;
            notification.style.marginRight = 20;
            notification.style.alignItems = Align.Center;
            notification.style.justifyContent = Justify.Center;

            // Add message text
            var messageText = new TextElement();
            messageText.text = message;
            messageText.style.color = Color.white;
            messageText.style.unityTextAlign = TextAnchor.MiddleCenter;
            notification.Add(messageText);

            // Add to the container
            m_Container.Add(notification);

            // Remove after a delay
            notification.schedule.Execute(() => {
                notification.RemoveFromHierarchy();
            }).ExecuteLater(3000); // 3 seconds
        }
    }
}

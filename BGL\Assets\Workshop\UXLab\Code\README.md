# Card Mesh Generator

A Unity component that dynamically generates a 3D mesh for a card with rounded corners.

## Features

- Create cards with customizable dimensions (width, height, thickness)
- Adjustable corner radius
- Control corner detail level through segment count
- Unit conversion support (meters, centimeters, millimeters)
- Custom inspector for easy editing

## Usage

1. Add the `CardMeshGenerator` component to a GameObject
2. Adjust the dimensions and corner properties in the Inspector
3. The mesh is automatically generated and updated when properties change

The component includes:
- `CardMeshGenerator.cs` - The main component that generates the card mesh
- `CardMeshGeneratorEditor.cs` - Custom editor for the component

## Demo

Check out the `CardMeshDemo` scene for a working example. 
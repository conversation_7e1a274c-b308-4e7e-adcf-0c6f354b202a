# Card Area System

The CardAreaSystem provides functionality for creating magnetic zones that constrain card movement. Cards within an area are locked to that area until they cross a threshold outside the boundary.

## Features

- Create rectangular areas that act as magnetic zones for cards
- Cards within an area are constrained to stay within the area
- Cards can leave an area only when they cross the outer threshold
- Visual representation of areas with customizable colors
- Easy integration with the existing CardInteractionSystem

## Setup

1. Make sure you have a `TableManager` instance in your scene.
2. Add the `CardAreaSystem` asset to the TableManager's Systems list.
3. Create card areas in your scene using the GameObject menu: GameObject > UX Lab > Card Area
4. Position and size the card areas as needed.
5. The system will automatically initialize when the TableManager initializes.

## Usage

### In the Editor

1. Create a new card area using GameObject > UX Lab > Card Area
2. Adjust the inner size to define the main area where cards will be constrained
3. Set the threshold distance to define how far a card must be dragged outside the area to be released
4. Customize the visualization colors if desired

The card areas are visualized in the Scene view using Gizmos:
- The inner boundary (white area) is where cards will be constrained
- The outer boundary (yellow area) is the threshold that cards must cross to be released
- When selected, the areas show additional labels for easier identification

### In Runtime

- When a card is dragged into an area, it will be constrained to stay within that area
- When a card is dropped within an area, it will automatically center itself in the area (if the area's centering option is enabled)
- To move a card out of an area, drag it beyond the outer threshold (yellow area) and hold it there briefly
- The card will be released from the area's constraint only after it has been outside the threshold for a specified delay time
- If the card is moved back within the threshold before the delay expires, it will remain constrained to the area
- Cards constrained to an area are highlighted with an orange outline (if debug visualization is enabled)
- The highlight is automatically removed when a card leaves an area

### Scripting

You can create and manage card areas programmatically:

```csharp
// Get reference to the system
CardAreaSystem areaSystem = tableManager.GetSystem<CardAreaSystem>();

// Create a new card area
CardArea newArea = areaSystem.CreateCardArea(new Vector3(0, 0, 0));

// Remove a card area
areaSystem.RemoveCardArea(existingArea);

// Check if a position is within any card area
CardArea area = areaSystem.GetCardAreaAtPosition(position);
```

## Customization

### CardArea Properties

Each CardArea can be customized with the following properties:

- **Unit Space**: The unit space to use for size measurements (Millimeters, Centimeters, Meters, Inches)
- **Inner Size**: The size of the main area (white) where cards are constrained
- **Threshold Distance**: How far beyond the inner boundary a card must be dragged to be released
- **Inner Color**: The color of the main area
- **Outer Color**: The color of the threshold area
- **Border Color**: The color of the border around the inner area
- **Border Width**: The width of the border line
- **Show Visualization**: Whether to show the runtime visualization of the area
- **Center Cards On Drop**: Whether to automatically center cards in this specific area when dropped

The editor will show the converted world units (meters) for reference, making it easier to understand the actual size in the scene.

### CardAreaSystem Settings

The CardAreaSystem can be customized with these settings:

- **Card Area Prefab**: A prefab to use when creating new card areas programmatically
- **Enable Card Areas**: Toggle to enable/disable all card area functionality
- **Show Debug Visualization**: Whether to highlight cards that are constrained to areas
- **Constraint Highlight Color**: The color used to highlight constrained cards
- **Center Cards On Drop**: Default setting for whether to automatically center cards in areas when dropped (each area can override this setting)
- **Centering Animation Duration**: The duration of the animation when centering cards
- **Release Delay**: Delay in seconds before releasing a card from an area after crossing the threshold

## Integration with Other Systems

The CardAreaSystem is designed to work alongside the CardInteractionSystem:

- CardInteractionSystem handles basic selection and movement
- CardAreaSystem handles constraining card movement within areas

The integration is automatic - once both systems are active in the TableManager, cards will be constrained to areas when dragged into them.

## Extending

To extend the CardAreaSystem with additional functionality:

1. Create a new system that inherits from TableSystem
2. Get a reference to the CardAreaSystem using `tableManager.GetSystem<CardAreaSystem>()`
3. Subscribe to the CardInteractionSystem's events to respond to card movements
4. Implement your custom logic to work with card areas

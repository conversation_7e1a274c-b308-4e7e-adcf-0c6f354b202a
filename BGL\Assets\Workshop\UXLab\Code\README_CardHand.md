# Card Hand Component

A Unity component that arranges cards in a hand-like formation with various arrangement options.

## Features

- Arrange cards in a curved or linear formation
- Cards are placed next to each other in a natural hand appearance
- Real-time updates in the custom editor when tweaking settings for a smooth editing experience
- Customize curve radius, arc angle, and vertical curve
- Add random variations with noise settings (Z-axis rotation only) for more natural-looking arrangements
- Control card spacing and scale
- Flip cards face up or face down
- Add and remove cards dynamically

## Usage

1. Add the `CardHand` component to a GameObject
2. Assign a card prefab with `CardLayout` and `CardMeshGenerator` components
3. Adjust the arrangement settings in the Inspector
4. Use the editor buttons to add, remove, or clear cards
5. Experiment with different settings to achieve the desired look

## Settings

### Basic Settings
- **Card Prefab**: The prefab to instantiate for new cards
- **Max Cards**: Maximum number of cards allowed in the hand
- **Arrangement Type**: Choose between Curve or Linear arrangement

### Curve Settings
- **Curve Radius**: Radius of the curve in meters (larger values create a flatter curve)
- **Arc Angle**: Arc angle in degrees (controls how wide the cards are spread)
- **Vertical Curve**: Vertical offset applied to cards based on their position in the curve

### Card Positioning
- **Card Spacing**: Spacing between cards as a multiplier of card width (higher values create more space between cards)
- **Card Scale**: Scale applied to cards in the hand
- **Face Up**: Whether cards should face the player (true) or away (false)

### Noise Settings
- **Use Noise**: Enable random variations in card positions and rotations
- **Rotation Noise**: Maximum random rotation offset in degrees (applied only to Z-axis to prevent cards from clipping)
- **Position Noise**: Maximum random position offset in meters
- **Noise Seed**: Seed for the random number generator (same seed produces the same arrangement)

## Demo Scene

The `CardHandDemo` scene demonstrates the CardHand component with a simple setup:
1. A table surface
2. A CardHand component with default settings
3. A camera positioned to view the hand

To try it out:
1. Open the `CardHandDemo` scene
2. Select the "Card Hand" GameObject
3. Use the buttons in the Inspector to add cards
4. Experiment with different settings to see how they affect the arrangement

## Examples

### Poker Hand
- Arrangement Type: Curve
- Curve Radius: 1.0
- Arc Angle: 30
- Vertical Curve: 0.1
- Card Spacing: 0.7
- Face Up: True

### Messy Hand
- Arrangement Type: Curve
- Curve Radius: 0.8
- Arc Angle: 40
- Vertical Curve: 0.15
- Card Spacing: 0.6
- Use Noise: True
- Rotation Noise: 8
- Position Noise: 0.015

### Spread Cards
- Arrangement Type: Linear
- Card Spacing: 1.0
- Face Up: True

Panel {
    /* Left/Edit Toolbar */
    --icon-select-regular: url("project:/Assets/Workshop/UXLab/Icons/Select-Tool-Icon.png");
    --icon-move-regular: url("project:/Assets/Workshop/UXLab/Icons/Move-Tool-Icon.png");
    --icon-zoom-regular: url("project:/Assets/Workshop/UXLab/Icons/Magnifier-Tool-Icon.png");
    --icon-rotate-regular: url("project:/Assets/Workshop/UXLab/Icons/Rotate-Tool-Icon.png");
    --icon-shapes-tool-regular: url("project:/Assets/Workshop/UXLab/Icons/Shapes-Tool-Icon.png");
    --icon-text-tool-regular: url("project:/Assets/Workshop/UXLab/Icons/Text-Tool-Icon.png");
    --icon-insert-image-tool-regular: url("project:/Assets/Workshop/UXLab/Icons/Insert-Image-Tool-Icon.png");
    --icon-trash-regular: url("project:/Assets/Workshop/UXLab/Icons/Trash-Tool-Icon.png");

    /* Top Toolbar */
    --icon-games-icon-regular: url("project:/Assets/Workshop/UXLab/Icons/Games-Icon.png");
    --icon-components-list-regular: url("project:/Assets/Workshop/UXLab/Icons/Components-List-Icon.png");
    --icon-component-editor-regular: url("project:/Assets/Workshop/UXLab/Icons/Component-Editor-Icon.png");
    --icon-deck-composer-regular: url("project:/Assets/Workshop/UXLab/Icons/Deck-Composer-Icon.png");

    /* View Mode Toolbar */
    --icon-eye-icon-regular: url("project:/Assets/Workshop/UXLab/Icons/View-Eye.png");
    --icon-graphics-icon-regular: url("project:/Assets/Workshop/UXLab/Icons/View-Preview-Wisiwig.png");
    --icon-bounding-boxes-icon-regular: url("project:/Assets/Workshop/UXLab/Icons/View-Block-View.png");
    --icon-view-grid-icon-regular: url("project:/Assets/Workshop/UXLab/Icons/View-Grid.png");
    --icon-view-rulers-icon-regular: url("project:/Assets/Workshop/UXLab/Icons/View-Rulers.png");
}

/* Left/Edit Toolbar */
.appui-icon--Select-Tool-Icon--regular {
    --unity-image: var(--icon-select-regular);
}

.appui-icon--Move-Tool-Icon--regular {
    --unity-image: var(--icon-move-regular);
}

.appui-icon--Magnifier-Tool-Icon--regular {
    --unity-image: var(--icon-zoom-regular);
    padding: 2px;
}

.appui-icon--Rotate-Tool-Icon--regular {
    --unity-image: var(--icon-rotate-regular);
    padding: 1px;
}

.appui-icon--Shapes-Tool-Icon--regular {
    --unity-image: var(--icon-shapes-tool-regular);
}

.appui-icon--Text-Tool-Icon--regular {
    --unity-image: var(--icon-text-tool-regular);
}

.appui-icon--Insert-Image-Icon--regular {
    --unity-image: var(--icon-insert-image-tool-regular);
}

.appui-icon--Trash-Tool-Icon--regular {
    --unity-image: var(--icon-trash-regular);
}

/* View Mode Toolbar */
.appui-icon--eye-icon--regular {
    --unity-image: var(--icon-eye-icon-regular);
}

.appui-icon--graphics-icon--regular {
    --unity-image: var(--icon-graphics-icon-regular);
}

.appui-icon--bounding-boxes-icon--regular {
    --unity-image: var(--icon-bounding-boxes-icon-regular);
}

.appui-icon--view-grid-icon--regular {
    --unity-image: var(--icon-view-grid-icon-regular);
}

.appui-icon--view-rulers-icon--regular {
    --unity-image: var(--icon-view-rulers-icon-regular);
}

/* Top Toolbar */
.appui-icon--games-icon--regular {
    --unity-image: var(--icon-games-icon-regular);
}

.appui-icon--components-list--regular {
    --unity-image: var(--icon-components-list-regular);
}

.appui-icon--component-editor--regular {
    --unity-image: var(--icon-component-editor-regular);
}

.appui-icon--deck-composer--regular {
    --unity-image: var(--icon-deck-composer-regular);
}


<?xml version="1.0" encoding="utf-8"?>
<engine:UXML
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:engine="UnityEngine.UIElements"
    xmlns:editor="UnityEditor.UIElements"
    xmlns:app="Unity.AppUI.UI"
    xsi:noNamespaceSchemaLocation="../../UIElementsSchema/UIElements.xsd">

    <!-- Editing Toolbar Container -->
    <app:ActionGroup direction="Vertical" name="editing-toolbar-group" class="toolbar-container vertical-toolbar editing-toolbar" selection-type="Single">
        <app:ActionButton name="select-tool-button" icon="Select-Tool-Icon" tooltip="Select Tool" preferred-tooltip-placement="Right" />
        <app:ActionButton name="move-tool-button" icon="Move-Tool-Icon" tooltip="Move Tool" preferred-tooltip-placement="Right" />
        <app:ActionButton name="zoom-tool-button" icon="Magnifier-Tool-Icon" tooltip="Zoom Tool" preferred-tooltip-placement="Right" /> <!-- Replace with actual zoom icon if available -->
        <app:ActionButton name="rotate-tool-button" icon="Rotate-Tool-Icon" tooltip="Rotate Tool" preferred-tooltip-placement="Right" />
        <app:ActionButton name="shape-tool-button" icon="Shapes-Tool-Icon" tooltip="Shape Tool" preferred-tooltip-placement="Right" /> <!-- Placeholder icon -->
        <app:ActionButton name="text-tool-button" icon="Text-Tool-Icon" tooltip="Text Tool" preferred-tooltip-placement="Right" />
        <app:ActionButton name="image-tool-button" icon="Insert-Image-Icon" tooltip="Image Tool" preferred-tooltip-placement="Right" />
        <app:ActionButton name="delete-tool-button" icon="Trash-Tool-Icon" tooltip="Delete Tool" preferred-tooltip-placement="Right" />
    </app:ActionGroup>
</engine:UXML> 
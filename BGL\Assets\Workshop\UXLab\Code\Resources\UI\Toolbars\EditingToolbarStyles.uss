/* Editing Toolbar Specific Styles */
.editing-toolbar {
    /* Positioning */
    position: absolute;
    top: 200px; /* Fixed position instead of percentage */
    left: 20px;

    /* Layout & Size - ActionGroup's direction="Vertical" handles flex-direction */
    height: auto; /* Fit content */
    width: 52px; /* Fixed width */

    /* Appearance */
    background-color: rgb(60, 60, 60);
    border-radius: 8px;
    padding: 6px;
    border-width: 1px;
    border-color: rgb(80, 80, 80);
}

/* Style individual buttons */
.editing-toolbar > .appui-actionbutton {
    width: 40px;
    height: 40px;
    margin-bottom: 6px; /* Spacing between buttons */
    margin-right: 0; /* Ensure no horizontal margin */
    background-color: rgb(80, 80, 80);
    border-radius: 6px;
}

/* Remove last-child pseudo-class - not supported in Unity */

.editing-toolbar > .appui-actionbutton:hover {
    background-color: rgb(100, 100, 100);
}

/* Selected state style */
.editing-toolbar > .appui-actionbutton--selected {
    background-color: rgb(120, 180, 60);
    color: white;
}


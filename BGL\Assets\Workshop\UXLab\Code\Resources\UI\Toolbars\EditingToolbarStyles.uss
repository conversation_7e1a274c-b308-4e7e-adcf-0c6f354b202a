/* Editing Toolbar Specific Styles */
.editing-toolbar {
    /* Positioning */
    position: absolute;
    top: 155px; /* Position below the mode toolbar */
    left: 10px;  /* Align with the left edge */
    
    /* Layout & Size - ActionGroup's direction="Vertical" handles flex-direction */
    /*width: 40px;*/
    height: auto; /* Fit content */
    
    /* Appearance */
    background-color: rgba(60, 60, 60, 0.8);
    border-radius: 6px;
    padding: 3px;
}

/* Style individual buttons */
.editing-toolbar > .appui-actionbutton {
    width: 32px;
    height: 32px;
    margin-bottom: 4px; /* Spacing between buttons */
    margin-right: 0; /* Ensure no horizontal margin */
}

.editing-toolbar > .appui-actionbutton:last-child {
    margin-bottom: 0; /* No margin on the last button */
}

/* Selected state style */
.editing-toolbar > .appui-actionbutton--selected {
    background-color: rgba(0, 150, 255, 0.6);
}


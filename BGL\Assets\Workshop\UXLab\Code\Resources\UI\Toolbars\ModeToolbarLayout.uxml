<?xml version="1.0" encoding="utf-8"?>
<engine:UXML
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:engine="UnityEngine.UIElements"
    xmlns:editor="UnityEditor.UIElements"
    xmlns:app="Unity.AppUI.UI"
    xsi:noNamespaceSchemaLocation="../../UIElementsSchema/UIElements.xsd">

    <!-- Mode Toolbar Container -->
    <app:ActionGroup name="mode-toolbar-group" class="toolbar-container horizontal-toolbar mode-toolbar" selection-type="Multiple">
        <!-- ActionButtons no longer need toggle="true" as the group handles selection -->
        <app:ActionButton name="view-mode-button" icon="eye-icon" tooltip="View Mode" />
        <app:ActionButton name="image-mode-button" class="collapsible-action-button" icon="graphics-icon" tooltip="Image Mode" />
        <app:ActionButton name="box-mode-button" class="collapsible-action-button" icon="bounding-boxes-icon" tooltip="Box Mode" />
        <app:ActionButton name="grid-mode-button" class="collapsible-action-button" icon="view-grid-icon" tooltip="Grid Mode" />
        <app:ActionButton name="deck-mode-button" class="collapsible-action-button" icon="view-rulers-icon" tooltip="Deck Mode" />
    </app:ActionGroup>
</engine:UXML> 
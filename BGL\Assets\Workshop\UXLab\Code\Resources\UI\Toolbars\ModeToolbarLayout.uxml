<?xml version="1.0" encoding="utf-8"?>
<engine:UXML
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:engine="UnityEngine.UIElements"
    xmlns:editor="UnityEditor.UIElements"
    xmlns:app="Unity.AppUI.UI"
    xsi:noNamespaceSchemaLocation="../../UIElementsSchema/UIElements.xsd">

    <!-- Mode Toolbar Container -->
    <engine:VisualElement name="mode-toolbar-container" class="mode-toolbar">
        <!-- Primary Mode Toggle -->
        <app:ActionGroup name="primary-mode-group" class="primary-mode-group" selection-type="Single">
            <app:ActionButton name="view-mode-toggle" icon="eye-icon" tooltip="View Mode" class="primary-mode-button" />
            <app:ActionButton name="grid-rulers-mode-toggle" icon="view-grid-icon" tooltip="Grid &amp; Rulers Mode" class="primary-mode-button" />
        </app:ActionGroup>

        <!-- Secondary Tools Container -->
        <engine:VisualElement name="secondary-tools" class="secondary-tools">
            <!-- View Mode Tools (visible when view mode is active) -->
            <app:ActionGroup name="view-mode-tools" class="view-mode-tools secondary-tool-group" selection-type="Multiple">
                <app:ActionButton name="bounding-box-tool" icon="bounding-boxes-icon" tooltip="Bounding Box Selection" />
                <app:ActionButton name="text-tool" icon="Text-Tool-Icon" tooltip="Text Tool" />
                <app:ActionButton name="image-tool" icon="Insert-Image-Icon" tooltip="Image Tool" />
            </app:ActionGroup>

            <!-- Grid/Rulers Mode Tools (visible when grid/rulers mode is active) -->
            <app:ActionGroup name="grid-rulers-tools" class="grid-rulers-tools secondary-tool-group" selection-type="Multiple">
                <app:ActionButton name="rulers-tool" icon="view-rulers-icon" tooltip="Rulers" />
                <app:ActionButton name="grid-tool" icon="view-grid-icon" tooltip="Grid" />
                <app:ActionButton name="measurement-tool" icon="components-list" tooltip="Measurements" />
                <app:ActionButton name="move-tool" icon="Move-Tool-Icon" tooltip="Move Tool" />
            </app:ActionGroup>
        </engine:VisualElement>
    </engine:VisualElement>
</engine:UXML>
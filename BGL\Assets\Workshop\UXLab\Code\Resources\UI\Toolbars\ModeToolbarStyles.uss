/* Mode Toolbar Specific Styles */
.mode-toolbar {
    /* Positioning */
    position: absolute;
    top: 50px;
    left: 20px;

    /* Layout & Size */
    flex-direction: column; /* Vertical stack of mode rows */
    width: auto; /* Auto width to accommodate expanded tools */
    height: auto; /* Auto height to fit content */

    /* Appearance - Shared background for primary buttons */
    background-color: rgb(60, 60, 60);
    border-radius: 8px;
    border-width: 1px;
    border-color: rgb(80, 80, 80);
    padding: 8px;
}

/* Mode Row - Each row contains a primary button + its secondary tools */
.mode-row {
    flex-direction: row; /* Horizontal layout: button + tools */
    align-items: flex-start;
    margin-bottom: 4px; /* Space between rows */
}

/* Primary Mode Button - The main toggle button for each mode */
.primary-mode-button {
    width: 40px;
    height: 40px;
    margin-right: 4px; /* Space between button and tools */
    margin-bottom: 4px; /* Space between buttons vertically */
    background-color: rgb(80, 80, 80);
    border-radius: 6px;
    border-width: 0; /* No border, parent container has border */
    flex-shrink: 0; /* Don't shrink */
}

.primary-mode-button:hover {
    background-color: rgb(100, 100, 100);
}

.primary-mode-button.appui-actionbutton--selected {
    background-color: rgb(120, 180, 60);
    color: white;
}

/* Secondary Tools Container - Holds the expanded tools */
.secondary-tools-container {
    flex-direction: row; /* Horizontal layout for tools */
    align-items: center;
    display: flex; /* TEMPORARILY ALWAYS VISIBLE FOR DEBUGGING */
    background-color: rgba(255, 255, 0, 0.3); /* Yellow debug background */
    min-width: 50px;
    min-height: 50px;
}

/* Secondary Tool Group - The ActionGroup containing the tools */
.secondary-tool-group {
    flex-direction: row; /* Horizontal layout for individual tools */
    background-color: rgb(60, 60, 60);
    border-radius: 8px;
    border-width: 1px;
    border-color: rgb(80, 80, 80);
    padding: 4px;
    align-items: center;
}

/* Show secondary tools when expanded */
.mode-toolbar.view-mode-expanded .view-mode-tools-container {
    display: flex;
}

.mode-toolbar.grid-rulers-expanded .grid-rulers-tools-container {
    display: flex;
}

/* Debug - temporarily show all containers to test */
.view-mode-tools-container {
    background-color: rgba(255, 0, 0, 0.2); /* Red tint for debugging */
}

.grid-rulers-tools-container {
    background-color: rgba(0, 255, 0, 0.2); /* Green tint for debugging */
}

/* Secondary tool buttons */
.secondary-tool-group > .appui-actionbutton {
    width: 40px;
    height: 40px;
    margin-right: 4px; /* Horizontal spacing */
    margin-bottom: 0;
    background-color: rgb(80, 80, 80);
    border-radius: 6px;
}

.secondary-tool-group > .appui-actionbutton:hover {
    background-color: rgb(100, 100, 100);
}

.secondary-tool-group > .appui-actionbutton.appui-actionbutton--selected {
    background-color: rgb(120, 180, 60);
    color: white;
}

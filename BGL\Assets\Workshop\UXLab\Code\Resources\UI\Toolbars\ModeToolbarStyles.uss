/* Mode Toolbar Specific Styles */
.mode-toolbar {
    /* Positioning */
    position: absolute;
    bottom: 20px; /* Position at bottom of screen */
    left: 20px;

    /* Layout & Size */
    flex-direction: row;
    width: auto;
    height: 60px; /* Fixed height */
    min-width: 200px;

    /* Appearance */
    background-color: rgba(60, 60, 60, 0.9);
    border-radius: 8px;
    padding: 6px;
    border: 2px solid rgba(0, 255, 0, 0.8); /* Temporary green border for debugging */
}

/* Primary Mode Group */
.primary-mode-group {
    flex-direction: row;
    margin-right: 8px;
    background-color: rgba(40, 40, 40, 0.8);
    border-radius: 6px;
    padding: 2px;
    height: 48px;
    align-items: center;
}

.primary-mode-button {
    width: 40px;
    height: 40px;
    margin: 2px;
    background-color: rgba(80, 80, 80, 0.6);
    border-radius: 4px;
}

.primary-mode-button:hover {
    background-color: rgba(100, 100, 100, 0.8);
}

.primary-mode-button.appui-actionbutton--selected {
    background-color: rgba(120, 180, 60, 0.9); /* Green highlight for active mode */
    color: white;
}

/* Secondary Tools */
.secondary-tools {
    flex-direction: row;
    height: 48px; /* Match the primary mode group height */
    align-items: center;
}

.secondary-tool-group {
    flex-direction: row;
    background-color: rgba(40, 40, 40, 0.8);
    border-radius: 6px;
    padding: 2px;
    height: 44px;
    align-items: center;
}

/* Hide inactive tool groups by default */
.view-mode-tools {
    display: flex; /* Show view mode tools by default */
}

.grid-rulers-tools {
    display: none; /* Hide grid/rulers tools by default */
}

/* Show/hide based on active mode */
.mode-toolbar.grid-rulers-active .view-mode-tools {
    display: none;
}

.mode-toolbar.grid-rulers-active .grid-rulers-tools {
    display: flex;
}

/* Secondary tool buttons */
.secondary-tool-group > .appui-actionbutton {
    width: 36px;
    height: 36px;
    margin: 2px;
    background-color: rgba(80, 80, 80, 0.6);
    border-radius: 4px;
}

.secondary-tool-group > .appui-actionbutton:hover {
    background-color: rgba(100, 100, 100, 0.8);
}

.secondary-tool-group > .appui-actionbutton.appui-actionbutton--selected {
    background-color: rgba(120, 180, 60, 0.9);
    color: white;
}


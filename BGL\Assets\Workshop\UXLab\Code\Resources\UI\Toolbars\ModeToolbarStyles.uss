/* Mode Toolbar Specific Styles */
.mode-toolbar {
/* Positioning */
    position: absolute;
    top: 50px;
        left: 20px;
    /* Layout & Size */
    flex-direction: column;
        /* Change to vertical layout */
        width: 52px;
        /* Match editing toolbar width */
        height: auto;
        /* Auto height to fit content */
    /* Appearance */
    background-color: rgb(60, 60, 60);
        border-radius: 8px;
        padding: 6px;
        border-width: 2px;
        border-color: rgb(80, 80, 80);
    }
    
    /* Primary Mode Group */
    .primary-mode-group {
        flex-direction: column;
        /* Change to vertical */
        margin-bottom: 8px;
        /* Space below instead of right */
        background-color: rgb(40, 40, 40);
    border-radius: 6px;
    padding: 2px;
        width: 48px;
        /* Fixed width instead of height */
        align-items: center;
}

        .primary-mode-button {
            width: 40px;
            height: 40px;
            margin-bottom: 4px;
            /* Vertical spacing */
            margin-left: 0;
            margin-right: 0;
            background-color: rgb(80, 80, 80);
            border-radius: 4px;
        }
    
        .primary-mode-button:hover {
            background-color: rgb(100, 100, 100);
        }
    
        .primary-mode-button.appui-actionbutton--selected {
            background-color: rgb(120, 180, 60);
            /* Green highlight for active mode */
            color: white;
        }
    
        /* Secondary Tools */
        .secondary-tools {
            flex-direction: column;
            /* Change to vertical */
            width: 48px;
            /* Match primary mode group width */
            align-items: center;
}

.secondary-tool-group {
    flex-direction: column;
    /* Change to vertical */
    background-color: rgb(40, 40, 40);
    border-radius: 6px;
    padding: 2px;
    width: 44px;
    /* Fixed width instead of height */
    align-items: center;
    margin-bottom: 4px;
    /* Space between tool groups */
}

/* Collapsible tool groups - hidden by default */
.view-mode-tools {
    display: none;
    /* Hidden by default, shown when view mode is active and expanded */
}

.grid-rulers-tools {
    display: none;
    /* Hidden by default, shown when grid/rulers mode is active and expanded */
}

/* Show tools when mode is active and expanded */
.mode-toolbar.view-mode-active.view-mode-expanded .view-mode-tools {
    display: flex;
}

.mode-toolbar.grid-rulers-active.grid-rulers-expanded .grid-rulers-tools {
    display: flex;
}

/* Secondary tool buttons */
.secondary-tool-group>.appui-actionbutton {
    width: 36px;
    height: 36px;
    margin-bottom: 2px;
    /* Vertical spacing */
    margin-left: 0;
    margin-right: 0;
    background-color: rgb(80, 80, 80);
    border-radius: 4px;
}

.secondary-tool-group>.appui-actionbutton:hover {
    background-color: rgb(100, 100, 100);
}

.secondary-tool-group>.appui-actionbutton.appui-actionbutton--selected {
    background-color: rgb(120, 180, 60);
    color: white;
}

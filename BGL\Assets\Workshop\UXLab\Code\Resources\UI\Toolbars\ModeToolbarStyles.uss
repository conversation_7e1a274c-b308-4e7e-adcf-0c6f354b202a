/* Mode Toolbar Specific Styles */
.mode-toolbar {
    /* Positioning */
    position: absolute;
    top: 500px; /* Temporary fixed position for testing */
    left: 20px;

    /* Layout & Size */
    flex-direction: row;
    width: auto;
    height: 60px; /* Fixed height */
    min-width: 200px;

    /* Appearance */
    background-color: rgb(60, 60, 60);
    border-radius: 8px;
    padding: 6px;
    border-width: 2px;
    border-color: rgb(80, 80, 80);
}

/* Primary Mode Group */
.primary-mode-group {
    flex-direction: row;
    margin-right: 8px;
    background-color: rgb(40, 40, 40);
    border-radius: 6px;
    padding: 2px;
    height: 48px;
    align-items: center;
}

.primary-mode-button {
    width: 40px;
    height: 40px;
    margin: 2px;
    background-color: rgb(80, 80, 80);
    border-radius: 4px;
}

.primary-mode-button:hover {
    background-color: rgb(100, 100, 100);
}

.primary-mode-button.appui-actionbutton--selected {
    background-color: rgb(120, 180, 60); /* Green highlight for active mode */
    color: white;
}

/* Secondary Tools */
.secondary-tools {
    flex-direction: row;
    height: 48px; /* Match the primary mode group height */
    align-items: center;
}

.secondary-tool-group {
    flex-direction: row;
    background-color: rgb(40, 40, 40);
    border-radius: 6px;
    padding: 2px;
    height: 44px;
    align-items: center;
}

/* Hide inactive tool groups by default */
.view-mode-tools {
    display: flex; /* Show view mode tools by default */
}

.grid-rulers-tools {
    display: none; /* Hide grid/rulers tools by default */
}

/* Show/hide based on active mode */
.mode-toolbar.grid-rulers-active .view-mode-tools {
    display: none;
}

.mode-toolbar.grid-rulers-active .grid-rulers-tools {
    display: flex;
}

/* Secondary tool buttons */
.secondary-tool-group > .appui-actionbutton {
    width: 36px;
    height: 36px;
    margin: 2px;
    background-color: rgb(80, 80, 80);
    border-radius: 4px;
}

.secondary-tool-group > .appui-actionbutton:hover {
    background-color: rgb(100, 100, 100);
}

.secondary-tool-group > .appui-actionbutton.appui-actionbutton--selected {
    background-color: rgb(120, 180, 60);
    color: white;
}


/* Mode Toolbar Specific Styles */
.mode-toolbar {
    /* Positioning */
    position: absolute;
    top: 105px; /* Position below the top toolbar */
    left: 10px; /* Shifted right (Prev: 10px) */
    
    /* Layout & Size */
    flex-direction: row; /* Horizontal layout */
    width: auto; /* Fit content */
    height: 40px; 
    
    /* Appearance */
    background-color: rgba(60, 60, 60, 0.8); /* Slightly lighter than top toolbar */
    border-radius: 6px;
    padding: 3px;
}

/* Style individual buttons */
.mode-toolbar > .appui-actionbutton {
    width: 32px;
    height: 32px;
    margin-right: 4px; /* Spacing between buttons */
}

.mode-toolbar > .appui-actionbutton:last-child {
    margin-right: 0; /* No margin on the last button */
}

/* Selected state style (AppUI should add .appui-actionbutton--selected) */
.mode-toolbar > .appui-actionbutton--selected {
    background-color: rgba(0, 150, 255, 0.6); /* Example selection color */
    /* Add other styles like border if needed */
}

.collapsible-action-button {
    display: none;
}

.mode-toolbar.expanded .collapsible-action-button {
    display: flex;
}


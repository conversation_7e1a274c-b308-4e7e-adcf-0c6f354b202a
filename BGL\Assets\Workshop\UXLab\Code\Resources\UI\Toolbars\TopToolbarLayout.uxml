<?xml version="1.0" encoding="utf-8"?>
<engine:UXML
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:engine="UnityEngine.UIElements"
    xmlns:editor="UnityEditor.UIElements"
    xmlns:app="Unity.AppUI.UI"
    xsi:noNamespaceSchemaLocation="../../UIElementsSchema/UIElements.xsd">

    <!-- Right Side Panel Container -->
    <engine:VisualElement name="right-panel-container" class="right-panel">
        <!-- Tab Header -->
        <engine:VisualElement name="tab-header" class="tab-header">
            <app:ActionGroup name="tab-group" class="tab-group" selection-type="Single">
                <app:ActionButton name="faces-tab" text="Faces" class="tab-button" />
                <app:ActionButton name="templates-tab" text="Templates" class="tab-button" />
                <app:ActionButton name="backs-tab" text="Backs" class="tab-button" />
            </app:ActionGroup>
        </engine:VisualElement>

        <!-- Tab Content Area -->
        <engine:VisualElement name="tab-content" class="tab-content">
            <!-- Faces Tab Content -->
            <engine:VisualElement name="faces-content" class="tab-panel active">
                <engine:Label text="Pip" class="category-label" />
                <engine:VisualElement class="card-grid">
                    <engine:VisualElement class="card-thumbnail" />
                    <engine:VisualElement class="card-thumbnail" />
                    <engine:VisualElement class="card-thumbnail" />
                    <engine:VisualElement class="card-thumbnail" />
                </engine:VisualElement>
            </engine:VisualElement>

            <!-- Templates Tab Content -->
            <engine:VisualElement name="templates-content" class="tab-panel">
                <engine:Label text="Poker" class="category-label" />
                <engine:Label text="Face / Ace" class="subcategory-label" />
                <engine:VisualElement class="card-grid">
                    <engine:VisualElement class="card-thumbnail large" />
                    <engine:VisualElement class="card-thumbnail large" />
                </engine:VisualElement>
                <engine:VisualElement class="card-grid">
                    <engine:VisualElement class="card-thumbnail" />
                    <engine:VisualElement class="card-thumbnail" />
                    <engine:VisualElement class="card-thumbnail" />
                </engine:VisualElement>
            </engine:VisualElement>

            <!-- Backs Tab Content -->
            <engine:VisualElement name="backs-content" class="tab-panel">
                <engine:Label text="Joker" class="category-label" />
                <engine:VisualElement class="card-grid">
                    <engine:VisualElement class="card-thumbnail large" />
                </engine:VisualElement>
                <engine:VisualElement class="card-grid">
                    <engine:VisualElement class="card-thumbnail" />
                    <engine:VisualElement class="card-thumbnail" />
                    <engine:VisualElement class="card-thumbnail" />
                </engine:VisualElement>

                <engine:Label text="Trading Cards Game" class="category-label" />
                <engine:Label text="M Games" class="subcategory-label" />
                <engine:Label text="P Games" class="subcategory-label" />
                <engine:Label text="A Games" class="subcategory-label" />
                <engine:VisualElement class="card-grid">
                    <engine:VisualElement class="card-thumbnail large" />
                    <engine:VisualElement class="card-thumbnail large" />
                    <engine:VisualElement class="card-thumbnail large" />
                </engine:VisualElement>
                <engine:VisualElement class="card-grid">
                    <engine:VisualElement class="card-thumbnail" />
                    <engine:VisualElement class="card-thumbnail" />
                    <engine:VisualElement class="card-thumbnail" />
                </engine:VisualElement>
            </engine:VisualElement>
        </engine:VisualElement>
    </engine:VisualElement>
</engine:UXML>
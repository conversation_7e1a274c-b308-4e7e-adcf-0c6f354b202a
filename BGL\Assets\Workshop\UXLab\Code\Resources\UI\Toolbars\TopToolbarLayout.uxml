<?xml version="1.0" encoding="utf-8"?>
<engine:UXML
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:engine="UnityEngine.UIElements"
    xmlns:editor="UnityEditor.UIElements"
    xmlns:app="Unity.AppUI.UI"
    xsi:noNamespaceSchemaLocation="../../UIElementsSchema/UIElements.xsd">

    <!-- Top Toolbar Container -->
    <engine:VisualElement name="top-toolbar-container" class="toolbar-container horizontal-toolbar top-toolbar">
        <!-- Left Side: Logo Area -->
        <engine:VisualElement name="logo-area" class="logo-area">
            <engine:VisualElement class="logo-container">
                <engine:Image name="logo-main" class="logo-main"/>
                <!-- Center: Game Name -->
                <engine:Label text="My Super Game V1.2" name="game-name-label" class="game-name-label" />
            </engine:VisualElement>
            <engine:Image name="logo-cog" class="logo-cog" />
        </engine:VisualElement>

        
         
        <!-- Right Side: Action Buttons -->
        <engine:VisualElement name="action-buttons" class="action-buttons">
            <app:ActionButton name="cards-button" icon="games-icon" tooltip="Cards" />
            <app:ActionButton name="grid-button" icon="components-list" tooltip="Grid View" />
            <app:ActionButton name="component-editor-button" icon="component-editor" tooltip="Component Editor" />
            <app:ActionButton name="deck-composer-button" icon="deck-composer" tooltip="Deck Composer" />
        </engine:VisualElement>
    </engine:VisualElement>
</engine:UXML> 
/* Right Panel Styles */
.right-panel {
    /* Positioning */
    position: absolute;
    top: 10px;
    right: 10px;
    width: 280px;
    height: 600px; /* Fixed height instead of calc() */
    min-height: 400px;

    /* Layout */
    flex-direction: column;

    /* Appearance */
    background-color: rgba(240, 240, 240, 0.95);
    border-radius: 8px;
    border: 2px solid rgba(255, 0, 0, 0.8); /* Temporary red border for debugging */
    overflow: hidden;
}

/* Tab Header */
.tab-header {
    flex-direction: row;
    background-color: rgba(220, 220, 220, 1);
    border-bottom: 1px solid rgba(200, 200, 200, 0.8);
}

.tab-group {
    flex-direction: row;
    width: 100%;
}

.tab-button {
    flex: 1;
    height: 40px;
    background-color: transparent;
    border: none;
    border-radius: 0;
    color: rgb(80, 80, 80);
    font-size: 14px;
    -unity-font-style: normal;
}

.tab-button:hover {
    background-color: rgba(200, 200, 200, 0.5);
}

.tab-button.appui-actionbutton--selected {
    background-color: rgba(240, 240, 240, 1);
    color: rgb(40, 40, 40);
    -unity-font-style: bold;
}

/* Tab Content */
.tab-content {
    flex: 1;
    padding: 15px;
    overflow: auto; /* Allow scrolling if content is too large */
    height: auto;
    min-height: 300px;
}

.tab-panel {
    display: none;
    flex-direction: column;
}

.tab-panel.active {
    display: flex;
}

/* Category Labels */
.category-label {
    font-size: 16px;
    -unity-font-style: bold;
    color: rgb(60, 60, 60);
    margin-bottom: 10px;
    margin-top: 15px;
}

.category-label:first-child {
    margin-top: 0;
}

.subcategory-label {
    font-size: 12px;
    color: rgb(100, 100, 100);
    margin-bottom: 8px;
    margin-left: 10px;
}

/* Card Grid */
.card-grid {
    flex-direction: row;
    flex-wrap: wrap;
    margin-bottom: 15px;
    justify-content: flex-start;
}

/* Card Thumbnails */
.card-thumbnail {
    width: 50px;
    height: 70px;
    background-color: rgb(255, 255, 255);
    border: 1px solid rgba(180, 180, 180, 0.8);
    border-radius: 4px;
    margin: 2px;
    cursor: pointer;
}

.card-thumbnail.large {
    width: 80px;
    height: 112px;
}

.card-thumbnail:hover {
    border-color: rgba(100, 150, 255, 0.8);
    background-color: rgba(240, 245, 255, 1);
}

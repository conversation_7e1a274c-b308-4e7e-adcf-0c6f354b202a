/* Top Toolbar Specific Styles */
.top-toolbar {
    /* Positioning */
    position: absolute;
    top: 0px;
    left: 10px; /* Start near the left edge */
    width: auto; /* Let flexbox handle width */

    /* Layout & Alignment */
    flex-direction: row;
    align-items: center; /* Vertically center items */
    /*justify-content: space-between;*/ /* Push logo left, buttons right */

    /* Appearance */
    /*background-color: rgba(40, 40, 40, 0.85);*/ /* Dark semi-transparent background */
    border-radius: 8px;
    padding: 5px 0px; /* Vertical padding, Horizontal padding */
}

/* Logo Area */
.top-toolbar .logo-area {
    flex-direction: row;
    align-items: center;
    margin-right: 20px; /* Add some space between logo and buttons */
}

.top-toolbar .logo-main {
    --unity-image: url("/Assets/Workshop/UXLab/Icons/LotG-BGL-Logo-Menu.png");
}

.top-toolbar .logo-cog {
    --unity-image: url("/Assets/Workshop/UXLab/Icons/LotG-BGL-Logo-HalfCog.png");
    -unity-background-scale-mode: scale-to-fit;
    margin-left: 0px;
    top: 9px;
}

.logo-container {
    justify-content: center;
}

/* Game Name Label */
.top-toolbar .game-name-label {
    color: rgb(200, 200, 200);
    font-size: 16px;
    -unity-font-style: bold;
    margin-left: 32px;
    position: absolute;
    right: 20px;
}

/* Action Buttons Area */
.top-toolbar .action-buttons {
    flex-direction: row;

    /*background-color: rgba(60, 60, 60, 0.8);
    border-radius: 6px;
    padding: 4px;*/
    top: 9px;
}
/* End of previously inline styles */

.top-toolbar > .action-buttons > .appui-actionbutton {
    width: 32px;
    height: 32px;
    margin: 2px;
}

.top-toolbar .appui-actionbutton--selected {
    background-color: rgba(0, 150, 255, 0.6); /* Example selection color */
    /* Add other styles like border if needed */
}

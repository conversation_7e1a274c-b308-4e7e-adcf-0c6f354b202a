using UnityEngine;
using UnityEngine.Serialization;
using Workshop.UXLab.Data;

namespace Workshop.UXLab
{
    /// <summary>
    /// Controls the table appearance and cards placed on it (now oriented for XY plane interaction)
    /// </summary>
    public class TableController : MonoBehaviour
    {
        [Header("Table Settings")]
        [SerializeField] private Vector2 m_TableSize = new Vector2(10f, 10f); // Now represents X and Y dimensions
        [SerializeField] private Material m_TableMaterial;
        [SerializeField] private Color m_TableColor = new Color(0.2f, 0.4f, 0.2f);
        [SerializeField] private float m_TableThickness = 0.1f; // Thickness along Z

        [Header("Card Settings")]
        [SerializeField] private GameObject m_CardPrefab;
        [SerializeField] private Material m_CardMaterial;
        [SerializeField] private Vector2 m_CardSize = new Vector2(0.063f, 0.088f); // Standard poker card size (X, Y) in meters
        [SerializeField] private float m_CardThickness = 0.003f; // Thickness along Z
        [SerializeField] private float m_CornerRadius = 0.003f;
        [SerializeField] private bool m_CreateCardOnStart = true;

        [Header("Card Layout Settings")]
        [SerializeField] private int m_NumberOfCards = 1;
        [SerializeField] private int m_MaxCardsPerRow = 5;
        [SerializeField] private Vector2 m_CardMargins = new Vector2(0.02f, 0.02f); // Margins in X and Y

        [Header("Management")]
        [SerializeField] private TableManager m_TableManager;
        [SerializeField] private CameraController m_CameraController;

        private GameObject m_TableObject;
        private GameObject m_CurrentCard;

        // Interaction Plane Z - Should match CameraController's m_InteractionPlaneZ
        // Consider making this configurable or syncing it. For now, assume 0.
        private const float kInteractionPlaneZ = 0f;

        private void Awake()
        {
            // Clean up the TableManager at the start of the session
            if (m_TableManager != null)
            {
                m_TableManager.ClearComponents();
            }
        }

        private void Start()
        {
            SetupTable();
            SetupTableManager();

            if (m_CreateCardOnStart)
            {
                if (m_NumberOfCards <= 1)
                {
                    CreateCard();
                }
                else
                {
                    CreateMultipleCards();
                }
            }
        }

        private void OnDestroy()
        {
            // Clean up the TableManager when this controller is destroyed
            CleanupTableManager();
        }

        private void OnApplicationQuit()
        {
            // Clean up the TableManager when the application quits
            CleanupTableManager();
        }

        /// <summary>
        /// Cleans up the TableManager by removing all components
        /// </summary>
        private void CleanupTableManager()
        {
            if (m_TableManager != null)
            {
                m_TableManager.Shutdown();
                m_TableManager.ClearComponents();
                m_TableManager.SetCameraController(null);
            }
        }

        /// <summary>
        /// Sets up the TableManager with required references
        /// </summary>
        private void SetupTableManager()
        {
            if (m_TableManager != null)
            {
                // Find camera controller if not assigned
                if (m_CameraController == null)
                {
                    m_CameraController = FindObjectOfType<CameraController>();
                }

                if (m_CameraController != null)
                {
                    m_TableManager.SetCameraController(m_CameraController);
                }

                // Register table as a component (ensure it has a collider if needed for interaction)
                if (m_TableObject != null && m_TableObject.GetComponent<Collider>() == null)
                {
                    // Add a BoxCollider matching the table dimensions
                    BoxCollider tableCollider = m_TableObject.AddComponent<BoxCollider>();
                    tableCollider.size = new Vector3(m_TableSize.x, m_TableSize.y, m_TableThickness);
                }
                m_TableManager.AddComponent(m_TableObject);

                // Initialize the TableManager with the new Awaitable-based system
                m_TableManager.Init();
            }
        }

        /// <summary>
        /// Creates the table mesh and material, oriented on the XY plane
        /// </summary>
        private void SetupTable()
        {
            // Create table object if it doesn't exist
            if (m_TableObject == null)
            {
                m_TableObject = new GameObject("Table");
                m_TableObject.transform.SetParent(transform);
                // Position the center of the table at the interaction plane Z
                m_TableObject.transform.localPosition = new Vector3(0, 0, kInteractionPlaneZ);
                // No rotation needed if parent is aligned with world axes
            }

            // Create mesh filter and renderer if they don't exist
            MeshFilter meshFilter = m_TableObject.GetComponent<MeshFilter>();
            if (meshFilter == null)
            {
                meshFilter = m_TableObject.AddComponent<MeshFilter>();
            }

            MeshRenderer meshRenderer = m_TableObject.GetComponent<MeshRenderer>();
            if (meshRenderer == null)
            {
                meshRenderer = m_TableObject.AddComponent<MeshRenderer>();
            }

            // Create a simple box mesh for the table (XY plane surface, Z thickness)
            Mesh tableMesh = new Mesh();
            tableMesh.name = "TableMesh";

            float halfWidth = m_TableSize.x * 0.5f; // X dimension
            float halfHeight = m_TableSize.y * 0.5f; // Y dimension
            float halfThickness = m_TableThickness * 0.5f; // Z dimension

            // Vertices (8 corners of a box aligned with XY plane)
            Vector3[] vertices = new Vector3[8]
            {
                new Vector3(-halfWidth, -halfHeight, -halfThickness), // 0: Bottom-Back-Left
                new Vector3( halfWidth, -halfHeight, -halfThickness), // 1: Bottom-Back-Right
                new Vector3( halfWidth,  halfHeight, -halfThickness), // 2: Top-Back-Right
                new Vector3(-halfWidth,  halfHeight, -halfThickness), // 3: Top-Back-Left
                new Vector3(-halfWidth, -halfHeight,  halfThickness), // 4: Bottom-Front-Left
                new Vector3( halfWidth, -halfHeight,  halfThickness), // 5: Bottom-Front-Right
                new Vector3( halfWidth,  halfHeight,  halfThickness), // 6: Top-Front-Right
                new Vector3(-halfWidth,  halfHeight,  halfThickness)  // 7: Top-Front-Left
            };

            // Triangles - 6 faces, 2 triangles each = 36 indices
            int[] triangles = new int[36]
            {
                // Front face (+Z)
                7, 6, 5,   7, 5, 4,
                // Back face (-Z)
                2, 3, 0,   2, 0, 1,
                // Top face (+Y)
                3, 7, 6,   3, 6, 2,
                // Bottom face (-Y)
                4, 5, 1,   4, 1, 0,
                // Right face (+X)
                5, 6, 2,   5, 2, 1,
                // Left face (-X)
                3, 4, 7,   3, 0, 4 // Corrected winding order
            };

            // UVs (Simple planar mapping for front face - adjust if needed)
            Vector2[] uvs = new Vector2[vertices.Length];
             // Assign UVs based on XY coordinates for front face (others can be simple)
            for (int i = 0; i < vertices.Length; i++)
            {
                 // Simple unwrap for basic texturing - more complex mapping might be needed
                 if (i < 4) // Back face
                     uvs[i] = new Vector2(vertices[i].x / m_TableSize.x + 0.5f, vertices[i].y / m_TableSize.y + 0.5f);
                 else // Front face
                     uvs[i] = new Vector2(vertices[i].x / m_TableSize.x + 0.5f, vertices[i].y / m_TableSize.y + 0.5f);
                 // Could refine this for side/top/bottom faces if detailed textures are used
            }

            tableMesh.vertices = vertices;
            tableMesh.triangles = triangles;
            tableMesh.uv = uvs; // Apply UVs
            tableMesh.RecalculateNormals();
            tableMesh.RecalculateBounds(); // Good practice

            meshFilter.sharedMesh = tableMesh;

            // Setup material
            if (m_TableMaterial != null)
            {
                meshRenderer.sharedMaterial = m_TableMaterial;
            }
            else
            {
                // Create a new material if none provided
                Material material = new Material(Shader.Find("Standard")); // Use URP/Lit if in URP
                material.name = "TableMaterial";
                material.color = m_TableColor;
                meshRenderer.sharedMaterial = material;
            }

            // Make sure the table casts and receives shadows
            meshRenderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.On;
            meshRenderer.receiveShadows = true;
        }

        /// <summary>
        /// Creates multiple cards arranged in rows/columns on the XY plane
        /// </summary>
        private void CreateMultipleCards()
        {
            // Calculate elevation: Table surface is at kInteractionPlaneZ. Cards sit slightly in front.
            float cardElevationZ = kInteractionPlaneZ + m_TableThickness * 0.5f + m_CardThickness * 0.5f;

            int numCols = Mathf.Min(m_NumberOfCards, m_MaxCardsPerRow);
            int numRows = Mathf.CeilToInt((float)m_NumberOfCards / numCols);

            // Calculate starting X based on number of columns
            float startX = -((numCols - 1) * (m_CardSize.x + m_CardMargins.x)) / 2f;
            // Calculate starting Y based on number of rows
            float startY = ((numRows - 1) * (m_CardSize.y + m_CardMargins.y)) / 2f; // Positive Y is typically "up"

            for (int i = 0; i < m_NumberOfCards; i++)
            {
                int row = i / numCols;
                int col = i % numCols;

                float xPos = startX + col * (m_CardSize.x + m_CardMargins.x);
                // Subtract row offset because positive Y is up, lower rows have higher indices
                float yPos = startY - row * (m_CardSize.y + m_CardMargins.y);

                GameObject card = CreateCardAt(new Vector3(xPos, yPos, -cardElevationZ));
                card.name = $"Card_{i + 1}";
                card.SetActive(true);
            }
        }

        /// <summary>
        /// Creates a card at the specified position (X, Y, Z)
        /// </summary>
        private GameObject CreateCardAt(Vector3 position)
        {
            GameObject cardObject = CreateCardObject();
            cardObject.transform.localPosition = position; // Position includes the Z elevation

            // Register card with the table manager
            if (m_TableManager != null)
            {
                m_TableManager.AddComponent(cardObject);
            }

            return cardObject;
        }

        /// <summary>
        /// Creates a single card centered on the table (XY plane)
        /// </summary>
        public GameObject CreateCard()
        {
            GameObject cardObject = CreateCardObject();

            // Position the card slightly in front of the table surface
            float cardElevationZ = kInteractionPlaneZ + m_TableThickness * 0.5f + m_CardThickness * 0.5f;
            cardObject.transform.localPosition = new Vector3(0, 0, -cardElevationZ);

            m_CurrentCard = cardObject;

            // Register card with the table manager
            if (m_TableManager != null)
            {
                m_TableManager.AddComponent(cardObject);
            }

            return cardObject;
        }

        /// <summary>
        /// Creates a card GameObject without positioning it, ensuring it faces forward (along +Z)
        /// </summary>
        private GameObject CreateCardObject()
        {
            GameObject cardObject;
            // Declare dataToApply here to be accessible in both branches and for collider logic
            CardSizeData dataToApply = null;

            if (m_CardPrefab != null)
            {
              m_CardPrefab.SetActive(false);
                cardObject = Instantiate(m_CardPrefab, transform);
                if (cardObject.TryGetComponent<CardMeshGenerator>(out CardMeshGenerator cardMeshGenerator))
                {
                    // --- Apply Size Data from Prefab or Default ---
                    CardSizeData editorData = cardMeshGenerator.EditorSizeData;

                    // Check if editor data from prefab is valid
                    if (editorData != null && editorData.Width > 0 && editorData.Height > 0)
                    {
                        // Clone the editor data to avoid modifying prefab data
                        dataToApply = editorData.Clone(); // Assign to the outer scope variable
                        Debug.Log($"Applying size data from prefab: {dataToApply.Width}mm (X) x {dataToApply.Height}mm (Y)", cardObject);
                    }
                    else
                    {
                        // Editor data is invalid, create default Poker size
                        dataToApply = CardSizeData.CreatePokerSize(); // Assign to the outer scope variable
                        Debug.Log("Prefab data invalid, applying default Poker size.", cardObject);
                    }

                    // Apply the determined data
                    cardMeshGenerator.ApplySizeData(dataToApply);
                }
                else
                {
                    Debug.LogWarning("Card prefab is missing CardMeshGenerator component. Cannot determine size for collider automatically.", cardObject);
                    // dataToApply remains null - collider might not be added or might use default if one already exists
                }

                 // Collider adding logic is moved after the if/else block
            }
            else // Creating card from scratch
            {
                cardObject = new GameObject("Card");
                cardObject.SetActive(false);
                cardObject.transform.SetParent(transform);

                // Add mesh renderer and material
                MeshRenderer meshRenderer = cardObject.AddComponent<MeshRenderer>();
                if (m_CardMaterial != null)
                {
                    meshRenderer.sharedMaterial = m_CardMaterial;
                }
                else
                {
                    Material cardMaterial = new Material(Shader.Find("Standard")); // Use URP/Lit if in URP
                    cardMaterial.color = Color.white;
                    meshRenderer.sharedMaterial = cardMaterial;
                }

                // Configure shadow casting and receiving
                meshRenderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.On;
                meshRenderer.receiveShadows = true;

                // Add card mesh generator
                CardMeshGenerator cardMeshGenerator = cardObject.AddComponent<CardMeshGenerator>();
                // Create a CardSizeData instance with our dimensions (X, Y, Z)
                // Use a local variable first for clarity
                CardSizeData sizeData = new CardSizeData(
                    m_CardSize.x * 1000f,     // Width (X) in mm
                    m_CardSize.y * 1000f,     // Height (Y) in mm
                    m_CardThickness * 1000f,  // Thickness (Z) in mm
                    m_CornerRadius * 1000f    // Corner Radius in mm
                );
                cardMeshGenerator.ApplySizeData(sizeData);
                // Assign the created size data to the outer scope variable for collider logic
                dataToApply = sizeData;
            }

            // --- Common Logic: Add BoxCollider if needed and possible ---
            if (cardObject.GetComponent<Collider>() == null)
            {
                if (dataToApply != null)
                {
                    BoxCollider cardCollider = cardObject.AddComponent<BoxCollider>();
                    cardCollider.size = new Vector3(
                        UnitConverter.MmToMeters(dataToApply.Width),
                        UnitConverter.MmToMeters(dataToApply.Height),
                        UnitConverter.MmToMeters(dataToApply.Thickness)
                    );
                    Debug.Log($"Added BoxCollider with size based on CardSizeData.", cardObject);
                }
                else
                {
                     Debug.LogWarning($"Could not add BoxCollider to {cardObject.name} because size data was not available (e.g., prefab missing CardMeshGenerator).");
                }
            }

            // No rotation needed, card mesh generator should create it facing +Z by default
            // cardObject.transform.localRotation = Quaternion.Euler(90f, 0f, 0f); // REMOVED
            return cardObject;
        }
    }
}

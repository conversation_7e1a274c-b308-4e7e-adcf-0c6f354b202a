using System.Collections.Generic;
using UnityEngine;
using System.Threading;
using UnityEngine.UIElements;
using UnityEngine.EventSystems;
using System;
using Workshop.UXLab.Data; // Added for CardDefinition
using Workshop.UXLab.Utils; // Added for ComponentUtils

namespace Workshop.UXLab
{
    [CreateAssetMenu(fileName = "TableManager", menuName = "Workshop/UXLab/TableManager")]
    public class TableManager : ScriptableObject
    {
        [Header("References")]
        [SerializeField] private CameraController m_CameraController;
        [SerializeField] private UnityEngine.UIElements.PanelSettings m_UIPanelSettings;

        [Header("Table Components")]
        [SerializeField] private List<GameObject> m_TableComponents = new List<GameObject>();

        [Header("Systems")]
        [SerializeField] private List<TableSystem> m_TableSystems = new List<TableSystem>();

        // Static instance for easy access
        public static TableManager Instance { get; private set; }

        // Internal references for Awaitable operation
        private GameObject m_CurrentHoveredComponent;
        private bool m_IsInitialized;

        // Manager component for interactions.
        private TableManagerComponent m_InteractionManagerInstance;

        // Events
        public delegate void ComponentHoverHandler(GameObject component);
        public event ComponentHoverHandler OnComponentHovered;

        public delegate void ComponentClickHandler(GameObject component);
        public event ComponentClickHandler OnComponentClicked;

        public delegate void BlankClickHandler();
        public event BlankClickHandler OnBlankClicked;

        /// <summary>
        /// Event invoked every frame by the TableManagerComponent's Update loop.
        /// Systems can subscribe to this for per-frame logic.
        /// </summary>
        public event Action OnUpdate;

        // Properties
        public CameraController CameraController => m_CameraController;
        public List<GameObject> TableComponents => m_TableComponents;
        public GameObject CurrentHoveredComponent => m_CurrentHoveredComponent;
        public List<TableSystem> TableSystems => m_TableSystems;
        public UnityEngine.UIElements.PanelSettings UIPanelSettings => m_UIPanelSettings;

        private void OnEnable()
        {
            // ScriptableObjects persist data between sessions, so we should
            // make sure lists are clean when loaded into memory
            ValidateComponents();
        }

        // Methods
        public void SetCameraController(CameraController cameraController)
        {
            m_CameraController = cameraController;
        }

        public void AddComponent(GameObject component)
        {
            if (component != null && !m_TableComponents.Contains(component))
            {
                m_TableComponents.Add(component);
            }
        }

        public void RemoveComponent(GameObject component)
        {
            if (component != null && m_TableComponents.Contains(component))
            {
                m_TableComponents.Remove(component);
            }
        }

        public void ClearComponents()
        {
            m_TableComponents.Clear();
        }

        public void AddSystem(TableSystem system)
        {
            if (system != null && !m_TableSystems.Contains(system))
            {
                m_TableSystems.Add(system);

                // If we're already initialized, initialize the new system
                if (m_IsInitialized)
                {
                    system.Init(this);
                }
            }
        }

        public void RemoveSystem(TableSystem system)
        {
            if (system != null && m_TableSystems.Contains(system))
            {
                // Shut down the system if we're initialized
                if (m_IsInitialized)
                {
                    system.Shutdown();
                }

                m_TableSystems.Remove(system);
            }
        }

        /// <summary>
        /// Retrieves the first initialized system of the specified type.
        /// </summary>
        /// <typeparam name="T">The type of TableSystem to retrieve.</typeparam>
        /// <returns>The system instance, or null if not found or not initialized.</returns>
        public T GetSystem<T>() where T : TableSystem
        {
            if (m_TableSystems == null)
            {
                Debug.LogError($"TableManager cannot GetSystem<{typeof(T).Name}>: Systems list is null.");
                return null;
            }

            foreach (var system in m_TableSystems)
            {
                if (system is T typedSystem)
                {
                    return typedSystem;
                }
            }
            return null; // Not found
        }

        /// <summary>
        /// Validates the component list by removing null references
        /// Helpful when debugging to avoid serialized nulls
        /// </summary>
        public void ValidateComponents()
        {
            if (m_TableComponents == null)
            {
                m_TableComponents = new List<GameObject>();
                return;
            }

            // Remove any null references that might have been serialized
            for (int i = m_TableComponents.Count - 1; i >= 0; i--)
            {
                if (m_TableComponents[i] == null)
                {
                    m_TableComponents.RemoveAt(i);
                }
            }

            // Validate systems list
            if (m_TableSystems == null)
            {
                m_TableSystems = new List<TableSystem>();
            }

            // Remove any null references from the systems list
            for (int i = m_TableSystems.Count - 1; i >= 0; i--)
            {
                if (m_TableSystems[i] == null)
                {
                    m_TableSystems.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// Resets the ScriptableObject to its default state
        /// Can be called manually from the Inspector via context menu
        /// </summary>
        [ContextMenu("Reset Manager")]
        public void Reset()
        {
            ClearComponents();
            m_CameraController = null;
            m_TableSystems.Clear();
        }

        /// <summary>
        /// Initialize the table manager and start the component hover check.
        /// </summary>
        public void Init()
        {
            // Add check for already initialized
            if (Instance != null && Instance.m_IsInitialized)
            {
                Debug.LogWarning("TableManager is already initialized via an existing instance.");
                // If another instance is already initialized, we might have an issue.
                // For now, let's assume this new instance should not proceed if a fully initialized one exists.
                if (Instance != this) return; 
            }
            
            if (m_IsInitialized)
            {
                Debug.LogWarning("TableManager instance is already initialized.");
                return;
            }

            Instance = this;
            // m_CancellationTokenSource = new CancellationTokenSource(); // No longer needed

            // Register for application quit
            Application.quitting += Shutdown;

            // // TODO: Instantiate InteractionManager MonoBehaviour
            GameObject interactionGO = new GameObject("TableManagerComponentHost");
            m_InteractionManagerInstance = interactionGO.AddComponent<TableManagerComponent>();
            m_InteractionManagerInstance.Initialize(this);

            // // Start the hover check process (KEEPING ASYNC FOR NOW)
            // _ = ComponentInteractionCheckAsync(m_CancellationTokenSource.Token); // REMOVED async task start

            // Initialize all systems
            foreach (var system in m_TableSystems)
            {
                if (system != null)
                {
                    system.Init(this);
                }
            }

            m_IsInitialized = true;
        }

        /// <summary>
        /// Shutdown the table manager and stop all running processes.
        /// </summary>
        public void Shutdown()
        {
            if (!m_IsInitialized)
                return;

            // Shutdown all systems
            foreach (var system in m_TableSystems)
            {
                if (system != null)
                {
                    system.Shutdown();
                }
            }

            // Destroy the Interaction Manager GameObject
            if (m_InteractionManagerInstance != null)
            {
                 Destroy(m_InteractionManagerInstance.gameObject);
                 m_InteractionManagerInstance = null;
            }

            Instance = null; // Clear the static instance
            m_CurrentHoveredComponent = null;
            m_IsInitialized = false;

            // Unregister from application quit
            Application.quitting -= Shutdown;
        }

        // --- Public Methods for Interaction Processing ---

        /// <summary>
        /// Checks if a GameObject is part of the managed components list.
        /// </summary>
        public bool IsManagedComponent(GameObject obj)
        {
            if (obj == null) return false;
            return (m_TableComponents.Contains(obj) && obj.TryGetComponent(out CardLayout card)) || obj.GetComponent<HandleIdentifier>() != null;
        }

        /// <summary>
        /// Processes hover state changes.
        /// </summary>
        public void ProcessHover(GameObject hoveredObject)
        {
            if (hoveredObject != m_CurrentHoveredComponent) // Only process if hover changed
            {
                 // Check if the new object is actually managed (could be null)
                 bool isManaged = IsManagedComponent(hoveredObject);
                 GameObject newHoverTarget = isManaged ? hoveredObject : null;

                 if (newHoverTarget != m_CurrentHoveredComponent) {
                      m_CurrentHoveredComponent = newHoverTarget;
                      OnComponentHovered?.Invoke(m_CurrentHoveredComponent);
                 }
            }
        }

        /// <summary>
        /// Processes a click on a managed component.
        /// </summary>
        public void ProcessClick(GameObject clickedObject)
        {
            if (IsManagedComponent(clickedObject))
            {
                OnComponentClicked?.Invoke(clickedObject);
            }
            else
            {
                // If somehow called with non-managed object, treat as blank click
                ProcessBlankClick();
            }
        }

        /// <summary>
        /// Processes a click on a blank area or unmanaged object.
        /// </summary>
        public void ProcessBlankClick()
        {
            OnBlankClicked?.Invoke();
        }

        // --------------------------------------------------

        /// <summary>
        /// Called by TableManagerComponent to invoke the frame update event.
        /// </summary>
        internal void InvokeUpdate()
        {
            OnUpdate?.Invoke();
        }

        /// <summary>
        /// Spawns a new card GameObject based on a CardDefinition asset and adds it to the managed components.
        /// </summary>
        /// <param name="cardDefinition">The definition asset for the card.</param>
        /// <param name="position">World position to spawn the card.</param>
        /// <param name="rotation">World rotation to spawn the card.</param>
        /// <returns>The instantiated card GameObject, or null if creation failed.</returns>
        public GameObject SpawnCard(CardDefinition cardDefinition, Vector3 position, Quaternion rotation)
        {
            if (cardDefinition == null)
            {
                Debug.LogError("Cannot spawn card with a null CardDefinition.", this);
                return null;
            }

            GameObject cardObject = ComponentUtils.CreateCardGameObject(cardDefinition, position, rotation);

            if (cardObject != null)
            {
                // Register the newly created card with the manager
                AddComponent(cardObject);
                Debug.Log($"Spawned card '{cardObject.name}' at position {position}.", this);
            }
            else
            {
                Debug.LogError($"Failed to create card GameObject from definition '{cardDefinition.name}'.", this);
            }

            return cardObject;
        }
    }
}

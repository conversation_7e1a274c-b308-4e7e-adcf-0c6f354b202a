using UnityEngine;
using UnityEngine.EventSystems;

namespace Workshop.UXLab
{
    /// <summary>
    /// MonoBehaviour host for the TableManager ScriptableObject.
    /// Handles the Update loop for interaction checks.
    /// </summary>
    public class TableManagerComponent : MonoBehaviour
    {
        private TableManager m_TableManager;
        private Camera m_Camera;

        /// <summary>
        /// Initializes the component with a reference to its controlling TableManager.
        /// </summary>
        public void Initialize(TableManager manager)
        {
            m_TableManager = manager;
            if (m_TableManager == null)
            {
                Debug.LogError("[TableManagerComponent] Initialization failed: TableManager is null!", this);
                enabled = false;
                return;
            }
            
            if (m_TableManager.CameraController != null)
            {
                m_Camera = m_TableManager.CameraController.Camera;
            }
            
            if (m_Camera == null)
            {
                 Debug.LogError("[TableManagerComponent] Initialization failed: Camera not found via TableManager!", this);
                 enabled = false; // Disable if no camera
            }
            else
            {
                 Debug.Log("[TableManagerComponent] Initialized successfully.", this);
            }
        }

        void Update()
        {
            if (m_TableManager == null || m_Camera == null || !enabled)
            {
                return; // Not ready or disabled
            }

            // Check if mouse is over a UI element
            bool isOverUI = EventSystem.current != null && EventSystem.current.IsPointerOverGameObject();
            
            // Check for mouse clicks 
            bool isMouseClicked = Input.GetMouseButtonDown(0);

            // If clicking on UI, let UI handle it, don't process world interactions
             if (isOverUI && isMouseClicked)
             {
                 // Potentially clear world hover state if needed
                 m_TableManager.ProcessHover(null); 
                 return; // Skip world raycast this frame
             }

             // --- World Interaction Check --- 
            
            // Invoke the general update event for subscribed systems
            m_TableManager.InvokeUpdate();
            
            if (!isOverUI)
            {
                Ray ray = m_Camera.ScreenPointToRay(Input.mousePosition);
                RaycastHit hit;
                GameObject hitObject = null;

                if (Physics.Raycast(ray, out hit))
                {
                    hitObject = hit.collider.gameObject;
                    // Check if it's a managed component or handle
                    if (!m_TableManager.IsManagedComponent(hitObject)) 
                    {
                        hitObject = null; // Treat as hitting nothing if not managed
                    }
                }
                
                // Process Hover
                m_TableManager.ProcessHover(hitObject); 

                // Process Click
                if (isMouseClicked)
                {
                    if (hitObject != null)
                    {
                        m_TableManager.ProcessClick(hitObject);
                    }
                    else
                    {
                        // Clicked on empty space or unmanaged object
                        m_TableManager.ProcessBlankClick();
                    }
                }
            }
            else // Mouse is over UI but wasn't clicked
            {
                 // Clear world hover state
                 m_TableManager.ProcessHover(null); 
            }
        }
    }
}

using UnityEngine;

namespace Workshop.UXLab
{
    /// <summary>
    /// Base class for systems that extend the Table functionality
    /// </summary>
    public abstract class TableSystem : ScriptableObject
    {
        protected TableManager m_TableManager;
        protected bool m_IsInitialized;
        
        /// <summary>
        /// Initialize the system with a reference to the table manager
        /// </summary>
        /// <param name="tableManager">The TableManager instance</param>
        public virtual void Init(TableManager tableManager)
        {
            if (m_IsInitialized)
            {
                Debug.LogWarning($"System {this.name} is already initialized.");
                return;
            }
            
            m_TableManager = tableManager;
            m_IsInitialized = true;
            
            Debug.Log($"TableSystem {this.name} initialized.");
        }
        
        /// <summary>
        /// Shutdown the system and clean up any resources
        /// </summary>
        public virtual void Shutdown()
        {
            if (!m_IsInitialized)
                return;
                
            m_IsInitialized = false;
        }
    }
} 
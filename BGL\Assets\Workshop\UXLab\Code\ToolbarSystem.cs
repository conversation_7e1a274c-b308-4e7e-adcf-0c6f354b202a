using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;
using Unity.AppUI.UI;
using Button = Unity.AppUI.UI.Button; // Disambiguate from UnityEngine.UIElements.Button
using System;
using Object = UnityEngine.Object;
#if UNITY_EDITOR
using UnityEditor;
using Workshop.UXLab.Data; // For CardDefinition
#endif

namespace Workshop.UXLab
{
    [CreateAssetMenu(fileName = "ToolbarSystem", menuName = "Workshop/UXLab/Systems/ToolbarSystem")]
    public class ToolbarSystem : TableSystem
    {
        [Header("UI References")]
        [SerializeField] private string m_TopToolbarLayoutPath = "UI/Toolbars/TopToolbarLayout";
        [SerializeField] private string m_ModeToolbarLayoutPath = "UI/Toolbars/ModeToolbarLayout";
        [SerializeField] private string m_EditingToolbarLayoutPath = "UI/Toolbars/EditingToolbarLayout";
        [SerializeField] private string m_TopToolbarStyleSheetPath = "UI/Toolbars/TopToolbarStyles";
        [SerializeField] private string m_ModeToolbarStyleSheetPath = "UI/Toolbars/ModeToolbarStyles";
        [SerializeField] private string m_EditingToolbarStyleSheetPath = "UI/Toolbars/EditingToolbarStyles";

        // --- Static State ---
        // Simple way for other systems/components to check the mode.
        // Avoids complex eventing for a visual toggle. Reset on domain reload.
        [NonSerialized]
        public static bool IsViewModeActive = true;
        [NonSerialized]
        public static bool IsGridRulersModeActive = false;
        [NonSerialized]
        public static bool IsViewModeExpanded = false;
        [NonSerialized]
        public static bool IsGridRulersModeExpanded = false;
        
        // --- Instance Members ---
        
        // UI Host
        private GameObject m_UIHostObject;
        private VisualElement m_RootUIElement;
        private Panel m_WrapperPanel;

        // Toolbar Root Elements
        private VisualElement m_RightPanelRoot;
        private VisualElement m_ModeToolbarRoot;
        private ActionGroup m_EditingToolbarGroupElement;

        // Tab system elements
        private ActionGroup m_TabGroup;
        private VisualElement m_TabContent;

        // Mode system elements
        private ActionButton m_ViewModeButton;
        private ActionButton m_GridRulersModeButton;
        private VisualElement m_ViewModeToolsContainer;
        private VisualElement m_GridRulersToolsContainer;

        // Reference to other systems (needed for actions)
        private CardEditorSystem m_CardEditorSystem;

        public override void Init(TableManager tableManager)
        {
            base.Init(tableManager);

            // Get reference to other systems
            m_CardEditorSystem = m_TableManager.GetSystem<CardEditorSystem>();
            if (m_CardEditorSystem == null)
            {
                Debug.LogWarning("ToolbarSystem: CardEditorSystem not found via TableManager. Save/Load functionality might be disabled.", this);
            }

            if (!SetupUIHost())
            {
                Debug.LogError("ToolbarSystem: Failed to initialize UI Host. Aborting Init.", this);
                base.Shutdown(); 
                return;
            }

            LoadToolbars();
            SetupToolbarButtons();

            Debug.Log("ToolbarSystem Initialized.", this);
        }

        public override void Shutdown()
        {
            if (!m_IsInitialized)
                return;

            if (m_UIHostObject != null)
            {
                Object.Destroy(m_UIHostObject);
                m_UIHostObject = null;
                m_RootUIElement = null;
                m_WrapperPanel = null;
            }

            m_RightPanelRoot = null;
            m_ModeToolbarRoot = null;
            m_EditingToolbarGroupElement = null;
            m_TabGroup = null;
            m_TabContent = null;
            m_ViewModeButton = null;
            m_GridRulersModeButton = null;
            m_ViewModeToolsContainer = null;
            m_GridRulersToolsContainer = null;

            Debug.Log("ToolbarSystem Shutdown.", this);
            base.Shutdown();
        }

        private bool SetupUIHost()
        {
            if (m_UIHostObject != null)
            {
                m_RootUIElement = m_UIHostObject.GetComponent<UIDocument>()?.rootVisualElement;
                if (m_RootUIElement != null) {
                     m_WrapperPanel = m_RootUIElement.Q<Panel>();
                     if (m_WrapperPanel != null) return true;
                }
                else {
                     Debug.LogWarning("ToolbarSystem: UI Host object existed but root element was null. Recreating.", this);
                     Object.Destroy(m_UIHostObject); 
                     m_UIHostObject = null;
                }
            }

            if (m_UIHostObject == null) {
                 m_UIHostObject = new GameObject("ToolbarUIHost");
                 var uiDocument = m_UIHostObject.AddComponent<UIDocument>();
                 if (m_TableManager.UIPanelSettings == null)
                 {
                      Debug.LogError("ToolbarSystem: TableManager.UIPanelSettings is null. Cannot configure UI host.", this);
                      Object.Destroy(m_UIHostObject);
                      m_UIHostObject = null;
                      return false;
                 }
                 uiDocument.panelSettings = m_TableManager.UIPanelSettings;
                 m_RootUIElement = uiDocument.rootVisualElement;
                 if (m_RootUIElement == null)
                 {
                      Debug.LogError("ToolbarSystem: Failed to get rootVisualElement from created UIDocument.", this);
                      Object.Destroy(m_UIHostObject);
                      m_UIHostObject = null;
                      return false;
                 }
                 m_RootUIElement.pickingMode = PickingMode.Ignore; 
            }

            if (m_WrapperPanel == null && m_RootUIElement != null) {
                 m_WrapperPanel = new Panel();
                 m_WrapperPanel.name = "toolbar-wrapper-panel";
                 m_WrapperPanel.style.position = Position.Absolute;
                 m_WrapperPanel.style.top = 0;
                 m_WrapperPanel.style.left = 0;
                 m_WrapperPanel.style.right = 0;
                 m_WrapperPanel.style.bottom = 0;
                 m_WrapperPanel.style.backgroundColor = Color.clear;
                 m_WrapperPanel.pickingMode = PickingMode.Ignore;
                 m_RootUIElement.Add(m_WrapperPanel);
            }
            
            return m_WrapperPanel != null;
        }

        private void LoadToolbars()
        {
            if (m_RootUIElement == null) return;

            // Load Right Panel (replaces top toolbar)
            m_RightPanelRoot = LoadToolbarElement<VisualElement>(m_TopToolbarLayoutPath, m_TopToolbarStyleSheetPath, "right-panel");
            Debug.Log($"ToolbarSystem: Right Panel Root loaded: {m_RightPanelRoot != null}", this);

            // Load Mode Toolbar (VisualElement container)
            m_ModeToolbarRoot = LoadToolbarElement<VisualElement>(m_ModeToolbarLayoutPath, m_ModeToolbarStyleSheetPath, "mode-toolbar");
            Debug.Log($"ToolbarSystem: Mode Toolbar Root loaded: {m_ModeToolbarRoot != null}", this);

            // Load Editing Toolbar (ActionGroup container)
            m_EditingToolbarGroupElement = LoadToolbarElement<ActionGroup>(m_EditingToolbarLayoutPath, m_EditingToolbarStyleSheetPath, "editing-toolbar");
            Debug.Log($"ToolbarSystem: Editing Toolbar loaded: {m_EditingToolbarGroupElement != null}", this);

            // Get references to sub-elements
            if (m_RightPanelRoot != null)
            {
                m_TabGroup = m_RightPanelRoot.Q<ActionGroup>("tab-group");
                m_TabContent = m_RightPanelRoot.Q<VisualElement>("tab-content");
                Debug.Log($"ToolbarSystem: Tab Group found: {m_TabGroup != null}, Tab Content found: {m_TabContent != null}", this);
            }

            if (m_ModeToolbarRoot != null)
            {
                m_ViewModeButton = m_ModeToolbarRoot.Q<ActionButton>("view-mode-toggle");
                m_GridRulersModeButton = m_ModeToolbarRoot.Q<ActionButton>("grid-rulers-mode-toggle");
                m_ViewModeToolsContainer = m_ModeToolbarRoot.Q<VisualElement>("view-mode-tools-container");
                m_GridRulersToolsContainer = m_ModeToolbarRoot.Q<VisualElement>("grid-rulers-tools-container");
                Debug.Log($"ToolbarSystem: View Mode Button found: {m_ViewModeButton != null}, Grid/Rulers Button found: {m_GridRulersModeButton != null}", this);
                Debug.Log($"ToolbarSystem: View Tools Container found: {m_ViewModeToolsContainer != null}, Grid Tools Container found: {m_GridRulersToolsContainer != null}", this);
            }
        }

        // Generic method to load layout, apply styles, and add to root
        private T LoadToolbarElement<T>(string layoutPath, string stylePath, string rootClass) where T : VisualElement
        {
            VisualTreeAsset layout = Resources.Load<VisualTreeAsset>(layoutPath);
            StyleSheet styles = Resources.Load<StyleSheet>(stylePath);

            if (layout == null)
            {
                Debug.LogError($"ToolbarSystem: Could not load layout from Resources/{layoutPath}", this);
                return null;
            }

            // Instantiate the template UXML
            TemplateContainer instance = layout.Instantiate();
            if (instance.childCount == 0) {
                 Debug.LogError($"ToolbarSystem: Instantiated layout from {layoutPath} has no children.", this);
                 return null;
            }
            
            // Get the actual root element defined *inside* the UXML file (the first child of the container)
            VisualElement desiredElement = instance.Children().FirstOrDefault();

            if (desiredElement == null) {
                Debug.LogError($"ToolbarSystem: Could not find the first child element within the instantiated {layoutPath}.", this);
                return null;
            }

            // Ensure the found element is of the expected type (e.g., ActionGroup or VisualElement)
            if (!(desiredElement is T toolbarInstance)) {
                 Debug.LogError($"ToolbarSystem: First child element in {layoutPath} is type {desiredElement.GetType().Name}, expected {typeof(T).Name}.", this);
                 return null;
            }

            // Add root class if not already present
            if (!toolbarInstance.ClassListContains(rootClass)) {
                 toolbarInstance.AddToClassList(rootClass); 
            }

            // Apply the specific stylesheet to this element
            if (styles != null)
            {
                toolbarInstance.styleSheets.Add(styles);
            }
            else
            {
                Debug.LogWarning($"ToolbarSystem: Could not load stylesheet from Resources/{stylePath}", this);
            }

            // Add the actual desired element to the WRAPPER PANEL
            if (m_WrapperPanel != null) {
                 m_WrapperPanel.Add(toolbarInstance);
                 return toolbarInstance; 
            } else {
                 Debug.LogError($"ToolbarSystem: Wrapper panel is null. Cannot add {toolbarInstance.name}.", this);
                 return null;
            }
        }

        private void SetupToolbarButtons()
        {
            // --- Tab System Setup ---
            if (m_TabGroup != null)
            {
                SetupActionButtonCallback(m_TabGroup, "faces-tab", () => {
                    ShowTabContent("faces-content");
                    Debug.Log("Faces Tab Activated");
                });
                SetupActionButtonCallback(m_TabGroup, "templates-tab", () => {
                    ShowTabContent("templates-content");
                    Debug.Log("Templates Tab Activated");
                });
                SetupActionButtonCallback(m_TabGroup, "backs-tab", () => {
                    ShowTabContent("backs-content");
                    Debug.Log("Backs Tab Activated");
                });

                // Set default tab
                ShowTabContent("faces-content");
            }

            // --- Primary Mode System Setup ---
            if (m_ViewModeButton != null && m_GridRulersModeButton != null)
            {
                m_ViewModeButton.clicked += () => {
                    ToggleViewMode();
                    Debug.Log("View Mode Toggled");
                };

                m_GridRulersModeButton.clicked += () => {
                    ToggleGridRulersMode();
                    Debug.Log("Grid & Rulers Mode Toggled");
                };

                // Set default mode
                SetViewMode(true, false); // Active but not expanded
            }

            // --- Secondary Tools Setup ---
            if (m_ModeToolbarRoot != null)
            {
                // View mode tools
                var viewModeTools = m_ModeToolbarRoot.Q<ActionGroup>("view-mode-tools");
                if (viewModeTools != null)
                {
                    SetupActionButtonCallback(viewModeTools, "bounding-box-tool", () => Debug.Log("Bounding Box Tool Activated"));
                    SetupActionButtonCallback(viewModeTools, "text-tool", () => Debug.Log("Text Tool Activated"));
                    SetupActionButtonCallback(viewModeTools, "image-tool", () => Debug.Log("Image Tool Activated"));
                }

                // Grid/rulers mode tools
                var gridRulersTools = m_ModeToolbarRoot.Q<ActionGroup>("grid-rulers-tools");
                if (gridRulersTools != null)
                {
                    SetupActionButtonCallback(gridRulersTools, "rulers-tool", () => Debug.Log("Rulers Tool Activated"));
                    SetupActionButtonCallback(gridRulersTools, "grid-tool", () => Debug.Log("Grid Tool Activated"));
                    SetupActionButtonCallback(gridRulersTools, "measurement-tool", () => Debug.Log("Measurement Tool Activated"));
                    SetupActionButtonCallback(gridRulersTools, "move-tool", () => Debug.Log("Move Tool Activated"));
                }
            }
        }
        
        // Helper to setup simple (non-toggle) Button clicks
        private void SetupButtonCallback(VisualElement root, string buttonName, System.Action onClick)
        {
            var button = root?.Q<Unity.AppUI.UI.Button>(buttonName); 
            if (button != null)
            {
                button.clicked += onClick;
                return; // Found Button, done.
            }
            
            // Fallback: Try finding an ActionButton if Button wasn't found (for top toolbar)
            var actionButton = root?.Q<ActionButton>(buttonName);
            if (actionButton != null) {
                actionButton.clicked += onClick;
            } else {
                 Debug.LogWarning($"ToolbarSystem: Button or ActionButton '{buttonName}' not found in {root?.name}.", this);
            }
        }

        // Helper to setup ActionButton clicks (within an ActionGroup or standalone)
        private void SetupActionButtonCallback(VisualElement root, string buttonName, System.Action onActivated)
        {
            var button = root?.Q<ActionButton>(buttonName);
            if (button != null)
            {
                 // ActionGroup handles the selection state, just wire up the click event
                 button.clicked += () => {
                     onActivated?.Invoke(); // Call the specific action for this button
                 };
            }
            else
            {
                Debug.LogWarning($"ToolbarSystem: ActionButton '{buttonName}' not found in {root?.name}.", this);
            }
        }

        // --- Helper Methods ---

        private void ShowTabContent(string activeTabName)
        {
            if (m_TabContent == null) return;

            // Hide all tab panels
            var allPanels = m_TabContent.Query<VisualElement>(className: "tab-panel").ToList();
            foreach (var panel in allPanels)
            {
                panel.RemoveFromClassList("active");
            }

            // Show the active tab panel
            var activePanel = m_TabContent.Q<VisualElement>(activeTabName);
            if (activePanel != null)
            {
                activePanel.AddToClassList("active");
            }
        }

        private void SetViewMode(bool isViewMode, bool isExpanded)
        {
            IsViewModeActive = isViewMode;
            IsGridRulersModeActive = !isViewMode;

            if (isViewMode)
            {
                IsViewModeExpanded = isExpanded;
                IsGridRulersModeExpanded = false; // Collapse the other mode
            }
            else
            {
                IsGridRulersModeExpanded = isExpanded;
                IsViewModeExpanded = false; // Collapse the other mode
            }

            UpdateModeToolbarClasses();
        }

        private void ToggleViewMode()
        {
            if (IsViewModeActive)
            {
                // If already active, toggle expansion
                IsViewModeExpanded = !IsViewModeExpanded;
            }
            else
            {
                // Switch to view mode and expand
                SetViewMode(true, true);
            }
            UpdateModeToolbarClasses();
        }

        private void ToggleGridRulersMode()
        {
            if (IsGridRulersModeActive)
            {
                // If already active, toggle expansion
                IsGridRulersModeExpanded = !IsGridRulersModeExpanded;
            }
            else
            {
                // Switch to grid/rulers mode and expand
                SetViewMode(false, true);
            }
            UpdateModeToolbarClasses();
        }

        private void UpdateModeToolbarClasses()
        {
            if (m_ModeToolbarRoot == null) return;

            // Remove all mode classes
            m_ModeToolbarRoot.RemoveFromClassList("view-mode-active");
            m_ModeToolbarRoot.RemoveFromClassList("grid-rulers-active");
            m_ModeToolbarRoot.RemoveFromClassList("view-mode-expanded");
            m_ModeToolbarRoot.RemoveFromClassList("grid-rulers-expanded");

            // Add active mode class
            if (IsViewModeActive)
            {
                m_ModeToolbarRoot.AddToClassList("view-mode-active");
                if (IsViewModeExpanded)
                {
                    m_ModeToolbarRoot.AddToClassList("view-mode-expanded");
                }
            }
            else
            {
                m_ModeToolbarRoot.AddToClassList("grid-rulers-active");
                if (IsGridRulersModeExpanded)
                {
                    m_ModeToolbarRoot.AddToClassList("grid-rulers-expanded");
                }
            }
        }

        // --- Action Handlers ---
        // Note: Save/Load functionality moved to right panel context or other UI elements
    }
} 
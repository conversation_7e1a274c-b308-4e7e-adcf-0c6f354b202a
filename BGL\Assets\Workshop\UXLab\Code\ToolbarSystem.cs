using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;
using Unity.AppUI.UI;
using Button = Unity.AppUI.UI.Button; // Disambiguate from UnityEngine.UIElements.Button
using System;
using Object = UnityEngine.Object;
#if UNITY_EDITOR
using UnityEditor;
using Workshop.UXLab.Data; // For CardDefinition
#endif

namespace Workshop.UXLab
{
    [CreateAssetMenu(fileName = "ToolbarSystem", menuName = "Workshop/UXLab/Systems/ToolbarSystem")]
    public class ToolbarSystem : TableSystem
    {
        [Header("UI References")]
        [SerializeField] private string m_TopToolbarLayoutPath = "UI/Toolbars/TopToolbarLayout";
        [SerializeField] private string m_ModeToolbarLayoutPath = "UI/Toolbars/ModeToolbarLayout";
        [SerializeField] private string m_EditingToolbarLayoutPath = "UI/Toolbars/EditingToolbarLayout";
        [SerializeField] private string m_TopToolbarStyleSheetPath = "UI/Toolbars/TopToolbarStyles";
        [SerializeField] private string m_ModeToolbarStyleSheetPath = "UI/Toolbars/ModeToolbarStyles";
        [SerializeField] private string m_EditingToolbarStyleSheetPath = "UI/Toolbars/EditingToolbarStyles";

        // --- Static State ---
        // Simple way for other systems/components (like CardElement) to check the mode.
        // Avoids complex eventing for a visual toggle. Reset on domain reload.
        [NonSerialized]
        public static bool IsBoxModeActive = false;
        
        // --- Instance Members ---
        
        // UI Host
        private GameObject m_UIHostObject;
        private VisualElement m_RootUIElement;
        private Panel m_WrapperPanel;

        // Toolbar Root Elements (ActionGroup for toggles)
        private VisualElement m_TopToolbarRoot;
        private ActionGroup m_ModeToolbarGroupElement;
        private ActionGroup m_EditingToolbarGroupElement;

        // Reference to other systems (needed for actions)
        private CardEditorSystem m_CardEditorSystem;

        public override void Init(TableManager tableManager)
        {
            base.Init(tableManager);

            // Get reference to other systems
            m_CardEditorSystem = m_TableManager.GetSystem<CardEditorSystem>();
            if (m_CardEditorSystem == null)
            {
                Debug.LogWarning("ToolbarSystem: CardEditorSystem not found via TableManager. Save/Load functionality might be disabled.", this);
            }

            if (!SetupUIHost())
            {
                Debug.LogError("ToolbarSystem: Failed to initialize UI Host. Aborting Init.", this);
                base.Shutdown(); 
                return;
            }

            LoadToolbars();
            SetupToolbarButtons();

            Debug.Log("ToolbarSystem Initialized.", this);
        }

        public override void Shutdown()
        {
            if (!m_IsInitialized)
                return;

            if (m_UIHostObject != null)
            {
                Object.Destroy(m_UIHostObject);
                m_UIHostObject = null;
                m_RootUIElement = null;
                m_WrapperPanel = null;
            }

            m_TopToolbarRoot = null;
            m_ModeToolbarGroupElement = null;
            m_EditingToolbarGroupElement = null;

            Debug.Log("ToolbarSystem Shutdown.", this);
            base.Shutdown();
        }

        private bool SetupUIHost()
        {
            if (m_UIHostObject != null)
            {
                m_RootUIElement = m_UIHostObject.GetComponent<UIDocument>()?.rootVisualElement;
                if (m_RootUIElement != null) {
                     m_WrapperPanel = m_RootUIElement.Q<Panel>();
                     if (m_WrapperPanel != null) return true;
                }
                else {
                     Debug.LogWarning("ToolbarSystem: UI Host object existed but root element was null. Recreating.", this);
                     Object.Destroy(m_UIHostObject); 
                     m_UIHostObject = null;
                }
            }

            if (m_UIHostObject == null) {
                 m_UIHostObject = new GameObject("ToolbarUIHost");
                 var uiDocument = m_UIHostObject.AddComponent<UIDocument>();
                 if (m_TableManager.UIPanelSettings == null)
                 {
                      Debug.LogError("ToolbarSystem: TableManager.UIPanelSettings is null. Cannot configure UI host.", this);
                      Object.Destroy(m_UIHostObject);
                      m_UIHostObject = null;
                      return false;
                 }
                 uiDocument.panelSettings = m_TableManager.UIPanelSettings;
                 m_RootUIElement = uiDocument.rootVisualElement;
                 if (m_RootUIElement == null)
                 {
                      Debug.LogError("ToolbarSystem: Failed to get rootVisualElement from created UIDocument.", this);
                      Object.Destroy(m_UIHostObject);
                      m_UIHostObject = null;
                      return false;
                 }
                 m_RootUIElement.pickingMode = PickingMode.Ignore; 
            }

            if (m_WrapperPanel == null && m_RootUIElement != null) {
                 m_WrapperPanel = new Panel();
                 m_WrapperPanel.name = "toolbar-wrapper-panel";
                 m_WrapperPanel.style.position = Position.Absolute;
                 m_WrapperPanel.style.top = 0;
                 m_WrapperPanel.style.left = 0;
                 m_WrapperPanel.style.right = 0;
                 m_WrapperPanel.style.bottom = 0;
                 m_WrapperPanel.style.backgroundColor = Color.clear;
                 m_WrapperPanel.pickingMode = PickingMode.Ignore;
                 m_RootUIElement.Add(m_WrapperPanel);
            }
            
            return m_WrapperPanel != null;
        }

        private void LoadToolbars()
        {
            if (m_RootUIElement == null) return;

            // Load Top Toolbar (Standard VisualElement container)
            m_TopToolbarRoot = LoadToolbarElement<VisualElement>(m_TopToolbarLayoutPath, m_TopToolbarStyleSheetPath, "top-toolbar");

            // Load Mode Toolbar (ActionGroup container)
            m_ModeToolbarGroupElement = LoadToolbarElement<ActionGroup>(m_ModeToolbarLayoutPath, m_ModeToolbarStyleSheetPath, "mode-toolbar");

            // Load Editing Toolbar (ActionGroup container)
            m_EditingToolbarGroupElement = LoadToolbarElement<ActionGroup>(m_EditingToolbarLayoutPath, m_EditingToolbarStyleSheetPath, "editing-toolbar");
        }

        // Generic method to load layout, apply styles, and add to root
        private T LoadToolbarElement<T>(string layoutPath, string stylePath, string rootClass) where T : VisualElement
        {
            VisualTreeAsset layout = Resources.Load<VisualTreeAsset>(layoutPath);
            StyleSheet styles = Resources.Load<StyleSheet>(stylePath);

            if (layout == null)
            {
                Debug.LogError($"ToolbarSystem: Could not load layout from Resources/{layoutPath}", this);
                return null;
            }

            // Instantiate the template UXML
            TemplateContainer instance = layout.Instantiate();
            if (instance.childCount == 0) {
                 Debug.LogError($"ToolbarSystem: Instantiated layout from {layoutPath} has no children.", this);
                 return null;
            }
            
            // Get the actual root element defined *inside* the UXML file (the first child of the container)
            VisualElement desiredElement = instance.Children().FirstOrDefault();

            if (desiredElement == null) {
                Debug.LogError($"ToolbarSystem: Could not find the first child element within the instantiated {layoutPath}.", this);
                return null;
            }

            // Ensure the found element is of the expected type (e.g., ActionGroup or VisualElement)
            if (!(desiredElement is T toolbarInstance)) {
                 Debug.LogError($"ToolbarSystem: First child element in {layoutPath} is type {desiredElement.GetType().Name}, expected {typeof(T).Name}.", this);
                 return null;
            }

            // Add root class if not already present
            if (!toolbarInstance.ClassListContains(rootClass)) {
                 toolbarInstance.AddToClassList(rootClass); 
            }

            // Apply the specific stylesheet to this element
            if (styles != null)
            {
                toolbarInstance.styleSheets.Add(styles);
            }
            else
            {
                Debug.LogWarning($"ToolbarSystem: Could not load stylesheet from Resources/{stylePath}", this);
            }

            // Add the actual desired element to the WRAPPER PANEL
            if (m_WrapperPanel != null) {
                 m_WrapperPanel.Add(toolbarInstance);
                 return toolbarInstance; 
            } else {
                 Debug.LogError($"ToolbarSystem: Wrapper panel is null. Cannot add {toolbarInstance.name}.", this);
                 return null;
            }
        }

        private void SetupToolbarButtons()
        {
            // --- Top Toolbar Buttons --- (Regular Buttons)
            if (m_TopToolbarRoot != null)
            {
                 SetupButtonCallback(m_TopToolbarRoot, "save-button", HandleSaveButtonClicked);
                 SetupButtonCallback(m_TopToolbarRoot, "load-button", HandleLoadButtonClicked);
                 SetupButtonCallback(m_TopToolbarRoot, "component-editor-button", () => Debug.Log("Component Editor Button Clicked"));
                 SetupButtonCallback(m_TopToolbarRoot, "deck-composer-button", () => Debug.Log("Deck Composer Button Clicked"));
            }

            // --- Mode Toolbar Buttons --- (Managed by ActionGroup)
             if (m_ModeToolbarGroupElement != null)
             {
                 SetupActionButtonCallback(m_ModeToolbarGroupElement, "view-mode-button", () => {
                     Debug.Log("View Mode Activated"); 
                      m_ModeToolbarGroupElement.ToggleInClassList("expanded");
                 });
                 SetupActionButtonCallback(m_ModeToolbarGroupElement, "image-mode-button", () => {
                     Debug.Log("Image Mode Activated");
                 });
                 SetupActionButtonCallback(m_ModeToolbarGroupElement, "box-mode-button", () => {
                     IsBoxModeActive = !IsBoxModeActive; // Toggle the state
                     Debug.Log("Box Mode Activated");
                 });
                 SetupActionButtonCallback(m_ModeToolbarGroupElement, "grid-mode-button", () => {
                     Debug.Log("Grid Mode Activated");
                 });
                 SetupActionButtonCallback(m_ModeToolbarGroupElement, "deck-mode-button", () => {
                     Debug.Log("Deck Mode Activated");
                 });

                 // ActionGroup with selection-type="Multiple" handles visual toggling.
                 // We just need to ensure our static state matches the desired behavior.
                 
                 // Reset state on init, assuming View mode is default visually
                 IsBoxModeActive = false; 
                 // If ActionGroup has an API to set selected index/button, we could use it here
                 // to ensure visual state matches IsBoxModeActive.
                 // m_ModeToolbarGroupElement.SetSelected(m_ModeToolbarGroupElement.Q<ActionButton>("view-mode-button")); // Example hypothetical API
                 // For multiple selection, we might need to update the visual state based on IsBoxModeActive
                 // if the ActionGroup doesn't automatically reflect external state changes.
             }
        }
        
        // Helper to setup simple (non-toggle) Button clicks
        private void SetupButtonCallback(VisualElement root, string buttonName, System.Action onClick)
        {
            var button = root?.Q<Unity.AppUI.UI.Button>(buttonName); 
            if (button != null)
            {
                button.clicked += onClick;
                return; // Found Button, done.
            }
            
            // Fallback: Try finding an ActionButton if Button wasn't found (for top toolbar)
            var actionButton = root?.Q<ActionButton>(buttonName);
            if (actionButton != null) {
                actionButton.clicked += onClick;
            } else {
                 Debug.LogWarning($"ToolbarSystem: Button or ActionButton '{buttonName}' not found in {root?.name}.", this);
            }
        }

        // Helper to setup ActionButton clicks (within an ActionGroup or standalone)
        private void SetupActionButtonCallback(VisualElement root, string buttonName, System.Action onActivated)
        {
            var button = root?.Q<ActionButton>(buttonName);
            if (button != null)
            {
                 // ActionGroup handles the selection state, just wire up the click event
                 button.clicked += () => {
                     onActivated?.Invoke(); // Call the specific action for this button
                 };
            }
            else
            {
                Debug.LogWarning($"ToolbarSystem: ActionButton '{buttonName}' not found in {root?.name}.", this);
            }
        }

        // --- Action Handlers ---

        private void HandleSaveButtonClicked()
        {
            if (m_CardEditorSystem != null)
            {
                #if UNITY_EDITOR
                m_CardEditorSystem.SaveSelectedCardDefinition();
                #else
                Debug.LogWarning("Save functionality is only available in the Unity Editor.");
                #endif
            }
            else
            {
                Debug.LogError("Save Button Clicked, but CardEditorSystem reference is null.");
            }
        }

        private void HandleLoadButtonClicked()
        {
            #if UNITY_EDITOR
            if (m_CardEditorSystem == null)
            {
                Debug.LogError("Load Button Clicked, but CardEditorSystem reference is null.");
                return;
            }

            CardDefinition selectedDefinition = Selection.activeObject as CardDefinition;

            if (selectedDefinition != null)
            {
                m_CardEditorSystem.LoadCardDefinition(selectedDefinition);
            }
            else
            {
                EditorUtility.DisplayDialog("Load Card Definition", 
                                            "Please select a CardDefinition asset in the Project window first, then click Load.", 
                                            "OK");
            }
            #else
            Debug.LogWarning("Load functionality is only available in the Unity Editor.");
            #endif
        }
    }
} 
using System.Collections.Generic;
using UnityEngine;

namespace Workshop.UXLab
{
    public enum UnitSpace
    {
        Millimeters,
        Centimeters,
        Meters,
        Inches,
        // TODO: Add Points (for fonts primarily?)
        // TODO: Add Pixels (requires DPI context)
    }

    // Optional: Add static helper class for conversions if needed elsewhere
    public static class UnitConverter
    {
        public const float kMmPerMeter = 1000f;
        public const float kMmPerCm = 10f;
        public const float kMmPerInch = 25.4f;

        // Unit Labels for UI display
        public static readonly Dictionary<UnitSpace, string> kUnitLabels = new Dictionary<UnitSpace, string>
        {
            { UnitSpace.Millimeters, "Millimeters (mm)" },
            { UnitSpace.Centimeters, "Centimeters (cm)" },
            { UnitSpace.Inches, "Inches (in)" },
            { UnitSpace.Meters, "Meters (m)" },
        };
        
        public static readonly Dictionary<UnitSpace, string> kUnitLabelsShort = new Dictionary<UnitSpace, string>
        {
            { UnitSpace.Millimeters, "(mm)" },
            { UnitSpace.Centimeters, "(cm)" },
            { UnitSpace.Inches, "(in)" },
            { UnitSpace.Meters, "(m)" },
        };

        /// <summary>
        /// Converts a value FROM the specified UnitSpace TO millimeters (internal units).
        /// </summary>
        public static float ToMillimeters(float value, UnitSpace fromSpace)
        {
            switch (fromSpace)
            {
                case UnitSpace.Centimeters:
                    return value * kMmPerCm;
                case UnitSpace.Inches:
                    return value * kMmPerInch;
                case UnitSpace.Millimeters:
                default:
                    return value; // Already in mm
            }
        }

        /// <summary>
        /// Converts a value FROM millimeters (internal units) TO the target UnitSpace.
        /// </summary>
        public static float FromMillimeters(float valueInMM, UnitSpace toSpace)
        {
            switch (toSpace)
            {
                case UnitSpace.Centimeters:
                    return valueInMM / kMmPerCm;
                case UnitSpace.Inches:
                    return valueInMM / kMmPerInch;
                case UnitSpace.Millimeters:
                default:
                    return valueInMM; // Already in mm
            }
        }

        /// <summary>
        /// Converts a Vector2 value FROM the specified UnitSpace TO millimeters (internal units).
        /// </summary>
        public static Vector2 ToMillimeters(Vector2 value, UnitSpace fromSpace)
        {
            switch (fromSpace)
            {
                case UnitSpace.Centimeters:
                    return value * kMmPerCm;
                case UnitSpace.Inches:
                    return value * kMmPerInch;
                case UnitSpace.Millimeters:
                default:
                    return value;
            }
        }

        /// <summary>
        /// Converts a Vector2 value FROM millimeters (internal units) TO the target UnitSpace.
        /// </summary>
        public static Vector2 FromMillimeters(Vector2 valueInMM, UnitSpace toSpace)
        {
             switch (toSpace)
            {
                case UnitSpace.Centimeters:
                    return valueInMM / kMmPerCm;
                case UnitSpace.Inches:
                    return valueInMM / kMmPerInch;
                case UnitSpace.Millimeters:
                default:
                    return valueInMM;
            }
        }

        /// <summary>
        /// Converts a value from millimeters to meters (for Unity world space).
        /// </summary>
        public static float MmToMeters(float valueInMM)
        {
            return valueInMM / kMmPerMeter;
        }

        /// <summary>
        /// Converts a Vector3 value from millimeters to meters (for Unity world space).
        /// Note: Typically only X and Y are scaled, Z often represents depth/offset.
        /// </summary>
        public static Vector3 MmToMeters(Vector3 valueInMM, bool scaleX = true, bool scaleY = true, bool scaleZ = false)
        {
            return new Vector3(
                scaleX ? valueInMM.x / kMmPerMeter : valueInMM.x,
                scaleY ? valueInMM.y / kMmPerMeter : valueInMM.y,
                scaleZ ? valueInMM.z / kMmPerMeter : valueInMM.z
            );
        }

        /// <summary>
        /// Converts a value from meters to millimeters.
        /// </summary>
        public static float MetersToMm(float valueInMeters)
        {
            return valueInMeters * kMmPerMeter;
        }

        // Add Vector2/Vector3 versions if needed

    }
}
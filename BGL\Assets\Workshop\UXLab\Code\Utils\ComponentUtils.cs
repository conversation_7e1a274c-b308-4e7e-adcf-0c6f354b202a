using UnityEngine;
using Workshop.UXLab.Data;

namespace Workshop.UXLab.Utils
{
    /// <summary>
    /// Utility class for creating and managing common components.
    /// </summary>
    public static class ComponentUtils
    {
        /// <summary>
        /// Creates a new Card GameObject from a CardDefinition asset.
        /// </summary>
        /// <param name="definition">The CardDefinition asset defining the card.</param>
        /// <param name="position">World position to spawn the card.</param>
        /// <param name="rotation">World rotation to spawn the card.</param>
        /// <returns>The instantiated card GameObject, or null if definition is invalid.</returns>
        public static GameObject CreateCardGameObject(CardDefinition definition, Vector3 position, Quaternion rotation)
        {
            if (definition == null)
            {
                Debug.LogError("Cannot create card from null CardDefinition.");
                return null;
            }

            // Create the main GameObject
            GameObject cardGO = new GameObject(definition.CardName ?? "New Card");
            cardGO.transform.position = position;
            cardGO.transform.rotation = rotation;

            // Add required components (<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> are handled by RequireComponent on CardMeshGenerator)
            var meshGenerator = cardGO.AddComponent<CardMeshGenerator>();
            var layout = cardGO.AddComponent<CardLayout>();
            var renderer = cardGO.GetComponent<MeshRenderer>(); // Get renderer added by RequireComponent

            // Apply a default material (important!)
            if (renderer != null)
            {
                // Use a simple default material. Replace with a more appropriate one if needed.
                renderer.sharedMaterial = new Material(Shader.Find("Standard"));
                renderer.sharedMaterial.color = Color.white; // Default to white
            }
            else
            {
                 Debug.LogError("MeshRenderer component not found after adding CardMeshGenerator. RequireComponent might have failed?", cardGO);
            }

            // Apply data to components
            if (meshGenerator != null)
            {
                meshGenerator.ApplySizeData(definition.SizeData);
            }
            else
            {
                 Debug.LogError("CardMeshGenerator component not found after adding it.", cardGO);
            }

            if (layout != null && definition.LayoutData != null)
            {
                layout.ApplyLayoutData(definition.LayoutData);
            }
            else if (layout == null)
            {
                Debug.LogError("CardLayout component not found after adding it.", cardGO);
            }
            // It's okay if definition.LayoutData is null, just means no elements initially

            return cardGO;
        }
    }
} 
using UnityEngine;
using System;
using System.Collections.Generic;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Workshop.UXLab.Utils
{
    /// <summary>
    /// Unity Editor implementation for file drag and drop using Unity's built-in DragAndDrop API.
    /// This provides drag and drop support when running in the Unity Editor.
    /// </summary>
    public static class FileDropEditorAPI
    {
        // --- Events ---
        /// <summary>
        /// Event triggered when files are dropped in the editor.
        /// Parameters: List of file paths, screen position where dropped
        /// </summary>
        public static event Action<List<string>, Vector2> OnFilesDropped;

        /// <summary>
        /// Event triggered when a drag operation starts in the editor.
        /// </summary>
        public static event Action OnDragEnter;

        /// <summary>
        /// Event triggered when a drag operation ends in the editor.
        /// </summary>
        public static event Action OnDragExit;

        // --- Properties ---
        /// <summary>
        /// Whether the editor API is initialized and working.
        /// </summary>
        public static bool IsInitialized { get; private set; }

        /// <summary>
        /// Whether files are currently being dragged in the editor.
        /// </summary>
        public static bool IsDragging { get; private set; }

        // --- Private Fields ---
        private static bool s_IsEnabled = false;
        private static bool s_WasDraggingLastFrame = false;

#if UNITY_EDITOR
        // --- Initialization ---
        /// <summary>
        /// Initialize the Unity Editor drag and drop API.
        /// </summary>
        public static bool Initialize()
        {
            if (IsInitialized)
                return true;

            try
            {
                // Subscribe to editor update to monitor drag state
                EditorApplication.update += UpdateDragState;
                IsInitialized = true;
                Debug.Log("FileDropEditorAPI: Unity Editor drag and drop API initialized successfully.");
                return true;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"FileDropEditorAPI: Failed to initialize Unity Editor drag and drop API: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Shutdown the Unity Editor drag and drop API.
        /// </summary>
        public static void Shutdown()
        {
            if (!IsInitialized)
                return;

            EditorApplication.update -= UpdateDragState;
            IsInitialized = false;
            s_IsEnabled = false;
            IsDragging = false;
            Debug.Log("FileDropEditorAPI: Unity Editor drag and drop API shut down.");
        }

        /// <summary>
        /// Enable drag and drop detection in the editor.
        /// </summary>
        public static bool Enable()
        {
            if (!IsInitialized)
            {
                Debug.LogError("FileDropEditorAPI: Cannot enable - API not initialized.");
                return false;
            }

            s_IsEnabled = true;
            Debug.Log("FileDropEditorAPI: Drag and drop detection enabled in Unity Editor.");
            return true;
        }

        /// <summary>
        /// Disable drag and drop detection in the editor.
        /// </summary>
        public static void Disable()
        {
            s_IsEnabled = false;
            IsDragging = false;
            Debug.Log("FileDropEditorAPI: Drag and drop detection disabled in Unity Editor.");
        }

        /// <summary>
        /// Update method called every editor frame to monitor drag state.
        /// </summary>
        private static void UpdateDragState()
        {
            if (!s_IsEnabled)
                return;

            // Check if we're currently in a drag operation
            bool isDraggingNow = DragAndDrop.paths != null && DragAndDrop.paths.Length > 0;

            // Detect drag enter
            if (isDraggingNow && !s_WasDraggingLastFrame)
            {
                IsDragging = true;
                OnDragEnter?.Invoke();
                Debug.Log("FileDropEditorAPI: Drag operation started in Unity Editor");
            }
            // Detect drag exit
            else if (!isDraggingNow && s_WasDraggingLastFrame)
            {
                IsDragging = false;
                OnDragExit?.Invoke();
                Debug.Log("FileDropEditorAPI: Drag operation ended in Unity Editor");
            }

            s_WasDraggingLastFrame = isDraggingNow;
        }

        /// <summary>
        /// Handle a drop operation in the Unity Editor.
        /// This should be called from UI elements that want to accept drops.
        /// </summary>
        public static bool HandleDrop(Vector2 screenPosition)
        {
            if (!s_IsEnabled || DragAndDrop.paths == null || DragAndDrop.paths.Length == 0)
                return false;

            // Accept the drag operation
            DragAndDrop.AcceptDrag();

            // Convert paths array to list
            List<string> filePaths = new List<string>(DragAndDrop.paths);

            // Trigger the drop event
            OnFilesDropped?.Invoke(filePaths, screenPosition);

            Debug.Log($"FileDropEditorAPI: Handled drop with {filePaths.Count} files at position {screenPosition}");
            foreach (string filePath in filePaths)
            {
                Debug.Log($"FileDropEditorAPI: Dropped file: {filePath}");
            }

            return true;
        }

        /// <summary>
        /// Check if the current drag operation contains valid files.
        /// </summary>
        public static bool HasValidDragData()
        {
            return DragAndDrop.paths != null && DragAndDrop.paths.Length > 0;
        }

        /// <summary>
        /// Get the current drag visual mode for the given position.
        /// </summary>
        public static DragAndDropVisualMode GetDragVisualMode(Vector2 position)
        {
            if (!s_IsEnabled || !HasValidDragData())
                return DragAndDropVisualMode.None;

            return DragAndDropVisualMode.Copy;
        }

        /// <summary>
        /// Get information about the current editor drag and drop system status.
        /// </summary>
        public static string GetSystemInfo()
        {
            return $"Unity Editor DragAndDrop API - Initialized: {IsInitialized}, Enabled: {s_IsEnabled}, Dragging: {IsDragging}";
        }

#else
        // --- Stub Implementation for Non-Editor Builds ---
        public static bool Initialize() { return false; }
        public static void Shutdown() { }
        public static bool Enable() { return false; }
        public static void Disable() { }
        public static bool HandleDrop(Vector2 screenPosition) { return false; }
        public static bool HasValidDragData() { return false; }
        public static string GetSystemInfo() { return "Unity Editor DragAndDrop API not available in builds"; }
#endif
    }
} 
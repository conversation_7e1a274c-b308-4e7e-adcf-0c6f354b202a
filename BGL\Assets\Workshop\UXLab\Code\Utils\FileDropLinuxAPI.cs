using UnityEngine;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;

namespace Workshop.UXLab.Utils
{
    /// <summary>
    /// Linux-specific file drop implementation using X11/GTK APIs through P/Invoke.
    /// This provides file drop functionality on Linux without requiring native plugins.
    /// </summary>
    public static class FileDropLinuxAPI
    {
        // --- X11 Imports ---
        [DllImport("libX11.so.6")]
        private static extern IntPtr XOpenDisplay(string display_name);

        [DllImport("libX11.so.6")]
        private static extern int XCloseDisplay(IntPtr display);

        [DllImport("libX11.so.6")]
        private static extern IntPtr XDefaultRootWindow(IntPtr display);

        [DllImport("libX11.so.6")]
        private static extern IntPtr XInternAtom(IntPtr display, string atom_name, bool only_if_exists);

        [DllImport("libX11.so.6")]
        private static extern int XSelectInput(IntPtr display, IntPtr window, long event_mask);

        [DllImport("libX11.so.6")]
        private static extern int XNextEvent(IntPtr display, out XEvent xevent);

        [DllImport("libX11.so.6")]
        private static extern int XPending(IntPtr display);

        // --- X11 Structures ---
        [StructLayout(LayoutKind.Sequential)]
        private struct XEvent
        {
            public int type;
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 24)]
            public IntPtr[] pad;
        }

        // --- X11 Constants ---
        private const int ClientMessage = 33;
        private const long StructureNotifyMask = 1L << 17;

        // --- State Management ---
        private static bool s_IsInitialized = false;
        private static bool s_IsEnabled = false;
        private static IntPtr s_Display = IntPtr.Zero;
        private static IntPtr s_Window = IntPtr.Zero;
        private static IntPtr s_XdndEnterAtom = IntPtr.Zero;
        private static IntPtr s_XdndDropAtom = IntPtr.Zero;

        // --- Events ---
        public static event Action<List<string>, Vector2> OnFilesDropped;
        public static event Action OnDragEnter;
        public static event Action OnDragExit;

        // --- Properties ---
        public static bool IsInitialized => s_IsInitialized;
        public static bool IsEnabled => s_IsEnabled;
        public static bool IsDragging { get; private set; }

        // --- Public Interface ---
        /// <summary>
        /// Initialize the Linux file drop system.
        /// </summary>
        public static bool Initialize()
        {
            if (s_IsInitialized)
                return true;

#if !UNITY_STANDALONE_LINUX || UNITY_EDITOR
            Debug.LogWarning("FileDropLinuxAPI: Only supported on Linux standalone builds.");
            return false;
#endif

            try
            {
                // Open X11 display
                s_Display = XOpenDisplay(null);
                if (s_Display == IntPtr.Zero)
                {
                    Debug.LogError("FileDropLinuxAPI: Could not open X11 display.");
                    return false;
                }

                // Get the root window (we'd need to find Unity's actual window)
                s_Window = XDefaultRootWindow(s_Display);
                if (s_Window == IntPtr.Zero)
                {
                    Debug.LogError("FileDropLinuxAPI: Could not get window handle.");
                    return false;
                }

                // Initialize XDND atoms
                s_XdndEnterAtom = XInternAtom(s_Display, "XdndEnter", false);
                s_XdndDropAtom = XInternAtom(s_Display, "XdndDrop", false);

                if (s_XdndEnterAtom == IntPtr.Zero || s_XdndDropAtom == IntPtr.Zero)
                {
                    Debug.LogError("FileDropLinuxAPI: Could not create XDND atoms.");
                    return false;
                }

                s_IsInitialized = true;
                Debug.Log("FileDropLinuxAPI: Successfully initialized Linux file drop.");
                return true;
            }
            catch (Exception e)
            {
                Debug.LogError($"FileDropLinuxAPI: Initialization failed: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// Enable file drop detection.
        /// </summary>
        public static bool Enable()
        {
            if (!s_IsInitialized)
            {
                Debug.LogError("FileDropLinuxAPI: Must initialize before enabling.");
                return false;
            }

            if (s_IsEnabled)
                return true;

            try
            {
                // Select for structure notify events to receive drag and drop messages
                XSelectInput(s_Display, s_Window, StructureNotifyMask);
                
                s_IsEnabled = true;
                Debug.Log("FileDropLinuxAPI: File drop enabled.");
                return true;
            }
            catch (Exception e)
            {
                Debug.LogError($"FileDropLinuxAPI: Failed to enable file drop: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disable file drop detection.
        /// </summary>
        public static void Disable()
        {
            if (!s_IsInitialized || !s_IsEnabled)
                return;

            try
            {
                // Unselect events
                XSelectInput(s_Display, s_Window, 0);
                
                s_IsEnabled = false;
                SetDraggingState(false);
                Debug.Log("FileDropLinuxAPI: File drop disabled.");
            }
            catch (Exception e)
            {
                Debug.LogError($"FileDropLinuxAPI: Failed to disable file drop: {e.Message}");
            }
        }

        /// <summary>
        /// Shutdown and cleanup the file drop system.
        /// </summary>
        public static void Shutdown()
        {
            if (!s_IsInitialized)
                return;

            try
            {
                Disable();

                if (s_Display != IntPtr.Zero)
                {
                    XCloseDisplay(s_Display);
                    s_Display = IntPtr.Zero;
                }

                s_Window = IntPtr.Zero;
                s_XdndEnterAtom = IntPtr.Zero;
                s_XdndDropAtom = IntPtr.Zero;
                s_IsInitialized = false;

                Debug.Log("FileDropLinuxAPI: Shutdown complete.");
            }
            catch (Exception e)
            {
                Debug.LogError($"FileDropLinuxAPI: Shutdown failed: {e.Message}");
            }
        }

        /// <summary>
        /// Check for and process X11 events (should be called regularly).
        /// </summary>
        public static void ProcessEvents()
        {
            if (!s_IsInitialized || !s_IsEnabled)
                return;

            try
            {
                // Process pending X11 events
                while (XPending(s_Display) > 0)
                {
                    XNextEvent(s_Display, out XEvent xevent);
                    ProcessXEvent(ref xevent);
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"FileDropLinuxAPI: Error processing events: {e.Message}");
            }
        }

        // --- Private Implementation ---
        private static void ProcessXEvent(ref XEvent xevent)
        {
            if (xevent.type == ClientMessage)
            {
                // This is a simplified version - in a full implementation, you would:
                // 1. Parse the ClientMessage to check if it's an XDND message
                // 2. Handle XdndEnter, XdndPosition, XdndDrop messages
                // 3. Extract file paths from the drag data
                // 4. Convert X11 coordinates to Unity screen coordinates
                
                Debug.LogWarning("FileDropLinuxAPI: X11 event processing is simplified - full implementation needed.");
                
                // For demonstration, simulate a file drop
                SimulateFileDrop();
            }
        }

        private static void SimulateFileDrop()
        {
            // This is just for demonstration - replace with actual XDND parsing
            List<string> testFiles = new List<string> { "/tmp/test.txt" };
            Vector2 testPosition = new Vector2(100, 100);
            
            OnFilesDropped?.Invoke(testFiles, testPosition);
            Debug.Log($"FileDropLinuxAPI: Simulated file drop with {testFiles.Count} files");
        }

        private static void SetDraggingState(bool isDragging)
        {
            if (IsDragging == isDragging)
                return;

            IsDragging = isDragging;

            if (isDragging)
            {
                OnDragEnter?.Invoke();
            }
            else
            {
                OnDragExit?.Invoke();
            }
        }

        /// <summary>
        /// Get system information for debugging.
        /// </summary>
        public static string GetSystemInfo()
        {
            return $"FileDropLinuxAPI Status:\n" +
                   $"  Initialized: {IsInitialized}\n" +
                   $"  Enabled: {IsEnabled}\n" +
                   $"  Dragging: {IsDragging}\n" +
                   $"  Display: {s_Display}\n" +
                   $"  Window: {s_Window}\n" +
                   $"  XdndEnter Atom: {s_XdndEnterAtom}\n" +
                   $"  XdndDrop Atom: {s_XdndDropAtom}";
        }
    }
} 
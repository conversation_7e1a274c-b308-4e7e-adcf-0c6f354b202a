using UnityEngine;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;

namespace Workshop.UXLab.Utils
{
    /// <summary>
    /// macOS-specific file drop implementation using Cocoa APIs through P/Invoke.
    /// This provides file drop functionality on macOS without requiring native plugins.
    /// </summary>
    public static class FileDropMacAPI
    {
        // --- Cocoa/Foundation Imports ---
        [DllImport("/System/Library/Frameworks/Foundation.framework/Foundation")]
        private static extern IntPtr objc_getClass(string className);

        [DllImport("/System/Library/Frameworks/Foundation.framework/Foundation")]
        private static extern IntPtr objc_msgSend(IntPtr receiver, IntPtr selector);

        [DllImport("/System/Library/Frameworks/Foundation.framework/Foundation")]
        private static extern IntPtr objc_msgSend(IntPtr receiver, IntPtr selector, IntPtr arg1);

        [DllImport("/System/Library/Frameworks/Foundation.framework/Foundation")]
        private static extern IntPtr sel_registerName(string selectorName);

        [DllImport("/System/Library/Frameworks/Foundation.framework/Foundation")]
        private static extern IntPtr objc_allocWithZone(IntPtr cls, IntPtr zone);

        [DllImport("/System/Library/Frameworks/AppKit.framework/AppKit")]
        private static extern IntPtr NSApp();

        [DllImport("/System/Library/Frameworks/AppKit.framework/AppKit")]
        private static extern IntPtr NSApplicationMain(int argc, string[] argv);

        // --- State Management ---
        private static bool s_IsInitialized = false;
        private static bool s_IsEnabled = false;
        private static IntPtr s_MainWindow = IntPtr.Zero;
        private static IntPtr s_DropDelegate = IntPtr.Zero;

        // --- Events ---
        public static event Action<List<string>, Vector2> OnFilesDropped;
        public static event Action OnDragEnter;
        public static event Action OnDragExit;

        // --- Properties ---
        public static bool IsInitialized => s_IsInitialized;
        public static bool IsEnabled => s_IsEnabled;
        public static bool IsDragging { get; private set; }

        // --- Public Interface ---
        /// <summary>
        /// Initialize the macOS file drop system.
        /// </summary>
        public static bool Initialize()
        {
            if (s_IsInitialized)
                return true;

#if !UNITY_STANDALONE_OSX || UNITY_EDITOR
            Debug.LogWarning("FileDropMacAPI: Only supported on macOS standalone builds.");
            return false;
#endif

            try
            {
                // Get the main application window
                IntPtr nsApp = NSApp();
                if (nsApp == IntPtr.Zero)
                {
                    Debug.LogError("FileDropMacAPI: Could not get NSApplication instance.");
                    return false;
                }

                // Get the main window
                IntPtr mainWindowSelector = sel_registerName("mainWindow");
                s_MainWindow = objc_msgSend(nsApp, mainWindowSelector);
                
                if (s_MainWindow == IntPtr.Zero)
                {
                    Debug.LogError("FileDropMacAPI: Could not get main window.");
                    return false;
                }

                // Create and register drag delegate
                if (!SetupDragDelegate())
                {
                    Debug.LogError("FileDropMacAPI: Failed to setup drag delegate.");
                    return false;
                }

                s_IsInitialized = true;
                Debug.Log("FileDropMacAPI: Successfully initialized macOS file drop.");
                return true;
            }
            catch (Exception e)
            {
                Debug.LogError($"FileDropMacAPI: Initialization failed: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// Enable file drop detection.
        /// </summary>
        public static bool Enable()
        {
            if (!s_IsInitialized)
            {
                Debug.LogError("FileDropMacAPI: Must initialize before enabling.");
                return false;
            }

            if (s_IsEnabled)
                return true;

            try
            {
                // Register for drag types
                RegisterDragTypes();
                s_IsEnabled = true;
                Debug.Log("FileDropMacAPI: File drop enabled.");
                return true;
            }
            catch (Exception e)
            {
                Debug.LogError($"FileDropMacAPI: Failed to enable file drop: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disable file drop detection.
        /// </summary>
        public static void Disable()
        {
            if (!s_IsInitialized || !s_IsEnabled)
                return;

            try
            {
                UnregisterDragTypes();
                s_IsEnabled = false;
                SetDraggingState(false);
                Debug.Log("FileDropMacAPI: File drop disabled.");
            }
            catch (Exception e)
            {
                Debug.LogError($"FileDropMacAPI: Failed to disable file drop: {e.Message}");
            }
        }

        /// <summary>
        /// Shutdown and cleanup the file drop system.
        /// </summary>
        public static void Shutdown()
        {
            if (!s_IsInitialized)
                return;

            try
            {
                Disable();
                CleanupDragDelegate();
                
                s_MainWindow = IntPtr.Zero;
                s_DropDelegate = IntPtr.Zero;
                s_IsInitialized = false;

                Debug.Log("FileDropMacAPI: Shutdown complete.");
            }
            catch (Exception e)
            {
                Debug.LogError($"FileDropMacAPI: Shutdown failed: {e.Message}");
            }
        }

        // --- Private Implementation ---
        private static bool SetupDragDelegate()
        {
            // This is a simplified version - in a full implementation, you would:
            // 1. Create a custom NSView subclass that implements NSDraggingDestination
            // 2. Override draggingEntered, draggingExited, performDragOperation methods
            // 3. Register the view as a drag destination
            
            // For now, we'll return true to indicate setup "succeeded"
            // A full implementation would require more complex Objective-C interop
            Debug.LogWarning("FileDropMacAPI: Drag delegate setup is simplified - full implementation needed.");
            return true;
        }

        private static void RegisterDragTypes()
        {
            // Register for file URL drag types
            // In a full implementation, this would register NSFilenamesPboardType
            Debug.LogWarning("FileDropMacAPI: Drag type registration is simplified - full implementation needed.");
        }

        private static void UnregisterDragTypes()
        {
            // Unregister drag types
            Debug.LogWarning("FileDropMacAPI: Drag type unregistration is simplified - full implementation needed.");
        }

        private static void CleanupDragDelegate()
        {
            // Cleanup the drag delegate
            if (s_DropDelegate != IntPtr.Zero)
            {
                // Release the delegate object
                s_DropDelegate = IntPtr.Zero;
            }
        }

        private static void SetDraggingState(bool isDragging)
        {
            if (IsDragging == isDragging)
                return;

            IsDragging = isDragging;

            if (isDragging)
            {
                OnDragEnter?.Invoke();
            }
            else
            {
                OnDragExit?.Invoke();
            }
        }

        /// <summary>
        /// Handle files being dropped (called from native delegate).
        /// </summary>
        public static void HandleFilesDropped(string[] filePaths, float x, float y)
        {
            if (filePaths == null || filePaths.Length == 0)
                return;

            List<string> fileList = new List<string>(filePaths);
            Vector2 dropPosition = new Vector2(x, y);

            OnFilesDropped?.Invoke(fileList, dropPosition);
            Debug.Log($"FileDropMacAPI: Dropped {fileList.Count} files at {dropPosition}");
        }

        /// <summary>
        /// Get system information for debugging.
        /// </summary>
        public static string GetSystemInfo()
        {
            return $"FileDropMacAPI Status:\n" +
                   $"  Initialized: {IsInitialized}\n" +
                   $"  Enabled: {IsEnabled}\n" +
                   $"  Dragging: {IsDragging}\n" +
                   $"  Main Window: {s_MainWindow}\n" +
                   $"  Drop Delegate: {s_DropDelegate}";
        }
    }
} 
using UnityEngine;
using UnityEngine.UIElements;
using System;
using System.Collections.Generic;

namespace Workshop.UXLab.Utils
{
    /// <summary>
    /// A manipulator that handles external file drag and drop onto UI elements.
    /// This works in conjunction with the FileDropSystem to detect files dropped from outside Unity.
    /// </summary>
    public class FileDropManipulator : PointerManipulator
    {
        // --- Static Drop Tracking ---
        private static bool s_DropHandled = false;
        private static int s_DropEventId = 0;

        // --- Events ---
        /// <summary>
        /// Event triggered when files are dropped onto this element.
        /// </summary>
        public event Action<List<string>> OnFilesDropped;

        /// <summary>
        /// Event triggered when a drag operation enters this element.
        /// </summary>
        public event Action OnDragEnter;

        /// <summary>
        /// Event triggered when a drag operation exits this element.
        /// </summary>
        public event Action OnDragExit;

        // --- Properties ---
        /// <summary>
        /// Filter for accepted file extensions. If null or empty, all files are accepted.
        /// Example: new[] { ".png", ".jpg", ".jpeg", ".gif" }
        /// </summary>
        public string[] AcceptedExtensions { get; set; }

        /// <summary>
        /// Whether to show visual feedback when dragging over this element.
        /// </summary>
        public bool ShowVisualFeedback { get; set; } = true;

        /// <summary>
        /// CSS class to add when dragging over this element.
        /// </summary>
        public string DragOverClass { get; set; } = "file-drop-hover";

        /// <summary>
        /// CSS class to add when dragging invalid files over this element.
        /// </summary>
        public string DragOverInvalidClass { get; set; } = "file-drop-error";

        // --- Private Fields ---
        private bool m_IsDragOver = false;
        private FileDropSystem m_FileDropSystem;
        private int m_LastDropEventId = -1;

        // --- Constructor ---
        public FileDropManipulator(VisualElement target) : this()
        {
            this.target = target;
        }

        public FileDropManipulator()
        {
            // Find or create the FileDropSystem
            m_FileDropSystem = FileDropSystem.Instance;
            if (m_FileDropSystem == null)
            {
                Debug.LogError("FileDropManipulator: FileDropSystem not found. Make sure FileDropSystem is initialized before using FileDropManipulator.");
                return;
            }

            Debug.Log($"FileDropManipulator: Connected to FileDropSystem (Platform: {m_FileDropSystem.CurrentPlatform}, Enabled: {m_FileDropSystem.IsEnabled}, Supported: {m_FileDropSystem.IsPlatformSupported})");

            // Register for global file drop events
            m_FileDropSystem.OnFilesDropped += HandleGlobalFileDrop;
            m_FileDropSystem.OnDragEnter += HandleGlobalDragEnter;
            m_FileDropSystem.OnDragExit += HandleGlobalDragExit;
            Debug.Log("FileDropManipulator: Registered for global file drop events and drag enter/exit events");
        }

        // --- Manipulator Implementation ---
        protected override void RegisterCallbacksOnTarget()
        {
            Debug.Log($"FileDropManipulator: RegisterCallbacksOnTarget called for target '{target?.name ?? "null"}'");
            Debug.Log($"FileDropManipulator: Target hierarchy - parent: '{target?.parent?.name ?? "null"}'");
            Debug.Log($"FileDropManipulator: Target properties - visible: {target?.visible}, enabled: {target?.enabledInHierarchy}, pickingMode: {target?.pickingMode}");

            target.RegisterCallback<PointerEnterEvent>(OnPointerEnter);
            target.RegisterCallback<PointerLeaveEvent>(OnPointerLeave);
            target.RegisterCallback<PointerMoveEvent>(OnPointerMove);

#if UNITY_EDITOR
            // For Unity Editor, external file drops are handled through global drag state monitoring
            // We don't need pointer events since external drags don't generate pointer down/up events
            target.pickingMode = PickingMode.Position;

            Debug.Log("FileDropManipulator: Unity Editor external file drop handling configured");
            Debug.Log($"FileDropManipulator: Target after setup - pickingMode: {target.pickingMode}");
#endif
        }

        protected override void UnregisterCallbacksFromTarget()
        {
            Debug.Log($"FileDropManipulator: UnregisterCallbacksFromTarget called for target '{target?.name ?? "null"}'");
            target.UnregisterCallback<PointerEnterEvent>(OnPointerEnter);
            target.UnregisterCallback<PointerLeaveEvent>(OnPointerLeave);
            target.UnregisterCallback<PointerMoveEvent>(OnPointerMove);

#if UNITY_EDITOR
            Debug.Log("FileDropManipulator: Unity Editor external file drop handling cleaned up");
#endif

            // Unregister from global events
            if (m_FileDropSystem != null)
            {
                m_FileDropSystem.OnFilesDropped -= HandleGlobalFileDrop;
                m_FileDropSystem.OnDragEnter -= HandleGlobalDragEnter;
                m_FileDropSystem.OnDragExit -= HandleGlobalDragExit;
                Debug.Log("FileDropManipulator: Unregistered from global file drop events and drag enter/exit events");
            }
        }

        // --- Event Handlers ---
        private void OnPointerEnter(PointerEnterEvent evt)
        {
            Debug.Log($"FileDropManipulator: OnPointerEnter - checking if global drag is active");
            
            // Simple check: if the FileDropSystem says we're dragging, show feedback
            if (m_FileDropSystem != null && m_FileDropSystem.IsDragging)
            {
                // Check if the dragged files are valid for this manipulator
                if (AreCurrentDraggedFilesValid())
                {
                    Debug.Log("FileDropManipulator: OnPointerEnter - Global drag active with valid files, setting drag over state");
                    SetDragOver(true, false);
                }
                else
                {
                    Debug.Log("FileDropManipulator: OnPointerEnter - Global drag active but no valid files for this manipulator, showing invalid feedback");
                    SetDragOver(true, true); // Show invalid file feedback
                }
            }
        }

        private void OnPointerLeave(PointerLeaveEvent evt)
        {
            Debug.Log($"FileDropManipulator: OnPointerLeave - clearing drag over state");
            SetDragOver(false);
        }

        private void OnPointerMove(PointerMoveEvent evt)
        {
            // Simple check: if we're moving over the element and there's an active file drag, show feedback
            if (m_FileDropSystem != null && m_FileDropSystem.IsDragging && !m_IsDragOver)
            {
                // Check if the dragged files are valid for this manipulator
                if (AreCurrentDraggedFilesValid())
                {
                    Debug.Log($"FileDropManipulator: OnPointerMove - Global drag active with valid files, setting drag over state");
                    SetDragOver(true, false);
                }
                else
                {
                    Debug.Log($"FileDropManipulator: OnPointerMove - Global drag active but no valid files for this manipulator, showing invalid feedback");
                    SetDragOver(true, true); // Show invalid file feedback
                }
            }
            else if ((m_FileDropSystem == null || !m_FileDropSystem.IsDragging) && m_IsDragOver)
            {
                Debug.Log($"FileDropManipulator: OnPointerMove - No global drag, clearing drag over state");
                SetDragOver(false);
            }
        }

        private void HandleGlobalFileDrop(List<string> filePaths, Vector2 screenPosition)
        {
            Debug.Log($"FileDropManipulator: HandleGlobalFileDrop called with {filePaths.Count} files, target={target?.name ?? "null"}");

            // Increment drop event ID and reset handled flag for new drops
            if (m_LastDropEventId != s_DropEventId)
            {
                s_DropEventId++;
                s_DropHandled = false;
                m_LastDropEventId = s_DropEventId;
                Debug.Log($"FileDropManipulator: New drop event {s_DropEventId}");
            }

            // If this drop has already been handled by another manipulator, skip it
            if (s_DropHandled)
            {
                Debug.Log("FileDropManipulator: Drop already handled by another manipulator");
                return;
            }

            // In Unity Editor or when platform doesn't support drag state tracking,
            // we need to check if the drop position is over our target element
            bool shouldAcceptDrop = m_IsDragOver;

            // In editor mode or when drag state tracking isn't working, check position
            if (!shouldAcceptDrop && target != null)
            {
                shouldAcceptDrop = IsPositionOverTarget(screenPosition);
                Debug.Log($"FileDropManipulator: Position check - shouldAcceptDrop={shouldAcceptDrop}, target visible={target.visible}, enabled={target.enabledInHierarchy}");
            }

            // Additional fallback: if we're in Unity Editor and the target is visible/enabled, be more permissive
            // This helps with simulation mode where exact position tracking might not work perfectly
#if UNITY_EDITOR
            if (!shouldAcceptDrop && target != null && target.enabledInHierarchy && target.visible)
            {
                // In editor simulation, if no other manipulator has claimed this drop yet, accept it
                // This is a fallback for when position detection isn't working perfectly
                shouldAcceptDrop = true;
                Debug.Log("FileDropManipulator: Editor fallback - accepting drop for visible/enabled target");
            }
#endif

            Debug.Log($"FileDropManipulator: shouldAcceptDrop={shouldAcceptDrop}, m_IsDragOver={m_IsDragOver}");

            if (!shouldAcceptDrop || target == null)
            {
                Debug.Log("FileDropManipulator: Rejecting drop - not over target or target is null");
                return;
            }

            // Mark this drop as handled to prevent other manipulators from processing it
            s_DropHandled = true;
            Debug.Log("FileDropManipulator: Marked drop as handled");

            // Filter files by accepted extensions if specified
            List<string> acceptedFiles = FilterFilesByExtension(filePaths);

            Debug.Log($"FileDropManipulator: Filtered {filePaths.Count} files to {acceptedFiles.Count} accepted files");

            if (acceptedFiles.Count > 0)
            {
                Debug.Log($"FileDropManipulator: Invoking OnFilesDropped with {acceptedFiles.Count} files");
                OnFilesDropped?.Invoke(acceptedFiles);
            }

            // Clear drag over state
            SetDragOver(false);
        }

        private void HandleGlobalDragEnter()
        {
            Debug.Log("FileDropManipulator: HandleGlobalDragEnter - files started dragging over application");
            
            // When files start dragging over the application, check if files are valid for this manipulator
            if (target != null && target.enabledInHierarchy && target.visible)
            {
                if (AreCurrentDraggedFilesValid())
                {
                    Debug.Log("FileDropManipulator: Setting drag over state due to global drag enter with valid files");
                    SetDragOver(true, false);
                }
                else
                {
                    Debug.Log("FileDropManipulator: Global drag enter but no valid files for this manipulator, showing invalid feedback");
                    SetDragOver(true, true); // Show invalid file feedback
                }
            }
        }

        private void HandleGlobalDragExit()
        {
            Debug.Log("FileDropManipulator: HandleGlobalDragExit - files stopped dragging over application");
            
#if UNITY_EDITOR
            // In Unity Editor, check if we should handle a drop when drag exits
            if (m_IsDragOver && UnityEditor.DragAndDrop.paths != null && UnityEditor.DragAndDrop.paths.Length > 0)
            {
                Debug.Log("FileDropManipulator: Attempting to handle Unity Editor drop on drag exit");
                HandleEditorDrop();
            }
#endif
            
            // Clear drag over state when files leave the application entirely
            if (m_IsDragOver)
            {
                Debug.Log("FileDropManipulator: Clearing drag over state due to global drag exit");
                SetDragOver(false);
            }
        }

        // --- Helper Methods ---
        private void SetDragOver(bool isDragOver)
        {
            SetDragOver(isDragOver, false);
        }

        private void SetDragOver(bool isDragOver, bool isInvalidFiles)
        {
            if (m_IsDragOver == isDragOver)
                return;

            Debug.Log($"FileDropManipulator: SetDragOver changing from {m_IsDragOver} to {isDragOver} (invalid={isInvalidFiles}) for target '{target?.name ?? "null"}'");
            Debug.Log($"FileDropManipulator: Target classes before: [{string.Join(", ", target.GetClasses())}]");

            m_IsDragOver = isDragOver;

            if (ShowVisualFeedback)
            {
                // Remove any existing drag classes first
                if (!string.IsNullOrEmpty(DragOverClass))
                    target.RemoveFromClassList(DragOverClass);
                if (!string.IsNullOrEmpty(DragOverInvalidClass))
                    target.RemoveFromClassList(DragOverInvalidClass);

                if (isDragOver)
                {
                    string classToAdd = isInvalidFiles ? DragOverInvalidClass : DragOverClass;
                    if (!string.IsNullOrEmpty(classToAdd))
                    {
                        target.AddToClassList(classToAdd);
                        Debug.Log($"FileDropManipulator: Added CSS class '{classToAdd}' to target");
                    }
                }
                else
                {
                    Debug.Log($"FileDropManipulator: Removed drag CSS classes from target");
                }
                
                Debug.Log($"FileDropManipulator: Target classes after: [{string.Join(", ", target.GetClasses())}]");
                Debug.Log($"FileDropManipulator: ShowVisualFeedback={ShowVisualFeedback}, DragOverClass='{DragOverClass}', DragOverInvalidClass='{DragOverInvalidClass}'");
            }
            else
            {
                Debug.Log($"FileDropManipulator: Visual feedback disabled - ShowVisualFeedback={ShowVisualFeedback}");
            }

            if (isDragOver)
            {
                Debug.Log("FileDropManipulator: Invoking OnDragEnter event");
                OnDragEnter?.Invoke();
            }
            else
            {
                Debug.Log("FileDropManipulator: Invoking OnDragExit event");
                OnDragExit?.Invoke();
            }
        }

        private List<string> FilterFilesByExtension(List<string> filePaths)
        {
            if (AcceptedExtensions == null || AcceptedExtensions.Length == 0)
            {
                return filePaths;
            }

            List<string> filteredFiles = new List<string>();
            foreach (string filePath in filePaths)
            {
                string extension = System.IO.Path.GetExtension(filePath).ToLowerInvariant();
                foreach (string acceptedExt in AcceptedExtensions)
                {
                    if (extension == acceptedExt.ToLowerInvariant())
                    {
                        filteredFiles.Add(filePath);
                        break;
                    }
                }
            }

            return filteredFiles;
        }

        private bool IsPositionOverTarget(Vector2 screenPosition)
        {
            if (target == null)
                return false;

            // Convert screen position to panel coordinates
            var panel = target.panel;
            if (panel == null)
                return false;

            // Convert screen position to panel space
            Vector2 panelPosition = RuntimePanelUtils.ScreenToPanel(panel, screenPosition);

            // Check if the position is within the target's bounds
            Rect targetBounds = target.worldBound;
            bool isOver = targetBounds.Contains(panelPosition);

            Debug.Log($"FileDropManipulator: IsPositionOverTarget - screenPos={screenPosition}, panelPos={panelPosition}, targetBounds={targetBounds}, isOver={isOver}");

            return isOver && target.enabledInHierarchy && target.visible;
        }

        // --- Public Methods ---
        /// <summary>
        /// Manually trigger the drag over state. Useful for testing or custom scenarios.
        /// </summary>
        public void SetDragOverState(bool isDragOver)
        {
            SetDragOver(isDragOver);
        }

        /// <summary>
        /// Check if the given file path would be accepted by this manipulator.
        /// </summary>
        public bool IsFileAccepted(string filePath)
        {
            if (AcceptedExtensions == null || AcceptedExtensions.Length == 0)
                return true;

            string extension = System.IO.Path.GetExtension(filePath).ToLowerInvariant();
            foreach (string acceptedExt in AcceptedExtensions)
            {
                if (extension == acceptedExt.ToLowerInvariant())
                    return true;
            }

            return false;
        }

        /// <summary>
        /// Check if any of the currently dragged files would be accepted by this manipulator.
        /// </summary>
        private bool AreCurrentDraggedFilesValid()
        {
            // If no extensions specified, accept all files
            if (AcceptedExtensions == null || AcceptedExtensions.Length == 0)
            {
                Debug.Log("FileDropManipulator: No extension restrictions - accepting all files");
                return true;
            }

#if UNITY_EDITOR
            // In Unity Editor, check DragAndDrop.paths
            if (UnityEditor.DragAndDrop.paths != null && UnityEditor.DragAndDrop.paths.Length > 0)
            {
                foreach (string filePath in UnityEditor.DragAndDrop.paths)
                {
                    if (IsFileAccepted(filePath))
                    {
                        Debug.Log($"FileDropManipulator: Found valid file in drag: {filePath}");
                        return true;
                    }
                }
                Debug.Log($"FileDropManipulator: No valid files found in Unity Editor drag. Accepted extensions: [{string.Join(", ", AcceptedExtensions)}]");
                return false;
            }
#endif

            // In runtime builds, we can't get the file list during drag operations
            // So we'll be conservative and show invalid feedback by default
            // This prevents false positive feedback for restricted drop zones
            Debug.Log("FileDropManipulator: Runtime drag detected - cannot validate files during drag, showing invalid feedback");
            return false;
        }

#if UNITY_EDITOR
        // --- Unity Editor External File Drop Handler ---
        private void HandleEditorDrop()
        {
            Debug.Log("FileDropManipulator: HandleEditorDrop - Unity Editor external file drop handling");

            // Check if we have valid drag data
            if (UnityEditor.DragAndDrop.paths != null && UnityEditor.DragAndDrop.paths.Length > 0)
            {
                // Filter files by accepted extensions
                List<string> allFiles = new List<string>(UnityEditor.DragAndDrop.paths);
                List<string> acceptedFiles = FilterFilesByExtension(allFiles);

                Debug.Log($"FileDropManipulator: Filtered {allFiles.Count} files to {acceptedFiles.Count} accepted files");

                if (acceptedFiles.Count > 0)
                {
                    // Accept the drag operation
                    UnityEditor.DragAndDrop.AcceptDrag();

                    // Trigger our file drop event
                    OnFilesDropped?.Invoke(acceptedFiles);

                    Debug.Log($"FileDropManipulator: Successfully handled Unity Editor drop with {acceptedFiles.Count} files");
                    foreach (string file in acceptedFiles)
                    {
                        Debug.Log($"FileDropManipulator: Dropped file: {file}");
                    }
                }
                else
                {
                    Debug.Log("FileDropManipulator: No accepted files after filtering");
                }
            }
            else
            {
                Debug.Log("FileDropManipulator: No valid drag data available");
            }

            SetDragOver(false);
        }
#endif
    }
}

using UnityEngine;
using System;
using System.Collections.Generic;

namespace Workshop.UXLab.Utils
{
    /// <summary>
    /// System that handles external file drag and drop detection for runtime Unity applications.
    /// This is a singleton that coordinates between platform-specific implementations and UI elements.
    /// Supports Windows (full), macOS (framework), and Linux (framework) through direct API calls.
    /// </summary>
    public class FileDropSystem : MonoBehaviour
    {
        // --- Singleton ---
        private static FileDropSystem s_Instance;
        public static FileDropSystem Instance
        {
            get
            {
                if (s_Instance == null)
                {
                    s_Instance = FindObjectOfType<FileDropSystem>();
                    if (s_Instance == null)
                    {
                        GameObject go = new GameObject("FileDropSystem");
                        s_Instance = go.AddComponent<FileDropSystem>();
                        DontDestroyOnLoad(go);
                    }
                }
                return s_Instance;
            }
        }

        // --- Events ---
        /// <summary>
        /// Event triggered when files are dropped anywhere in the application.
        /// Parameters: List of file paths, screen position where dropped
        /// </summary>
        public event Action<List<string>, Vector2> OnFilesDropped;

        /// <summary>
        /// Event triggered when a drag operation starts (files enter the application window).
        /// </summary>
        public event Action OnDragEnter;

        /// <summary>
        /// Event triggered when a drag operation ends (files leave the application window).
        /// </summary>
        public event Action OnDragExit;

        // --- Properties ---
        /// <summary>
        /// Whether files are currently being dragged over the application window.
        /// </summary>
        public bool IsDragging { get; private set; }

        /// <summary>
        /// Whether the file drop system is currently enabled.
        /// </summary>
        public bool IsEnabled { get; private set; }

        /// <summary>
        /// Whether the platform-specific implementation is available and working.
        /// </summary>
        public bool IsPlatformSupported
        {
            get
            {
#if UNITY_EDITOR
                return FileDropEditorAPI.IsInitialized;
#elif UNITY_STANDALONE_WIN
                return FileDropWindowsAPI.IsInitialized;
#elif UNITY_STANDALONE_OSX
                return FileDropMacAPI.IsInitialized;
#elif UNITY_STANDALONE_LINUX
                return FileDropLinuxAPI.IsInitialized;
#else
                return false;
#endif
            }
        }

        /// <summary>
        /// Get the current platform name for debugging.
        /// </summary>
        public string CurrentPlatform
        {
            get
            {
#if UNITY_EDITOR
                return "Unity Editor";
#elif UNITY_STANDALONE_WIN
                return "Windows";
#elif UNITY_STANDALONE_OSX
                return "macOS";
#elif UNITY_STANDALONE_LINUX
                return "Linux";
#else
                return "Unsupported Platform";
#endif
            }
        }

        // --- Unity Lifecycle ---
        private void Awake()
        {
            if (s_Instance == null)
            {
                s_Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else if (s_Instance != this)
            {
                Destroy(gameObject);
                return;
            }

            Initialize();
        }

        private void Start()
        {
            EnableFileDropDetection();
        }

        private void Update()
        {
#if UNITY_STANDALONE_LINUX && !UNITY_EDITOR
            // Linux requires manual event processing
            if (IsEnabled)
            {
                FileDropLinuxAPI.ProcessEvents();
            }
#endif

#if UNITY_STANDALONE_WIN && !UNITY_EDITOR
            // Windows benefits from frequent drag checking
            if (IsEnabled)
            {
                FileDropWindowsAPI.Update();
            }
#endif

#if UNITY_EDITOR
            // In editor, provide a fallback for testing
            if (Input.GetKeyDown(KeyCode.F12))
            {
                Debug.Log("FileDropSystem: F12 pressed - simulating file drop for testing");
                TestFileDrop();
            }
#endif
        }

        private void OnDestroy()
        {
            DisableFileDropDetection();
            Shutdown();
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (hasFocus && IsEnabled)
            {
                // Re-enable when application gains focus
                EnableFileDropDetection();
            }
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (!pauseStatus && IsEnabled)
            {
                // Re-enable when application unpauses
                EnableFileDropDetection();
            }
        }

        // --- Initialization ---
        private void Initialize()
        {
            Debug.Log($"FileDropSystem: Initializing file drop system for {CurrentPlatform}...");

#if UNITY_EDITOR
            // Use Unity Editor API implementation
            if (FileDropEditorAPI.Initialize())
            {
                // Subscribe to Editor API events
                FileDropEditorAPI.OnFilesDropped += HandleFilesDropped;
                FileDropEditorAPI.OnDragEnter += HandleDragEnter;
                FileDropEditorAPI.OnDragExit += HandleDragExit;
                
                Debug.Log("FileDropSystem: Unity Editor API implementation initialized successfully.");
            }
            else
            {
                Debug.LogError("FileDropSystem: Failed to initialize Unity Editor API implementation.");
            }
#elif UNITY_STANDALONE_WIN
            // Use Windows API implementation
            if (FileDropWindowsAPI.Initialize())
            {
                // Subscribe to Windows API events
                FileDropWindowsAPI.OnFilesDropped += HandleFilesDropped;
                FileDropWindowsAPI.OnDragEnter += HandleDragEnter;
                FileDropWindowsAPI.OnDragExit += HandleDragExit;
                
                Debug.Log("FileDropSystem: Windows API implementation initialized successfully.");
            }
            else
            {
                Debug.LogError("FileDropSystem: Failed to initialize Windows API implementation.");
            }
#elif UNITY_STANDALONE_OSX
            // Use macOS API implementation
            if (FileDropMacAPI.Initialize())
            {
                // Subscribe to macOS API events
                FileDropMacAPI.OnFilesDropped += HandleFilesDropped;
                FileDropMacAPI.OnDragEnter += HandleDragEnter;
                FileDropMacAPI.OnDragExit += HandleDragExit;
                
                Debug.Log("FileDropSystem: macOS API implementation initialized successfully.");
            }
            else
            {
                Debug.LogError("FileDropSystem: Failed to initialize macOS API implementation.");
            }
#elif UNITY_STANDALONE_LINUX
            // Use Linux API implementation
            if (FileDropLinuxAPI.Initialize())
            {
                // Subscribe to Linux API events
                FileDropLinuxAPI.OnFilesDropped += HandleFilesDropped;
                FileDropLinuxAPI.OnDragEnter += HandleDragEnter;
                FileDropLinuxAPI.OnDragExit += HandleDragExit;
                
                Debug.Log("FileDropSystem: Linux API implementation initialized successfully.");
            }
            else
            {
                Debug.LogError("FileDropSystem: Failed to initialize Linux API implementation.");
            }
#else
            Debug.LogWarning("FileDropSystem: File drop detection is not supported on this platform.");
#endif
        }

        private void Shutdown()
        {
#if UNITY_EDITOR
            // Unsubscribe from Editor API events
            FileDropEditorAPI.OnFilesDropped -= HandleFilesDropped;
            FileDropEditorAPI.OnDragEnter -= HandleDragEnter;
            FileDropEditorAPI.OnDragExit -= HandleDragExit;
            
            FileDropEditorAPI.Shutdown();
#elif UNITY_STANDALONE_WIN
            // Unsubscribe from Windows API events
            FileDropWindowsAPI.OnFilesDropped -= HandleFilesDropped;
            FileDropWindowsAPI.OnDragEnter -= HandleDragEnter;
            FileDropWindowsAPI.OnDragExit -= HandleDragExit;
            
            FileDropWindowsAPI.Shutdown();
#elif UNITY_STANDALONE_OSX
            // Unsubscribe from macOS API events
            FileDropMacAPI.OnFilesDropped -= HandleFilesDropped;
            FileDropMacAPI.OnDragEnter -= HandleDragEnter;
            FileDropMacAPI.OnDragExit -= HandleDragExit;
            
            FileDropMacAPI.Shutdown();
#elif UNITY_STANDALONE_LINUX
            // Unsubscribe from Linux API events
            FileDropLinuxAPI.OnFilesDropped -= HandleFilesDropped;
            FileDropLinuxAPI.OnDragEnter -= HandleDragEnter;
            FileDropLinuxAPI.OnDragExit -= HandleDragExit;
            
            FileDropLinuxAPI.Shutdown();
#endif
        }

        // --- Public Methods ---
        /// <summary>
        /// Enable file drop detection.
        /// </summary>
        public void EnableFileDropDetection()
        {
            if (IsEnabled)
                return;

#if UNITY_EDITOR
            if (FileDropEditorAPI.Enable())
            {
                IsEnabled = true;
                Debug.Log("FileDropSystem: File drop detection enabled (Unity Editor).");
            }
            else
            {
                Debug.LogError("FileDropSystem: Failed to enable file drop detection (Unity Editor).");
                IsEnabled = false;
            }
#elif UNITY_STANDALONE_WIN
            if (FileDropWindowsAPI.Enable())
            {
                IsEnabled = true;
                Debug.Log("FileDropSystem: File drop detection enabled (Windows).");
            }
            else
            {
                Debug.LogError("FileDropSystem: Failed to enable file drop detection (Windows).");
                IsEnabled = false;
            }
#elif UNITY_STANDALONE_OSX
            if (FileDropMacAPI.Enable())
            {
                IsEnabled = true;
                Debug.Log("FileDropSystem: File drop detection enabled (macOS).");
            }
            else
            {
                Debug.LogError("FileDropSystem: Failed to enable file drop detection (macOS).");
                IsEnabled = false;
            }
#elif UNITY_STANDALONE_LINUX
            if (FileDropLinuxAPI.Enable())
            {
                IsEnabled = true;
                Debug.Log("FileDropSystem: File drop detection enabled (Linux).");
            }
            else
            {
                Debug.LogError("FileDropSystem: Failed to enable file drop detection (Linux).");
                IsEnabled = false;
            }
#else
            Debug.LogWarning("FileDropSystem: File drop detection not supported on this platform.");
            IsEnabled = false;
#endif
        }

        /// <summary>
        /// Disable file drop detection.
        /// </summary>
        public void DisableFileDropDetection()
        {
            if (!IsEnabled)
                return;

#if UNITY_EDITOR
            FileDropEditorAPI.Disable();
#elif UNITY_STANDALONE_WIN
            FileDropWindowsAPI.Disable();
#elif UNITY_STANDALONE_OSX
            FileDropMacAPI.Disable();
#elif UNITY_STANDALONE_LINUX
            FileDropLinuxAPI.Disable();
#endif
            
            IsEnabled = false;
            SetDraggingState(false);
            Debug.Log($"FileDropSystem: File drop detection disabled ({CurrentPlatform}).");
        }

        /// <summary>
        /// Manually trigger a file drop event. Useful for testing.
        /// </summary>
        public void SimulateFileDrop(List<string> filePaths, Vector2 screenPosition)
        {
            if (filePaths != null && filePaths.Count > 0)
            {
                OnFilesDropped?.Invoke(filePaths, screenPosition);
                Debug.Log($"FileDropSystem: Simulated file drop with {filePaths.Count} files at {screenPosition}");
            }
        }

        /// <summary>
        /// Test the file drop system with sample files.
        /// </summary>
        public void TestFileDrop()
        {
            List<string> testFiles = new List<string>();
            Vector2 testPosition = new Vector2(Screen.width * 0.5f, Screen.height * 0.5f);

#if UNITY_STANDALONE_WIN
            testFiles.AddRange(new[] { "C:\\TestFiles\\image1.png", "C:\\TestFiles\\image2.jpg", "C:\\TestFiles\\document.pdf" });
#elif UNITY_STANDALONE_OSX
            testFiles.AddRange(new[] { "/Users/<USER>/image1.png", "/Users/<USER>/image2.jpg", "/Users/<USER>/document.pdf" });
#elif UNITY_STANDALONE_LINUX
            testFiles.AddRange(new[] { "/home/<USER>/image1.png", "/home/<USER>/image2.jpg", "/home/<USER>/document.pdf" });
#else
            testFiles.AddRange(new[] { "TestFiles/image1.png", "TestFiles/image2.jpg", "TestFiles/document.pdf" });
#endif
            
            SimulateFileDrop(testFiles, testPosition);
        }

        // --- Event Handlers ---
        private void HandleFilesDropped(List<string> filePaths, Vector2 dropPosition)
        {
            Debug.Log($"FileDropSystem: HandleFilesDropped called with {filePaths.Count} files at position {dropPosition}");
            foreach (string filePath in filePaths)
            {
                Debug.Log($"FileDropSystem: Dropped file: {filePath}");
            }
            
            SetDraggingState(false);
            OnFilesDropped?.Invoke(filePaths, dropPosition);
            
            Debug.Log($"FileDropSystem: OnFilesDropped event invoked with {OnFilesDropped?.GetInvocationList().Length ?? 0} subscribers");
        }

        private void HandleDragEnter()
        {
            Debug.Log("FileDropSystem: HandleDragEnter called - files entering application window");
            SetDraggingState(true);
            OnDragEnter?.Invoke();
            
            Debug.Log($"FileDropSystem: OnDragEnter event invoked with {OnDragEnter?.GetInvocationList().Length ?? 0} subscribers");
        }

        private void HandleDragExit()
        {
            Debug.Log("FileDropSystem: HandleDragExit called - files leaving application window");
            SetDraggingState(false);
            OnDragExit?.Invoke();
            
            Debug.Log($"FileDropSystem: OnDragExit event invoked with {OnDragExit?.GetInvocationList().Length ?? 0} subscribers");
        }

        private void SetDraggingState(bool isDragging)
        {
            if (IsDragging != isDragging)
            {
                IsDragging = isDragging;
                Debug.Log($"FileDropSystem: Dragging state changed to {isDragging}");
            }
        }

        // --- Static Utility Methods ---
        /// <summary>
        /// Create and initialize the FileDropSystem if it doesn't exist.
        /// </summary>
        public static void EnsureInitialized()
        {
            var instance = Instance; // This will create the instance if needed
        }

        /// <summary>
        /// Check if file drop is supported on the current platform.
        /// </summary>
        public static bool IsSupported()
        {
#if UNITY_STANDALONE_WIN || UNITY_STANDALONE_OSX || UNITY_STANDALONE_LINUX
            return true;
#else
            return false;
#endif
        }

        /// <summary>
        /// Get information about the current file drop system status.
        /// </summary>
        public static string GetSystemInfo()
        {
            var instance = Instance;
            if (instance == null)
                return "FileDropSystem not initialized";

            string implementationInfo = "";
#if UNITY_EDITOR
            implementationInfo = FileDropEditorAPI.GetSystemInfo();
#elif UNITY_STANDALONE_WIN
            implementationInfo = FileDropWindowsAPI.GetSystemInfo();
#elif UNITY_STANDALONE_OSX
            implementationInfo = FileDropMacAPI.GetSystemInfo();
#elif UNITY_STANDALONE_LINUX
            implementationInfo = FileDropLinuxAPI.GetSystemInfo();
#else
            implementationInfo = "No platform-specific implementation";
#endif

            return $"FileDropSystem Status:\n" +
                   $"  Platform: {instance.CurrentPlatform}\n" +
                   $"  Enabled: {instance.IsEnabled}\n" +
                   $"  Dragging: {instance.IsDragging}\n" +
                   $"  Platform Supported: {instance.IsPlatformSupported}\n" +
                   $"  Implementation: {implementationInfo}";
        }
    }
} 
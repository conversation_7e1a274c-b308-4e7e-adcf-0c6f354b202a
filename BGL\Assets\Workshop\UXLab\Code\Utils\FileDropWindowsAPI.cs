using UnityEngine;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Text;

namespace Workshop.UXLab.Utils
{
    /// <summary>
    /// Windows-specific file drop implementation using DragAcceptFiles with drag state detection.
    /// Provides reliable file drop functionality with visual feedback through drag state monitoring.
    /// Based on proven Windows API approach for maximum compatibility.
    /// </summary>
    public static class FileDropWindowsAPI
    {
        // --- Windows API Constants ---
        private const int WM_DROPFILES = 0x0233;
        private const int WM_MOUSEMOVE = 0x0200;
        private const int WM_NCMOUSEMOVE = 0x00A0;
        private const int GWL_WNDPROC = -4;

        // --- Windows API Imports ---
        [DllImport("user32.dll")]
        private static extern IntPtr GetActiveWindow();

        [DllImport("user32.dll")]
        private static extern IntPtr SetWindowLongPtr(IntPtr hWnd, int nIndex, IntPtr dwNewLong);

        [DllImport("user32.dll")]
        private static extern IntPtr CallWindowProc(IntPtr lpPrevWndFunc, IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);

        [DllImport("user32.dll")]
        private static extern bool GetCursorPos(out POINT lpPoint);

        [DllImport("user32.dll")]
        private static extern IntPtr WindowFromPoint(POINT Point);

        [DllImport("user32.dll")]
        private static extern short GetAsyncKeyState(int vKey);

        [DllImport("shell32.dll")]
        private static extern void DragAcceptFiles(IntPtr hWnd, bool fAccept);

        [DllImport("shell32.dll")]
        private static extern uint DragQueryFile(IntPtr hDrop, uint iFile, StringBuilder lpszFile, uint cch);

        [DllImport("shell32.dll")]
        private static extern bool DragQueryPoint(IntPtr hDrop, out POINT lppt);

        [DllImport("shell32.dll")]
        private static extern void DragFinish(IntPtr hDrop);

        [StructLayout(LayoutKind.Sequential)]
        private struct POINT
        {
            public int x;
            public int y;
        }

        // --- State Management ---
        private static IntPtr s_OriginalWndProc = IntPtr.Zero;
        private static IntPtr s_UnityWindow = IntPtr.Zero;
        private static bool s_IsInitialized = false;
        private static bool s_IsEnabled = false;
        private static bool s_WasMouseOverWindow = false;
        private static bool s_IsExternalDragActive = false;
        private static float s_LastDragCheckTime = 0f;

        // --- Events ---
        public static event Action<List<string>, Vector2> OnFilesDropped;
        public static event Action OnDragEnter;
        public static event Action OnDragExit;

        // --- Properties ---
        public static bool IsInitialized => s_IsInitialized;
        public static bool IsEnabled => s_IsEnabled;
        public static bool IsDragging => s_IsExternalDragActive;

        // --- Public Interface ---
        /// <summary>
        /// Initialize the Windows file drop system.
        /// </summary>
        public static bool Initialize()
        {
            if (s_IsInitialized)
                return true;

#if !UNITY_STANDALONE_WIN && !UNITY_EDITOR
            Debug.LogWarning("FileDropWindowsAPI: Only supported on Windows standalone builds.");
            return false;
#endif

            try
            {
                s_UnityWindow = GetActiveWindow();
                if (s_UnityWindow == IntPtr.Zero)
                {
                    Debug.LogError("FileDropWindowsAPI: Could not get Unity window handle.");
                    return false;
                }

                // Hook into the window procedure
                s_OriginalWndProc = SetWindowLongPtr(s_UnityWindow, GWL_WNDPROC, Marshal.GetFunctionPointerForDelegate(new WndProcDelegate(WndProc)));
                
                if (s_OriginalWndProc == IntPtr.Zero)
                {
                    Debug.LogError("FileDropWindowsAPI: Failed to hook window procedure.");
                    return false;
                }

                s_IsInitialized = true;
                Debug.Log("FileDropWindowsAPI: Successfully initialized Windows file drop system.");
                return true;
            }
            catch (Exception e)
            {
                Debug.LogError($"FileDropWindowsAPI: Initialization failed: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// Enable file drop detection.
        /// </summary>
        public static bool Enable()
        {
            if (!s_IsInitialized)
            {
                Debug.LogError("FileDropWindowsAPI: Must initialize before enabling.");
                return false;
            }

            if (s_IsEnabled)
                return true;

            try
            {
                DragAcceptFiles(s_UnityWindow, true);
                s_IsEnabled = true;
                Debug.Log("FileDropWindowsAPI: File drop enabled.");
                return true;
            }
            catch (Exception e)
            {
                Debug.LogError($"FileDropWindowsAPI: Failed to enable file drop: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disable file drop detection.
        /// </summary>
        public static void Disable()
        {
            if (!s_IsInitialized || !s_IsEnabled)
                return;

            try
            {
                DragAcceptFiles(s_UnityWindow, false);
                s_IsEnabled = false;
                SetDraggingState(false);
                Debug.Log("FileDropWindowsAPI: File drop disabled.");
            }
            catch (Exception e)
            {
                Debug.LogError($"FileDropWindowsAPI: Failed to disable file drop: {e.Message}");
            }
        }

        /// <summary>
        /// Shutdown and cleanup the file drop system.
        /// </summary>
        public static void Shutdown()
        {
            if (!s_IsInitialized)
                return;

            try
            {
                Disable();

                // Restore original window procedure
                if (s_OriginalWndProc != IntPtr.Zero && s_UnityWindow != IntPtr.Zero)
                {
                    SetWindowLongPtr(s_UnityWindow, GWL_WNDPROC, s_OriginalWndProc);
                }

                s_OriginalWndProc = IntPtr.Zero;
                s_UnityWindow = IntPtr.Zero;
                s_IsInitialized = false;

                Debug.Log("FileDropWindowsAPI: Shutdown complete.");
            }
            catch (Exception e)
            {
                Debug.LogError($"FileDropWindowsAPI: Shutdown failed: {e.Message}");
            }
        }

        // --- Private Implementation ---
        private delegate IntPtr WndProcDelegate(IntPtr hWnd, uint msg, IntPtr wParam, IntPtr lParam);

        private static IntPtr WndProc(IntPtr hWnd, uint msg, IntPtr wParam, IntPtr lParam)
        {
            try
            {
                if (s_IsEnabled)
                {
                    if (msg == WM_DROPFILES)
                    {
                        // Before handling the drop, make sure we fire drag enter if we haven't already
                        if (!s_IsExternalDragActive)
                        {
                            Debug.Log("FileDropWindowsAPI: Drop detected without prior drag enter - simulating drag enter");
                            s_IsExternalDragActive = true;
                            SetDraggingState(true);
                        }
                        
                        HandleDropFiles(wParam);
                        return IntPtr.Zero;
                    }
                    else if (msg == WM_MOUSEMOVE || msg == WM_NCMOUSEMOVE)
                    {
                        // Check for external drag operations when mouse moves
                        CheckForExternalDrag();
                    }
                    // Also check for other drag-related messages
                    else if (msg == 0x022E) // WM_DRAGENTER (might not work with DragAcceptFiles but worth trying)
                    {
                        Debug.Log("FileDropWindowsAPI: WM_DRAGENTER detected");
                        if (!s_IsExternalDragActive)
                        {
                            s_IsExternalDragActive = true;
                            SetDraggingState(true);
                        }
                    }
                    else if (msg == 0x022F) // WM_DRAGLEAVE
                    {
                        Debug.Log("FileDropWindowsAPI: WM_DRAGLEAVE detected");
                        if (s_IsExternalDragActive)
                        {
                            s_IsExternalDragActive = false;
                            SetDraggingState(false);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"FileDropWindowsAPI: Error in WndProc: {e.Message}");
            }

            // Call the original window procedure for all other messages
            return CallWindowProc(s_OriginalWndProc, hWnd, msg, wParam, lParam);
        }

        private static void CheckForExternalDrag()
        {
            try
            {
                // Check if left mouse button is pressed (indicating potential drag)
                bool isLeftButtonPressed = (GetAsyncKeyState(0x01) & 0x8000) != 0; // VK_LBUTTON
                
                // Get current cursor position
                GetCursorPos(out POINT cursorPos);
                
                // Check if cursor is over our window
                IntPtr windowUnderCursor = WindowFromPoint(cursorPos);
                bool isMouseOverOurWindow = (windowUnderCursor == s_UnityWindow);
                
                // More aggressive detection: if mouse button is pressed and over our window, assume external drag
                bool shouldBeInDragState = isLeftButtonPressed && isMouseOverOurWindow;
                
                // Only log when state changes to avoid spam
                if (shouldBeInDragState != s_IsExternalDragActive)
                {
                    Debug.Log($"FileDropWindowsAPI: CheckForExternalDrag - leftPressed={isLeftButtonPressed}, mouseOver={isMouseOverOurWindow}, shouldDrag={shouldBeInDragState}, currentDragActive={s_IsExternalDragActive}");
                }
                
                // Update drag state immediately without timing protection
                if (shouldBeInDragState && !s_IsExternalDragActive)
                {
                    s_IsExternalDragActive = true;
                    Debug.Log("FileDropWindowsAPI: External drag detected - entering drag state");
                    SetDraggingState(true);
                }
                else if (!shouldBeInDragState && s_IsExternalDragActive)
                {
                    s_IsExternalDragActive = false;
                    Debug.Log("FileDropWindowsAPI: External drag ended - exiting drag state");
                    SetDraggingState(false);
                }
                
                // Track mouse position for next check
                s_WasMouseOverWindow = isMouseOverOurWindow;
            }
            catch (Exception e)
            {
                Debug.LogError($"FileDropWindowsAPI: Error checking for external drag: {e.Message}");
            }
        }

        private static void HandleDropFiles(IntPtr hDrop)
        {
            try
            {
                Debug.Log("FileDropWindowsAPI: Handling dropped files");
                
                // Clear drag state since drop completed
                if (s_IsExternalDragActive)
                {
                    s_IsExternalDragActive = false;
                    SetDraggingState(false);
                }
                
                // Get drop position
                DragQueryPoint(hDrop, out POINT dropPoint);
                Vector2 dropPosition = new Vector2(dropPoint.x, dropPoint.y);

                // Get number of files
                uint fileCount = DragQueryFile(hDrop, 0xFFFFFFFF, null, 0);
                List<string> filePaths = new List<string>();

                // Get each file path
                for (uint i = 0; i < fileCount; i++)
                {
                    uint pathLength = DragQueryFile(hDrop, i, null, 0) + 1;
                    StringBuilder filePath = new StringBuilder((int)pathLength);
                    
                    if (DragQueryFile(hDrop, i, filePath, pathLength) > 0)
                    {
                        filePaths.Add(filePath.ToString());
                    }
                }

                // Cleanup
                DragFinish(hDrop);

                // Trigger events
                if (filePaths.Count > 0)
                {
                    OnFilesDropped?.Invoke(filePaths, dropPosition);
                    Debug.Log($"FileDropWindowsAPI: Successfully dropped {filePaths.Count} files at {dropPosition}");
                    foreach (string file in filePaths)
                    {
                        Debug.Log($"FileDropWindowsAPI: File: {file}");
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"FileDropWindowsAPI: Error handling dropped files: {e.Message}");
            }
        }

        private static void SetDraggingState(bool isDragging)
        {
            if (isDragging)
            {
                Debug.Log("FileDropWindowsAPI: Firing OnDragEnter event");
                OnDragEnter?.Invoke();
            }
            else
            {
                Debug.Log("FileDropWindowsAPI: Firing OnDragExit event");
                OnDragExit?.Invoke();
            }
        }

        /// <summary>
        /// Force a check for external drag operations.
        /// Call this when you suspect an external drag might be happening.
        /// </summary>
        public static void ForceCheckForExternalDrag()
        {
            if (!s_IsInitialized || !s_IsEnabled)
                return;
                
            Debug.Log("FileDropWindowsAPI: ForceCheckForExternalDrag called");
            CheckForExternalDrag();
        }

        /// <summary>
        /// Update method to be called from Unity's Update loop.
        /// This provides more frequent drag detection than relying only on mouse move messages.
        /// </summary>
        public static void Update()
        {
            if (!s_IsInitialized || !s_IsEnabled)
                return;
                
            // Check for drags every 50ms (20 times per second)
            float currentTime = UnityEngine.Time.realtimeSinceStartup;
            if (currentTime - s_LastDragCheckTime > 0.05f)
            {
                s_LastDragCheckTime = currentTime;
                CheckForExternalDrag();
            }
        }

        /// <summary>
        /// Simulate drag enter for visual feedback.
        /// Call this when you detect external drag operations starting.
        /// </summary>
        public static void SimulateDragEnter()
        {
            if (!s_IsExternalDragActive)
            {
                s_IsExternalDragActive = true;
                SetDraggingState(true);
            }
        }

        /// <summary>
        /// Simulate drag exit for visual feedback.
        /// Call this when you detect external drag operations ending.
        /// </summary>
        public static void SimulateDragExit()
        {
            if (s_IsExternalDragActive)
            {
                s_IsExternalDragActive = false;
                SetDraggingState(false);
            }
        }

        /// <summary>
        /// Get system information for debugging.
        /// </summary>
        public static string GetSystemInfo()
        {
            return $"FileDropWindowsAPI Status:\n" +
                   $"  Initialized: {IsInitialized}\n" +
                   $"  Enabled: {IsEnabled}\n" +
                   $"  Dragging: {IsDragging}\n" +
                   $"  External Drag Active: {s_IsExternalDragActive}\n" +
                   $"  Mouse Over Window: {s_WasMouseOverWindow}\n" +
                   $"  Window Handle: {s_UnityWindow}\n" +
                   $"  Original WndProc: {s_OriginalWndProc}";
        }

        /// <summary>
        /// Get the current cursor position in screen coordinates.
        /// Returns Vector2.zero if the operation fails.
        /// </summary>
        public static Vector2 GetCursorPosition()
        {
            try
            {
                if (GetCursorPos(out POINT cursorPos))
                {
                    return new Vector2(cursorPos.x, cursorPos.y);
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"FileDropWindowsAPI: Error getting cursor position: {e.Message}");
            }
            
            return Vector2.zero;
        }
    }
}

using UnityEngine;
using UnityEngine.UIElements;
using Unity.AppUI.UI; // Required for Slider
using System;
using System.Linq;
using Object = UnityEngine.Object;

namespace Workshop.UXLab
{
    [CreateAssetMenu(fileName = "ZoomControlSystem", menuName = "Workshop/UXLab/Systems/ZoomControlSystem")]
    public class ZoomControlSystem : TableSystem
    {
        [Header("UI References")]
        [SerializeField] private string m_ZoomControlLayoutPath = "UI/ZoomControl/ZoomControlLayout";
        [SerializeField] private string m_ZoomControlStyleSheetPath = "UI/ZoomControl/ZoomControlStyles";

        // UI Host
        private GameObject m_UIHostObject;
        private VisualElement m_RootUIElement;
        private Panel m_WrapperPanel;
        private VisualElement m_ZoomControlRootElement;

        // UI Elements
        private SliderFloat m_ZoomSlider;
        private Label m_ZoomLabel;

        // References
        private CameraController m_CameraController;

        // Flag to prevent feedback loop when setting camera zoom from the slider
        private bool m_IsUpdatingFromSlider = false;

        public override void Init(TableManager tableManager)
        {
            base.Init(tableManager);

            m_CameraController = m_TableManager.CameraController;
            if (m_CameraController == null)
            {
                Debug.LogError("ZoomControlSystem: CameraController not found on TableManager. System cannot function.", this);
                base.Shutdown();
                return;
            }

            if (!SetupUIHost())
            {
                Debug.LogError("ZoomControlSystem: Failed to initialize UI Host. Aborting Init.", this);
                base.Shutdown();
                return;
            }

            LoadZoomControl();
            QueryUIElements();
            RegisterCallbacks();

            // Set initial state
            UpdateSliderFromCameraController();

            // Subscribe to CameraController event for external zoom changes
            if (m_CameraController != null)
            {
                m_CameraController.OnZoomChanged += HandleCameraZoomChanged;
            }

            Debug.Log("ZoomControlSystem Initialized.", this);
        }

        public override void Shutdown()
        {
            if (!m_IsInitialized)
                return;

            // Unsubscribe from events
            if (m_CameraController != null)
            {
                m_CameraController.OnZoomChanged -= HandleCameraZoomChanged;
            }
            UnregisterCallbacks();

            if (m_UIHostObject != null)
            {
                Object.Destroy(m_UIHostObject);
                m_UIHostObject = null;
                m_RootUIElement = null;
                m_WrapperPanel = null;
                m_ZoomControlRootElement = null;
            }

            m_ZoomSlider = null;
            m_ZoomLabel = null;
            m_CameraController = null;

            Debug.Log("ZoomControlSystem Shutdown.", this);
            base.Shutdown();
        }

        private bool SetupUIHost()
        {
            if (m_TableManager.UIPanelSettings == null)
            {
                Debug.LogError("ZoomControlSystem: TableManager.UIPanelSettings is null. Cannot configure UI host.", this);
                return false;
            }

            m_UIHostObject = new GameObject("ZoomControlUIHost");
            var uiDocument = m_UIHostObject.AddComponent<UIDocument>();
            uiDocument.panelSettings = m_TableManager.UIPanelSettings;
            m_RootUIElement = uiDocument.rootVisualElement;

            if (m_RootUIElement == null)
            {
                Debug.LogError("ZoomControlSystem: Failed to get rootVisualElement from created UIDocument.", this);
                Object.Destroy(m_UIHostObject);
                m_UIHostObject = null;
                return false;
            }

            // Create a full-screen wrapper panel to hold the control
            m_WrapperPanel = new Panel();
            m_WrapperPanel.name = "zoom-control-wrapper-panel";
            m_WrapperPanel.style.position = Position.Absolute;
            m_WrapperPanel.style.top = 0;
            m_WrapperPanel.style.left = 0;
            m_WrapperPanel.style.right = 0;
            m_WrapperPanel.style.bottom = 0;
            m_WrapperPanel.style.backgroundColor = Color.clear;
            m_WrapperPanel.pickingMode = PickingMode.Ignore; // Ignore clicks on the wrapper itself
            m_RootUIElement.Add(m_WrapperPanel);

            return true;
        }

        private void LoadZoomControl()
        {
            if (m_WrapperPanel == null) return;

            VisualTreeAsset layout = Resources.Load<VisualTreeAsset>(m_ZoomControlLayoutPath);
            StyleSheet styles = Resources.Load<StyleSheet>(m_ZoomControlStyleSheetPath);

            if (layout == null)
            {
                Debug.LogError($"ZoomControlSystem: Could not load layout from Resources/{m_ZoomControlLayoutPath}", this);
                return;
            }

            m_ZoomControlRootElement = layout.Instantiate().Children().First();

            if (m_ZoomControlRootElement == null)
            {
                Debug.LogError($"ZoomControlSystem: Instantiated layout from {m_ZoomControlLayoutPath} has no root child element.", this);
                return;
            }

            if (styles != null)
            {
                m_ZoomControlRootElement.styleSheets.Add(styles);
            }
            else
            {
                Debug.LogWarning($"ZoomControlSystem: Could not load stylesheet from Resources/{m_ZoomControlStyleSheetPath}", this);
            }

            m_WrapperPanel.Add(m_ZoomControlRootElement);
        }

        private void QueryUIElements()
        {
            if (m_ZoomControlRootElement == null) return;

            m_ZoomSlider = m_ZoomControlRootElement.Q<SliderFloat>("zoom-slider");
            m_ZoomLabel = m_ZoomControlRootElement.Q<Label>("zoom-label");

            if (m_ZoomSlider == null)
            {
                Debug.LogError("ZoomControlSystem: Could not find Slider named 'zoom-slider' in the UI.", this);
            }
            if (m_ZoomLabel == null)
            {
                Debug.LogError("ZoomControlSystem: Could not find Label named 'zoom-label' in the UI.", this);
            }
        }

        private void RegisterCallbacks()
        {
            if (m_ZoomSlider != null)
            {
                m_ZoomSlider.RegisterValueChangingCallback(OnSliderValueChanged);
            }
        }

        private void UnregisterCallbacks()
        {
            if (m_ZoomSlider != null)
            {
                m_ZoomSlider.UnregisterValueChangingCallback(OnSliderValueChanged);
            }
        }

        private void OnSliderValueChanged(ChangingEvent<float> evt)
        {
            if (m_CameraController != null && m_IsInitialized)
            {
                float newValue = evt.newValue;

                m_IsUpdatingFromSlider = true; // Set flag before updating camera
                try
                {
                    // Check if the camera controller's value is already very close to avoid minor feedback loops
                    if (Mathf.Abs(m_CameraController.GetNormalizedZoom() - newValue) > 0.001f) {
                         m_CameraController.SetNormalizedZoom(newValue);
                    }
                    // Always update the label immediately when the slider changes
                    UpdateLabel(newValue);
                }
                finally
                {
                    m_IsUpdatingFromSlider = false; // Unset flag after updating camera
                }
            }
        }

        /// <summary>
        /// Handles the OnZoomChanged event from the CameraController.
        /// Updates the slider if the change did not originate from this slider.
        /// </summary>
        private void HandleCameraZoomChanged(float newNormalizedZoom)
        {
            // If the flag is set, it means this change originated from our slider,
            // so we don't need to update the slider again.
            if (m_IsUpdatingFromSlider)
            {
                return;
            }

            // Update the slider visually without triggering its changed event
            if (m_ZoomSlider != null && Mathf.Abs(m_ZoomSlider.value - newNormalizedZoom) > 0.001f)
            {
                m_ZoomSlider.SetValueWithoutNotify(newNormalizedZoom);
                UpdateLabel(newNormalizedZoom);
            }
        }

        /// <summary>
        /// Reads the normalized zoom from the CameraController and updates the slider's value
        /// and the label, but only if the value has changed significantly.
        /// Uses SetValueWithoutNotify to prevent triggering OnSliderValueChanged.
        /// </summary>
        private void UpdateSliderFromCameraController()
        {
            float currentCameraZoom = m_CameraController.GetNormalizedZoom();

            // Only update if the difference is noticeable, prevents jitter/loops
            if (Mathf.Abs(m_ZoomSlider.value - currentCameraZoom) > 0.001f)
            {
                m_ZoomSlider.SetValueWithoutNotify(currentCameraZoom);
                UpdateLabel(currentCameraZoom);
            }
        }

        private void UpdateLabel(float normalizedZoom)
        {
            if (m_ZoomLabel != null)
            {
                // Format as percentage (e.g., 0.75 -> 75%)
                m_ZoomLabel.text = $"{normalizedZoom * 100:F0}%";
            }
        }
    }
}

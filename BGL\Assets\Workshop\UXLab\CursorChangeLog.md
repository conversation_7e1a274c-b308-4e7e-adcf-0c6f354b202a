# Cursor Change Log

## Card Editor UI Implementation

1. Updated `CardMeshGenerator.cs`:
   - Added Inches to UnitSpace enum
   - Added GetUnitMultiplier implementation for inches
   - Added CurrentUnitSpace property

2. Updated `CardEditorSystem.cs`:
   - Added CardSelected and CardDeselected events
   - Added helper properties (IsCardSelected, SelectedCardMesh)
   - Implemented UI creation directly in the system's Init method
   - Added UI cleanup in Shutdown method
   - Fixed naming convention to use m_ prefix for private fields
   - Fixed naming convention to use Pascal case for public members
   - Stores reference to UI Control for direct method calls
   - Added panel settings configuration from TableManager

3. Updated `TableManager.cs`:
   - Added PanelSettings reference for UI consistency
   - Added UIPanelSettings property
   - Updated variable naming to follow m_ prefix convention
   - Added UI interaction check to prevent hover/click on 3D objects when using UI
   - Implemented EventSystem.IsPointerOverGameObject check for UI interaction detection

4. Updated `TableSystem.cs`:
   - Updated variable naming to follow m_ prefix convention

5. Created UI components:
   - Created `CardEditorUIControl.cs` for managing UI interactions
   - Made CardEditorUIControl independent with public methods for setting the active card
   - Removed dependency on CardEditorSystem for cleaner architecture
   - Created UXML layout for the UI in Resources/UI/CardEditorUI.uxml
   - Created USS styling for the UI in Resources/UI/CardEditorUI.uss
   - Fixed naming convention to use m_ prefix for private fields
   - Fixed naming convention to use k prefix for constants
   - Fixed unit conversion to correctly handle values when switching unit types
   - Implemented proper unit conversion algorithm for changing between different unit types
   - Added card thickness field and conversion handling
   - Added dropdown for common card sizes (playing card, credit card, etc.)
   - Implemented locking of custom size fields when a predefined card type is selected

6. Removed redundant components:
   - Removed `CardEditorUIManager.cs` and integrated functionality into CardEditorSystem
   - Removed `CardEditorUISetup.cs` and integrated functionality into CardEditorSystem

7. Updated documentation:
   - Created Changelog.md to track changes
   - Created CursorLearnings.md to document insights
   - Updated CardEditorUI_README.md for setup instructions

8. Simplifications:
   - Removed reflection-based reference injection
   - Replaced event subscriptions with direct method calls
   - Made CardEditorUIControl completely independent of CardEditorSystem
   - Unified UI appearance with shared panel settings
   - Prevented 3D object interactions when mouse is over UI elements

- Added `ToolbarSystem.cs` for managing editor toolbars.
- Created initial UXML/USS files for `TopToolbar`, `ModeToolbar`, and `EditingToolbar` in `Resources/UI/Toolbars`.

- `CardLayout`: Fixed `CreateElementFromData` to calculate element `localPosition.z` based on a base offset and layer index, ensuring correct stacking above the card.

*   Refactored Dimension/Layers/Element Editor Controls from MonoBehaviour to VisualElement.
*   Implemented UI Host pattern for systems needing UI.
*   Fixed Element Z-ordering based on layer index.
*   Implemented Width/Height properties (Option B) on ImageCardElement and updated editor/handles to use them.
*   Integrated `ToolbarSystem`.
*   Added formatting to FloatFields in Dimension Editor (units, precision).
*   Added formatting to Rotation FloatField in Element Editor (degrees symbol, precision).

### Learning Points 

## Change Log

*   **2024-07-29:**
    *   Added `ZoomControlSystem.cs` with UXML/USS for a camera zoom slider UI.
    *   Added `GetNormalizedZoom()` to `CameraController.cs`.
*   **2024-07-28:**
    *   Refactored Transform display in `CardElementEditorControl` to use Width/Height properties and support unit conversion.
    *   Fixed resize handle logic in `CardEditorSystem` to correctly update Width/Height and position.
    *   Implemented Width/Height properties on `ImageCardElement`.
    *   Updated `CardElementEditorControl` to use Width/Height properties for sizing.
*   **2024-07-27:**
    *   Refactored `CardLayersEditorControl` to `VisualElement`.
    *   Fixed element Z-ordering in `CardLayout`.
*   **2024-07-26:**
    *   Refactored `CardDimensionEditorControl` to `VisualElement`.
    *   Implemented `ToolbarSystem` and basic toolbars (Top, Mode, Editing).
    *   Configured Mode Toolbar `ActionGroup` for multiple selection.
    *   Updated `ToolbarSystem`'s Box Mode button callback to toggle `IsBoxModeActive` state.
    *   Replaced `OnDrawGizmos` bounding box with runtime `LineRenderer` in `CardElement`, controlled by `ToolbarSystem.IsBoxModeActive`.
    *   **Refactor `CardInteractionSystem.cs`**: Overhauled card interaction logic.
        *   Introduced new state variables (`m_SelectionStartPosition`, `m_SelectionStartRotation`, `m_DragStartPosition`, `m_DragStartRotation`, `m_WasDraggingLastFrame`) for more robust state tracking.
        *   Renamed `m_AnimationCts` to `m_ActiveAnimationCts` and `StopDragAnimation` to `StopCurrentAnimation`.
        *   Replaced `AnimateDropAsync` with a general `AnimateTransformAsync` for position and rotation.
        *   Revised `HandleComponentClicked` to correctly manage selection, deselection, and card return/placement logic based on whether a click is part of a drag operation or a simple click.
        *   Revised `HandleBlankClicked` to return the selected card to its original position upon a simple click, and to correctly maintain selection if a drag ends on a blank space.
        *   Updated `ProcessCardDrag` to use new state variables for consistent drag initiation height and drop targeting. It now sets `m_WasDraggingLastFrame` upon drag completion.
        *   Added internal helper `SelectCardInternal`.
        *   Updated `Shutdown` logic for card state restoration.
    *   **Further `CardInteractionSystem.cs` update**: Modified `HandleBlankClicked` and `HandleComponentClicked` to remove animations for simple deselection and card switching. Cards now remain in their current position. Drag cancellation by dropping on another card still animates the dragged card back.
    *   **CardInteractionSystem Logic Refinement**: `m_SelectionStartPosition` (resting place) is now only updated on initial selection or if a drag successfully completes by dropping the card onto itself. Drags ending on blank space or another card now return the dragged card to `m_DragStartPosition` (where that specific drag began). Simple clicks deselect/select cards in place without movement.
    *   **Added Drag Tilt Effect to `CardInteractionSystem.cs`**: Implemented a visual effect where the dragged card tilts on its Z-axis based on horizontal mouse movement. Added configurable parameters for max tilt angle, sensitivity, and tilt speed.
    *   **Added Position Lag to Card Drag in `CardInteractionSystem.cs`**: Implemented smooth position interpolation for dragged cards, making them lag slightly behind the mouse. Added `m_DragPositionLerpSpeed` for configuration. Drop animation now also smoothly resets card rotation.
*   **2024-07-25:**
    *   Created initial project structure and core systems (`TableManager`, `CameraController`).
    *   Defined data structures (`CardDefinition`, `CardLayoutData`, `CardElementData`).

## [YYYY-MM-DD]

*   **Created `IntegratedCardEditorControl.cs`, `IntegratedCardEditorLayout.uxml`, `IntegratedCardEditorStyles.uss`**: Initial files for the new consolidated card editor UI.
*   **Created `Changelog.md`**: Added human-readable changelog.
*   **Created `CursorChangeLog.md`**: Added detailed internal changelog.
*   **Created `CursorLearnings.md`**: Added learning/notes document.

# Changelog

- Configured AppUI Panel in CardEditorSystem for proper layering and input passthrough.

- Created `CardDeck` component to manage and visually stack cards.
- Implemented `AddCard`, `AddNewCard`, `DrawCard`, `PeekTopCard`, `ClearDeck`, `Shuffle`, `FlipDeck`, and `UpdateLayout` methods in `CardDeck`.
- Added stubs for `RevealTopCard`, `PlaceOnTop`, and `Discard`.
- Created `CardDeckEditor` custom inspector with buttons to test deck operations.
- Refined `CardDeck` robustness by adding checks for `CardSizeData` validity before layout updates or adding cards.
- Switched `CardDeck.Shuffle` to use `System.Random` for potentially better distribution.
- Refactored `CardDeck` stacking logic:
    - Replaced absolute biased randomness with cumulative steps.
    - Added `CumulativeOffsetStep` (Vector2) and `CumulativeRotationStep` (float) to define the average change per card.
    - Renamed variation fields to `OffsetStepNoise` and `RotationStepNoise` to clarify they are noise around the step.
    - `UpdateLayout` now calculates each card's transform based on the previous card's transform plus the step and noise.
    - Removed pre-calculation/storage of random values in `DeckCardInfo`.
- Initialized `System.Random` in `Awake` for noise generation.

- Implemented dynamic deck creation in `CardInteractionSystem`.
- Added `SetCardPrefabForRuntimeInit` and animation parameter getters to `CardDeck`.
- Refactored `StopCurrentAnimation` to `StopAnimationOnCard` in `CardInteractionSystem`.
- Simplified dynamic deck creation: removed `m_DefaultCardPrefabForDynamicDecks` from `CardInteractionSystem` and `SetCardPrefabForRuntimeInit` from `CardDeck`.
- Dynamic decks now self-destruct when only one card remains, releasing the card (with rotation reset) rather than when completely empty.

- Modified `CardInteractionSystem.cs` to implement bounding box check for dynamic deck creation. Added `DoCardsOverlap` helper method and updated `ProcessCardDrag`.
- Enhanced dynamic deck creation in `CardInteractionSystem.cs`:
    - `DoCardsOverlap` now performs a 2D (XY plane) bounding box check.
    - `GetObjectUnderCard` attempts raycast first, then falls back to iterating all table cards and checking for 2D overlap if raycast fails. 
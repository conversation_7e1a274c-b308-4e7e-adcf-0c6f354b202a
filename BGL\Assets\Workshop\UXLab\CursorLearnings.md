# Cursor Learnings

## Card Editor UI System

### Insights
- Unity UI Toolkit provides a modern way to create UI with UXML and USS, similar to HTML and CSS
- Proper separation of concerns is important:
  - CardEditorSystem handles selection and management
  - CardMeshGenerator handles the actual card mesh generation
  - CardEditorUIControl focuses solely on UI editing functionality without system dependencies
- Unit space conversion is straightforward but requires careful handling of conversions both ways
  - Understanding how values are stored is crucial: in our case, values are stored in the current unit format
  - When changing units, we need a two-step conversion: current unit → meters → new unit
  - No conversion is needed when displaying values in the same unit they're stored in
  - It's important to include all properties (including thickness) when handling unit conversions
- Predefined object templates can improve user experience
  - Common card sizes as presets allow quick selection of standard dimensions
  - Conditional UI states (enabling/disabling fields) provide clear context for user actions
  - Conversion between different unit systems must be considered for predefined sizes stored in a specific unit
- Simple, direct APIs are better than complex event systems for tightly coupled components
- Shared UI panel settings ensure consistent UI appearance and behavior across multiple systems
- Proper UI interaction handling prevents conflicts between 3D object interactions and UI interactions

### Challenges Overcome
- Needed to ensure the UI responds accurately to card selection/deselection events
- Field values need to be properly converted between different units while maintaining the internal meter-based values
- Found a way to dynamically set up the UI from the CardEditorSystem rather than requiring a separate manager
- Simplified the design by removing unnecessary dependencies and event subscriptions
- Ensured UI panels from different systems use the same rendering setup for visual consistency
- Prevented unintended 3D object selection when the user is interacting with UI elements
- Implemented proper dimension matching to identify when a card matches a predefined size
- Ensured all properties are included in unit conversion to avoid distorted dimensions

### Best Practices Identified
- Following the coding conventions with proper prefixes (m_ for private fields, k for constants)
- Creating clear and focused controls with a single responsibility
- Prefer direct method calls over reflection for setting up object references
- Components should have clear, well-defined interfaces that don't expose implementation details
- Avoid circular dependencies between components when possible
- Properly cleaning up references in component destruction to avoid memory leaks
- Starting UI object as disabled until fully initialized to prevent UI flashing
- Using a shared panel settings reference to maintain UI consistency across systems
- Using EventSystem.IsPointerOverGameObject() to detect UI interactions and avoid conflicts with 3D object interactions
- Provide both predefined options and custom controls for flexibility while maintaining ease of use
- Include floating point tolerance when matching against predefined values 

## Unity 6 UI Toolkit Custom Controls

When implementing custom controls with Unity 6's UI Toolkit:

1. **UXML Element Creation**:
   - Use the `[UxmlElement]` attribute on a `partial` class
   - Inherit from `VisualElement` or another UI control class
   - No need to implement a UxmlFactory class manually as Unity 6 auto-generates it

2. **Exposing Properties to UXML**:
   - Use the `[UxmlAttribute]` attribute on properties to expose them as UXML attributes
   - Properties are automatically converted to kebab-case in UXML (e.g., `DragHandleClassName` becomes `drag-handle-class-name`)
   - Unity 6 provides built-in converters for common types like string, int, float, bool, Color, etc.

3. **Events and Data Binding**:
   - Use C# events to allow other components to respond to control state changes
   - For complex data types, consider implementing custom converter attributes
   - Use strong typing for properties to enable compile-time checking

4. **Styling Best Practices**:
   - Keep styling in USS files, not in code
   - Use `AddToClassList()` rather than direct style manipulation
   - Avoid setting specific style properties in C# when possible
   - Store class names as constants to avoid string literals and typos

5. **Control Lifecycle**:
   - Initialize controls in the constructor or in the AttachToPanelEvent callback
   - Clean up resources in the DetachFromPanelEvent callback
   - Register for UI events using RegisterCallback<T> methods

## UI Controls Implementation

For draggable panels, a few key learnings:

1. **Bounds Checking**: Always implement bounds checking to prevent UI elements from moving off-screen.

2. **Event Handling**: Using capture/release mouse pattern ensures drag operations complete correctly even if the mouse moves outside the element.

3. **Separation of Concerns**: Keeping the drag logic separate from styling makes maintenance easier.

4. **Attribute Exposure**: Expose configurable properties like drag handle class and edge padding as UXML attributes. 

## UI Component Architecture
- **DraggablePanel Component**: Created a reusable UI component for draggable panels with shadow effects
- **Discovered contentContainer override**: Instead of overriding the `Add` method (which isn't virtual), we should override the `contentContainer` property to control where child elements are added
- **Naming Convention**: Use distinct names for UXML and USS files to avoid confusion - suffix with "Layout" for UXML and "Stylesheet" for USS
- **Shadow Implementation**: Used CSS variables and -unity-box-shadow to create customizable shadows
- **Fallback Pattern**: Implemented fallback code when templates can't be loaded to ensure UI still functions
- **Nested Structure Benefits**: Moving styling to container elements creates clear separation between structure and appearance
- **Default Drag Handlers**: Making containers the default drag target provides better UX as users naturally try to drag a panel by its visible surface

## Unity UI Toolkit Best Practices
- **File Structure**: Keep UXML (layout) and USS (style) files separate for better organization and reuse
- **Resource Loading**: Use Resources.Load to load UI assets at runtime
- **Error Handling**: Add clear error messages when UI assets fail to load
- **Element Picking**: Be careful with picking-mode to ensure correct interaction with UI elements
- **Event Propagation**: Use StopPropagation to prevent events from bubbling up when handled
- **Visual Hierarchy**: Use nested elements with specific responsibilities (wrapper, shadow, container, content)
- **Consistent Padding**: Apply padding at the container level for uniform layout

## Common Pitfalls
- **Same Names for Different Resources**: Using the same name for both UXML and USS files creates ambiguity when loading resources
- **Override Issues**: Cannot override non-virtual methods like `Add()` in VisualElement
- **Pointer Events**: Pointer events need to be properly captured and released to avoid stuck UI states
- **Size Calculations**: Remember to account for padding and borders when setting element sizes 

## AppUI
- Referencing the `How to use AppUI - a document for Claude.md` guide is crucial for correct component usage (e.g., `Button.title` vs `Button.text`).

## State Management & Callbacks
- When a UI callback (e.g., `OnValueChanged`) triggers an action in a system, don't assume the system's state (e.g., `m_HandleTargetElement`) is still valid from the initial interaction. The callback logic should re-fetch or operate on known current state (like the currently selected object `m_SelectedComponent`) if necessary.

## Interaction Loops
- Using a MonoBehaviour `Update` loop (like `TableManagerComponent`) for per-frame checks (raycasting, input) is generally more robust than async tasks within ScriptableObjects.

## Plane Definition
- When defining a `Plane` for raycasting, use 3 non-collinear points. The winding order (e.g., `new Plane(p0, p2, p1)`) determines the direction of the plane's normal, which is critical for `Plane.Raycast` to work correctly.

## Corner Resizing
- Implementing corner-anchored resizing requires updating both the element's size *and* its center position based on the vector between the dragged handle and the opposite anchor point.

## Debugging
- Temporarily adding detailed `Debug.Log` statements to trace execution flow and check variable values step-by-step is essential for diagnosing complex interaction issues.

## ActionGroup Vertical Layout Problem and Solution
- **Problem:** `ActionGroup` with `direction="Vertical"` was not rendering vertically.
- **Solution:** Discovered that some AppUI components, including `ActionGroup`, seem to require being nested within an `appui:Panel` element to function correctly, possibly for theme/layout context. Wrapping the toolbars in a root `Panel` created in C# resolved the vertical layout issue. 

## Card Element Z-Ordering
- **Card Element Z-Ordering:** Ensure `CardLayout.cs` explicitly calculates the `localPosition.z` for child elements. A base offset slightly above the card surface (Z=0) plus a small increment per layer index (`element.transform.GetSiblingIndex()`) ensures correct visual stacking. Relying solely on `CardElementData.Position.z` can lead to elements appearing behind the card or not layering correctly. 

## Refactoring Card Editor UI

*   **Goal:** Consolidate `CardLayersEditorControl` and `CardElementEditorControl` into a single `IntegratedCardEditorControl` panel.
*   **Structure:** Use Foldouts (simulating Accordion) for Size, Layout, and Elements sections.
*   **Challenge:** Migrating and integrating logic for element list management, property display/editing, unit conversions, and layout controls from multiple sources into one cohesive control. 

*   **Initial Setup**: Created initial changelog and learning documents (`CursorChangeLog.md`, `CursorLearnings.md`, `Changelog.md`).
*   **AppUI Panel Configuration**: Learned how to configure an `AppUI Panel` in `CardEditorSystem` to fill the screen, be transparent, and ignore input events using `pickingMode = PickingMode.Ignore` to allow clicks to pass through. Gave it a name for easier debugging. 

## CardDeck System

- Created a `CardDeck` component (`Code/CardDeck.cs`) to manage a list of `CardLayout` objects.
- Implemented visual stacking using per-card random offsets (`HorizontalOffsetVariation`, `RotationVariation`) and a vertical offset (`VerticalOffsetPerCard`). Offsets are stored in a private `DeckCardInfo` struct.
- The `UpdateLayout` method applies these stored offsets and the vertical stacking based on the card's index in the `m_Cards` list.
- Created a corresponding `CardDeckEditor` (`Code/Editor/CardDeckEditor.cs`) to provide buttons in the inspector for easily adding, drawing, shuffling, clearing, and updating the deck layout.
- Ensured editor operations work with Unity's Undo system (`Undo.RecordObject`, `Undo.RegisterCreatedObjectUndo`, `Undo.SetTransformParent`, `Undo.DestroyObjectImmediate`).
- Added checks to prevent modifying prefab assets directly from the editor buttons, requiring operations on scene instances.
- Added logic in `AddCard` and `AddNewCard` to ensure the `CardMeshGenerator` on the card has valid `CardSizeData` applied before adding it to the deck, as the layout relies on this information (though currently only for potential future use, not direct layout calculation). It attempts to use `EditorSizeData` if `CurrentSizeData` isn't set.
- Refactored CardDeck stacking from absolute bias to cumulative steps:
    - Replaced `HorizontalOffsetBiasX/Y`, `RotationBias`, `HorizontalOffsetVariation`, `RotationVariation`.
    - Added `CumulativeOffsetStep` (Vector2), `CumulativeRotationStep` (float), `OffsetStepNoise` (float), `RotationStepNoise` (float).
    - The `UpdateLayout` function now iterates, calculating each card's local position and rotation based on the previous card's transform plus the average step (`Cumulative...Step`) and random noise (`...StepNoise`).
    - This creates a more connected, fanned, or snaking appearance depending on the step values.
- Switched `Shuffle` method to use `System.Random` instead of `UnityEngine.Random` for potentially better shuffle quality. 
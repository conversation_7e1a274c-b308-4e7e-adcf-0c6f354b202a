using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;

#if UNITY_EDITOR
namespace Workshop.UXLab
{
    [CustomEditor(typeof(CardUIRenderTest))]
    public class CardUIRenderTestEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            DrawDefaultInspector();

            CardUIRenderTest testComponent = (CardUIRenderTest)target;

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Quick Setup", EditorStyles.boldLabel);

            if (GUILayout.Button("Create Render System"))
            {
                CreateRenderSystem(testComponent);
            }

            if (GUILayout.Button("Auto-Assign Card Mesh"))
            {
                AutoAssignCardMesh(testComponent);
            }

            if (GUILayout.Button("Add System to TableManager"))
            {
                AddSystemToTableManager(testComponent);
            }

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Render Controls", EditorStyles.boldLabel);

            // Get the current pixels per mm value
            SerializedProperty pixelsPerMmProp = serializedObject.FindProperty("m_PixelsPerMm");
            EditorGUILayout.PropertyField(pixelsPerMmProp, new GUIContent("Resolution (pixels/mm)"));
            serializedObject.ApplyModifiedProperties();

            if (EditorApplication.isPlaying)
            {
                if (GUILayout.Button("Update Resolution"))
                {
                    UpdateResolution(testComponent);
                }

                if (GUILayout.Button("Render to Texture"))
                {
                    RenderToTexture(testComponent);
                }
            }
            else
            {
                EditorGUILayout.HelpBox("Enter Play mode to update resolution or render the texture.", MessageType.Info);
            }
        }

        private void CreateRenderSystem(CardUIRenderTest testComponent)
        {
            // Check if a render system already exists as a ScriptableObject
            string[] guids = AssetDatabase.FindAssets("t:CardRenderSystem");
            if (guids.Length > 0)
            {
                string path = AssetDatabase.GUIDToAssetPath(guids[0]);
                CardRenderSystem existingSystem = AssetDatabase.LoadAssetAtPath<CardRenderSystem>(path);

                if (existingSystem != null)
                {
                    // Assign the existing system
                    SerializedObject serializedObj = new SerializedObject(testComponent);
                    SerializedProperty systemProp = serializedObj.FindProperty("m_RenderSystem");
                    systemProp.objectReferenceValue = existingSystem;
                    serializedObj.ApplyModifiedProperties();

                    Debug.Log("Assigned existing CardRenderSystem ScriptableObject");
                    return;
                }
            }

            // Create a new CardRenderSystem ScriptableObject
            CardRenderSystem renderSystem = ScriptableObject.CreateInstance<CardRenderSystem>();

            // Try to find a PanelSettings asset to use as a template
            PanelSettings panelSettings = null;
            string[] panelSettingsGuids = AssetDatabase.FindAssets("t:PanelSettings");
            if (panelSettingsGuids.Length > 0)
            {
                string path = AssetDatabase.GUIDToAssetPath(panelSettingsGuids[0]);
                panelSettings = AssetDatabase.LoadAssetAtPath<PanelSettings>(path);
            }

            // Assign the panel settings if found
            if (panelSettings != null)
            {
                SerializedObject renderSystemSO = new SerializedObject(renderSystem);
                SerializedProperty panelSettingsProp = renderSystemSO.FindProperty("m_PanelSettingsTemplate");
                panelSettingsProp.objectReferenceValue = panelSettings;
                renderSystemSO.ApplyModifiedProperties();
            }

            // Save the ScriptableObject as an asset
            string assetPath = "Assets/Workshop/UXLab/Table Systems/CardRenderSystem.asset";
            AssetDatabase.CreateAsset(renderSystem, assetPath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Assign the render system to the test component
            SerializedObject so = new SerializedObject(testComponent);
            SerializedProperty renderSystemProp = so.FindProperty("m_RenderSystem");
            renderSystemProp.objectReferenceValue = renderSystem;
            so.ApplyModifiedProperties();

            Debug.Log("Created and assigned new CardRenderSystem ScriptableObject");
        }

        private void AutoAssignCardMesh(CardUIRenderTest testComponent)
        {
            // Check if the component is already on a card mesh
            CardMeshGenerator cardMesh = testComponent.GetComponent<CardMeshGenerator>();
            if (cardMesh == null)
            {
                // Try to find a card mesh in the scene
                cardMesh = FindObjectOfType<CardMeshGenerator>();
            }

            if (cardMesh != null)
            {
                // Assign the card mesh
                SerializedObject so = new SerializedObject(testComponent);
                SerializedProperty cardMeshProp = so.FindProperty("m_CardMesh");
                cardMeshProp.objectReferenceValue = cardMesh;
                so.ApplyModifiedProperties();

                Debug.Log("Assigned CardMeshGenerator");
            }
            else
            {
                Debug.LogError("No CardMeshGenerator found in the scene");
            }
        }

        private void AddSystemToTableManager(CardUIRenderTest testComponent)
        {
            // Get the render system from the test component
            SerializedObject so = new SerializedObject(testComponent);
            SerializedProperty renderSystemProp = so.FindProperty("m_RenderSystem");
            CardRenderSystem renderSystem = renderSystemProp.objectReferenceValue as CardRenderSystem;

            if (renderSystem == null)
            {
                Debug.LogError("No CardRenderSystem assigned to the test component");
                return;
            }

            // Find the TableManager in the scene
            TableManager tableManager = FindObjectOfType<TableManager>();
            if (tableManager == null)
            {
                // Try to find TableManager as a ScriptableObject
                string[] guids = AssetDatabase.FindAssets("t:TableManager");
                if (guids.Length > 0)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guids[0]);
                    tableManager = AssetDatabase.LoadAssetAtPath<TableManager>(path);
                }
            }

            if (tableManager == null)
            {
                Debug.LogError("No TableManager found in the scene or as an asset");
                return;
            }

            // Add the render system to the TableManager
            SerializedObject tableManagerSO = new SerializedObject(tableManager);
            SerializedProperty systemsProp = tableManagerSO.FindProperty("m_TableSystems");

            // Check if the system is already in the list
            bool alreadyAdded = false;
            for (int i = 0; i < systemsProp.arraySize; i++)
            {
                SerializedProperty systemProp = systemsProp.GetArrayElementAtIndex(i);
                if (systemProp.objectReferenceValue == renderSystem)
                {
                    alreadyAdded = true;
                    break;
                }
            }

            if (!alreadyAdded)
            {
                // Add the system to the list
                int newIndex = systemsProp.arraySize;
                systemsProp.arraySize++;
                SerializedProperty newSystemProp = systemsProp.GetArrayElementAtIndex(newIndex);
                newSystemProp.objectReferenceValue = renderSystem;
                tableManagerSO.ApplyModifiedProperties();

                Debug.Log("Added CardRenderSystem to TableManager");

                // If the TableManager is already initialized, initialize the system
                if (EditorApplication.isPlaying && TableManager.Instance != null)
                {
                    TableManager.Instance.AddSystem(renderSystem);
                }
            }
            else
            {
                Debug.Log("CardRenderSystem is already added to TableManager");
            }
        }

        private void UpdateResolution(CardUIRenderTest testComponent)
        {
            if (!EditorApplication.isPlaying)
            {
                EditorUtility.DisplayDialog("Cannot Update Resolution", "The game must be playing to update the resolution. Please enter Play mode and try again.", "OK");
                return;
            }

            // Get the current pixels per mm value
            SerializedObject so = new SerializedObject(testComponent);
            SerializedProperty pixelsPerMmProp = so.FindProperty("m_PixelsPerMm");
            int pixelsPerMm = pixelsPerMmProp.intValue;

            // Call the UpdateResolution method on the test component
            testComponent.UpdateResolution(pixelsPerMm);
        }

        private void RenderToTexture(CardUIRenderTest testComponent)
        {
            if (!EditorApplication.isPlaying)
            {
                EditorUtility.DisplayDialog("Cannot Render", "The game must be playing to render the texture. Please enter Play mode and try again.", "OK");
                return;
            }

            // Call the RenderToTexture method on the test component
            testComponent.RenderToTexture();
        }
    }
}
#endif

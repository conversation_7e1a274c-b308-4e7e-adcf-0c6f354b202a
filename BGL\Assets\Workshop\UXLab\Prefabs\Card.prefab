%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &******************
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1646689548692387020}
  - component: {fileID: -6911152551940592350}
  - component: {fileID: -7243041097371428452}
  - component: {fileID: -5770372778730494104}
  - component: {fileID: -3672269034321923574}
  - component: {fileID: 7389886598389639511}
  - component: {fileID: -8941384197875932020}
  - component: {fileID: 7088401382126066669}
  m_Layer: 0
  m_Name: Card
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1646689548692387020
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: ******************}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &-6911152551940592350
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: ******************}
  m_Mesh: {fileID: 0}
--- !u!23 &-7243041097371428452
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: ******************}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: da27ba8181716459f81d0bdc9e918f1a, type: 2}
  - {fileID: 2100000, guid: 052cea32e106f49e1a1663a1fa6e5aa6, type: 2}
  - {fileID: 2100000, guid: f551202a3e1fc1c41ac17e5a3f9f63f1, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &-5770372778730494104
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: ******************}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4a12b4ef4b1a6478da0ed0a07f9a5a9e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_CornerSegments: 8
  m_ShowGizmos: 1
  m_EditorSizeData:
    Width: 63
    Height: 88
    Thickness: 0.5
    UseUniformCorners: 1
    UniformCornerRadius: 3
    TopLeftCornerRadius: 3
    TopRightCornerRadius: 3
    BottomRightCornerRadius: 8
    BottomLeftCornerRadius: 3
    Cutouts: []
  m_CurrentSizeData:
    Width: 63
    Height: 88
    Thickness: 0.5
    UseUniformCorners: 1
    UniformCornerRadius: 3
    TopLeftCornerRadius: 3
    TopRightCornerRadius: 3
    BottomRightCornerRadius: 8
    BottomLeftCornerRadius: 3
    Cutouts: []
  m_HasAppliedData: 1
  references:
    version: 2
    RefIds: []
--- !u!114 &-3672269034321923574
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: ******************}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 888380afc233049ce9e618f9f36c8ba8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  profile: {fileID: 11400000, guid: 8d083cbf804bc3343aae77cf1b95bf3b, type: 2}
  profileSync: 1
  camerasLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  effectGroup: 1
  effectTarget: {fileID: 0}
  effectGroupLayer:
    serializedVersion: 2
    m_Bits: 4294967295
  effectNameFilter: 
  effectNameUseRegEx: 0
  combineMeshes: 0
  alphaCutOff: 0
  cullBackFaces: 1
  padding: 0
  ignoreObjectVisibility: 0
  reflectionProbes: 0
  GPUInstancing: 1
  sortingPriority: 0
  optimizeSkinnedMesh: 1
  depthClip: 0
  cameraDistanceFade: 0
  cameraDistanceFadeNear: 0
  cameraDistanceFadeFar: 1000
  normalsOption: 0
  ignore: 0
  _highlighted: 0
  fadeInDuration: 0
  fadeOutDuration: 0
  flipY: 0
  constantWidth: 1
  extraCoveragePixels: 0
  minimumWidth: 0
  subMeshMask: -1
  overlay: 0
  overlayMode: 0
  overlayColor: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
  overlayAnimationSpeed: 1
  overlayMinIntensity: 0.5
  overlayBlending: 1
  overlayTexture: {fileID: 0}
  overlayTextureUVSpace: 0
  overlayTextureScale: 1
  overlayTextureScrolling: {x: 0, y: 0}
  overlayVisibility: 0
  outline: 1
  outlineColor: {r: 0, g: 0.45961946, b: 2.1213202, a: 0.34901962}
  outlineColorStyle: 0
  outlineGradient:
    serializedVersion: 2
    key0: {r: 1, g: 1, b: 1, a: 1}
    key1: {r: 1, g: 1, b: 1, a: 1}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_ColorSpace: -1
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
  outlineGradientInLocalSpace: 0
  outlineWidth: 0.05
  outlineBlurPasses: 2
  outlineQuality: 2
  outlineEdgeMode: 0
  outlineEdgeThreshold: 0.995
  outlineSharpness: 1
  outlineDownsampling: 1
  outlineVisibility: 0
  glowBlendMode: 0
  outlineBlitDebug: 0
  outlineIndependent: 0
  outlineContourStyle: 1
  outlineMaskMode: 0
  glow: 0
  glowWidth: 0.4
  glowQuality: 3
  glowBlurMethod: 0
  glowDownsampling: 2
  glowHQColor: {r: 0.64, g: 1, b: 0, a: 1}
  glowDithering: 1
  glowDitheringStyle: 0
  glowMagicNumber1: 0.75
  glowMagicNumber2: 0.5
  glowAnimationSpeed: 1
  glowVisibility: 0
  glowBlitDebug: 0
  glowBlendPasses: 1
  glowPasses:
  - offset: 4
    alpha: 0.1
    color: {r: 0.64, g: 1, b: 0, a: 1}
  - offset: 3
    alpha: 0.2
    color: {r: 0.64, g: 1, b: 0, a: 1}
  - offset: 2
    alpha: 0.3
    color: {r: 0.64, g: 1, b: 0, a: 1}
  - offset: 1
    alpha: 0.4
    color: {r: 0.64, g: 1, b: 0, a: 1}
  glowMaskMode: 0
  innerGlow: 0
  innerGlowWidth: 1
  innerGlowColor: {r: 1, g: 1, b: 1, a: 1}
  innerGlowBlendMode: 0
  innerGlowVisibility: 0
  targetFX: 0
  targetFXTexture: {fileID: 0}
  targetFXColor: {r: 1, g: 1, b: 1, a: 1}
  targetFXCenter: {fileID: 0}
  targetFXRotationSpeed: 50
  targetFXInitialScale: 4
  targetFXEndScale: 1.5
  targetFXScaleToRenderBounds: 1
  targetFXUseEnclosingBounds: 0
  targetFXAlignToGround: 0
  targetFXOffset: {x: 0, y: 0, z: 0}
  targetFXFadePower: 32
  targetFXGroundMaxDistance: 10
  targetFXGroundLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  targetFXTransitionDuration: 0.5
  targetFXStayDuration: 1.5
  targetFXVisibility: 1
  iconFX: 0
  iconFXMesh: {fileID: 0}
  iconFXLightColor: {r: 1, g: 1, b: 1, a: 1}
  iconFXDarkColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  iconFXCenter: {fileID: 0}
  iconFXRotationSpeed: 50
  iconFXAnimationOption: 0
  iconFXAnimationAmount: 0.1
  iconFXAnimationSpeed: 3
  iconFXScale: 1
  iconFXScaleToRenderBounds: 0
  iconFXOffset: {x: 0, y: 1, z: 0}
  iconFXTransitionDuration: 0.5
  iconFXStayDuration: 1.5
  seeThrough: 2
  seeThroughOccluderMask:
    serializedVersion: 2
    m_Bits: 4294967295
  seeThroughOccluderThreshold: 0.3
  seeThroughOccluderMaskAccurate: 0
  seeThroughOccluderCheckInterval: 1
  seeThroughOccluderCheckIndividualObjects: 0
  seeThroughDepthOffset: 0
  seeThroughMaxDepth: 0
  seeThroughIntensity: 0.8
  seeThroughTintAlpha: 0.5
  seeThroughTintColor: {r: 1, g: 0, b: 0, a: 1}
  seeThroughNoise: 1
  seeThroughBorder: 0
  seeThroughBorderColor: {r: 0, g: 0, b: 0, a: 1}
  seeThroughBorderOnly: 0
  seeThroughBorderWidth: 0.45
  seeThroughOrdered: 0
  seeThroughTexture: {fileID: 0}
  seeThroughTextureUVSpace: 0
  seeThroughTextureScale: 1
  seeThroughChildrenSortingMode: 0
  rmsCount: 1
  hitFxInitialIntensity: 0
  hitFxMode: 0
  hitFxFadeOutDuration: 0.25
  hitFxColor: {r: 1, g: 1, b: 1, a: 1}
  hitFxRadius: 0.5
--- !u!64 &7389886598389639511
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: ******************}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 0}
--- !u!114 &-8941384197875932020
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: ******************}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5009cbb7e54994bb586cde7a70f34e6b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  highlightOnHover: 1
  triggerMode: 1
  raycastCamera: {fileID: 0}
  raycastSource: 0
  raycastLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  minDistance: 0
  maxDistance: 0
  respectUI: 1
  volumeLayerMask:
    serializedVersion: 2
    m_Bits: 0
  selectOnClick: 0
  selectedProfile: {fileID: 0}
  selectedAndHighlightedProfile: {fileID: 0}
  singleSelection: 0
  toggle: 0
  keepSelection: 1
--- !u!114 &7088401382126066669
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: ******************}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0c7d2a5aaae69ad46bac8eaba85f28b4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 

# Card Table Scene

This project demonstrates a simple tabletop scene with a card and camera controls.

## Features

- Tabletop with customizable size and material
- Card with customizable dimensions using CardMeshGenerator
- Card shadow for visual depth
- Smooth camera controls:
  - Zoom with mouse wheel
  - Pan with WASD keys
  - Pan with middle mouse button drag
  - Pan with right mouse button drag

## Usage

1. Open the `TableTopScene` scene in Unity
2. Press Play to see the tabletop with a card
3. Use the following controls to navigate:
   - **Zoom**: Mouse wheel
   - **Pan**: WASD keys, middle mouse button drag, or right mouse button drag

## Customization

The TableController component allows you to customize:

- Table size and appearance
- Card dimensions and appearance
- Shadow intensity and size

You can modify these properties in the Inspector for the TableTop GameObject.

## Implementation Details

- `CameraController.cs` - Handles camera movement and zoom with limits
- `TableController.cs` - Creates and manages the table and card
- `CardMeshGenerator.cs` - Generates the 3D mesh for the card with rounded corners

The card shadow is implemented as a simple quad with a transparent material positioned just above the table.
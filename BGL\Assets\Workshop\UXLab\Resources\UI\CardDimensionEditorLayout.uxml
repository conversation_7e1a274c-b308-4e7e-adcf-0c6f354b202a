<?xml version="1.0" encoding="utf-8"?>
<engine:UXML 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
    xmlns:engine="UnityEngine.UIElements" 
    xmlns:editor="UnityEditor.UIElements"
    xmlns:appui="Unity.AppUI.UI"
    xmlns:workshop="Workshop.UXLab"
    xsi:noNamespaceSchemaLocation="../../../../../UIElementsSchema/UIElements.xsd">
    
    <appui:Panel name="card-editor-panel" theme="editor-dark" scale="small" layoutDirection="Ltr" style="width: 100%; height: 100%; background-color: rgba(0,0,0,0);" picking-mode="ignore">

        <workshop:DraggablePanel name="card-editor-container" edge-padding="15" class="card-editor-container">
            <engine:VisualElement name="panel-header">
                <appui:Text text="Card Editor" class="card-editor-title" />
            </engine:VisualElement>
            
            <engine:VisualElement class="input-container">
                <appui:Text text="Card Type" class="property-label" />
                <appui:Dropdown name="card-type-dropdown" class="property-field" />
            </engine:VisualElement>
            
            <engine:VisualElement name="custom-size-container">
                <engine:VisualElement class="input-container">
                    <appui:Text text="Width" class="property-label" />
                    <appui:FloatField name="width-field" class="property-field" />
                </engine:VisualElement>
                
                <engine:VisualElement class="input-container">
                    <appui:Text text="Height" class="property-label" />
                    <appui:FloatField name="height-field" class="property-field" />
                </engine:VisualElement>
                
                <engine:VisualElement class="input-container">
                    <appui:Text text="Thickness" class="property-label" />
                    <appui:FloatField name="thickness-field" class="property-field" />
                </engine:VisualElement>
                
                <engine:VisualElement class="input-container">
                    <appui:Text text="Corner Radius" class="property-label" />
                    <appui:FloatField name="corner-radius-field" class="property-field" />
                </engine:VisualElement>
            </engine:VisualElement>
            
            <engine:VisualElement class="input-container">
                <appui:Text text="Unit" class="property-label" />
                <appui:Dropdown name="unit-dropdown" class="property-field" />
            </engine:VisualElement>
        </workshop:DraggablePanel>
    </appui:Panel>
    
</engine:UXML> 
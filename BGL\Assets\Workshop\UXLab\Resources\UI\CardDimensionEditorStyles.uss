.card-editor-container {
    width: 330px;
    position: absolute;
    top: 20px;
    right: 20px;
}

.card-editor-container.visible {
    opacity: 1;
    display: flex;
}

.card-editor-title {
    font-size: 18px;
    margin-bottom: 16px;
    color: rgb(220, 220, 220);
    -unity-font-style: bold;
    padding-bottom: 8px;
}

.input-container {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.property-label {
    width: 35%;
    color: rgb(200, 200, 200);
}

.property-field {
    width: 60%;
    min-height: 24px;
}

/* Panel styling */
.unity-app-ui-panel {
    width: 100%;
    height: 100%;
    position: absolute;
}

.appui-dropdown-item__label {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    max-width: 100%;
}

.appui-dropdown-item:hover {
    background-color: rgba(100, 100, 100, 0.6);
}
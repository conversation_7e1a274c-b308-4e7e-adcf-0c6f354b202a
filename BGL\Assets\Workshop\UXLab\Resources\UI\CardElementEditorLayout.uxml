<?xml version="1.0" encoding="utf-8"?>
<engine:UXML 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
    xmlns:engine="UnityEngine.UIElements" 
    xmlns:editor="UnityEditor.UIElements"
    xmlns:appui="Unity.AppUI.UI"
    xmlns:workshop="Workshop.UXLab"
    xsi:noNamespaceSchemaLocation="../../../../../UIElementsSchema/UIElements.xsd">
    
    <appui:Panel name="card-component-editor-panel" theme="editor-dark" scale="small" layoutDirection="Ltr" style="width: 100%; height: 100%; background-color: rgba(0,0,0,0);" picking-mode="ignore">

        <workshop:DraggablePanel name="card-component-editor-container" edge-padding="15" class="card-component-editor-container">
            <engine:VisualElement name="panel-header">
                <appui:Text text="Component Editor" class="component-editor-title" />
            </engine:VisualElement>

            <!-- Content Area -->
            <engine:ScrollView name="component-scroll-view" mode="Vertical">
                <engine:VisualElement name="component-properties" style="padding: 5px;">
                    
                    <engine:VisualElement name="no-component-selected" style="align-items: center; margin-top: 20px;">
                        <appui:Text text="Select a layer to edit its properties." style="color: rgb(180, 180, 180); -unity-font-style: italic;"/>
                    </engine:VisualElement>
                    
                    <engine:VisualElement name="selected-component-fields">
                        <!-- Basic Properties -->
                        <engine:VisualElement class="input-container">
                            <appui:Text text="Name" class="property-label" />
                            <appui:TextField name="component-name-field" class="property-field" />
                        </engine:VisualElement>

                        <appui:Divider class="section-divider"/>
                        <appui:Text text="Transform" class="section-header" />

                        <!-- Units Dropdown -->
                        <engine:VisualElement class="input-container">
                            <appui:Text text="Unit" class="property-label" />
                            <appui:Dropdown name="unit-dropdown" class="property-field" />
                        </engine:VisualElement>
                        
                        <!-- Position (X, Y) -->
                        <engine:VisualElement class="input-container">
                            <appui:Text text="Position" class="property-label" />
                            <appui:Vector2Field name="component-position-field" class="property-field" />
                        </engine:VisualElement>
                        
                        <!-- Size (Width, Height) -->
                        <engine:VisualElement class="input-container">
                            <appui:Text text="Size" class="property-label" />
                            <appui:Vector2Field name="component-size-field" class="property-field" />
                        </engine:VisualElement>

                        <!-- Rotation (Z) -->
                         <engine:VisualElement class="input-container">
                            <appui:Text text="Rotation (° Z)" class="property-label" />
                            <appui:FloatField name="component-rotation-field" class="property-field" />
                        </engine:VisualElement>

                        <appui:Divider class="section-divider"/>
                        <appui:Text text="Component Specific" class="section-header" />
                        <engine:VisualElement name="component-specific-fields">
                            <!-- Dynamically add fields here based on component type -->
                             <appui:Text text="No specific properties found." style="color: rgb(180, 180, 180); -unity-font-style: italic; margin-left: 10px; margin-bottom: 10px;"/>
                        </engine:VisualElement>

                         <appui:Divider class="section-divider"/>
                        <appui:Text text="Advanced" class="section-header" />
                        <engine:VisualElement name="advanced-actions">
                            <!-- Placeholder for Mirroring -->
                             <engine:VisualElement class="input-container">
                                 <appui:Text text="Mirror" class="property-label" />
                                 <engine:VisualElement style="flex-direction: row;">
                                    <appui:ActionButton name="mirror-x-button" label="X" class="small-action-button"/>
                                    <appui:ActionButton name="mirror-y-button" label="Y" class="small-action-button"/>
                                 </engine:VisualElement>
                            </engine:VisualElement>
                            <!-- Placeholder for Duplication -->
                             <engine:VisualElement class="input-container">
                                 <appui:Text text="Duplicate to Quadrants" class="property-label" />
                                <appui:ActionButton name="duplicate-button" label="Duplicate" class="action-button"/>
                             </engine:VisualElement>
                        </engine:VisualElement>

                    </engine:VisualElement> <!-- End selected-component-fields -->

                </engine:VisualElement>
            </engine:ScrollView>

        </workshop:DraggablePanel>
    </appui:Panel>
    
</engine:UXML> 
/* Resources/UI/CardElementEditorStyles.uss */
.card-component-editor-container {
    width: 330px; /* Same as original dimension editor */
    position: absolute;
    top: 280px; /* Position below default layer panel pos */
    right: 370px; /* Align with layer panel */
    flex-direction: column;
    min-height: 300px;
    max-height: 600px; /* Allow more vertical space */
}

.card-component-editor-container.visible {
    opacity: 1;
    display: flex; /* Set display to flex when visible */
}

.component-editor-title {
    font-size: 18px;
    margin-bottom: 10px;
    color: rgb(220, 220, 220);
    -unity-font-style: bold;
    padding-bottom: 8px;
    border-bottom-width: 1px;
    border-bottom-color: rgb(80, 80, 80);
}

/* ScrollView takes up remaining space */
#component-scroll-view {
    flex-grow: 1;
}

/* Common input row styling */
.input-container {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px; /* Consistent spacing */
    min-height: 24px;
}

.property-label {
    width: 35%; /* Adjust as needed */
    color: rgb(200, 200, 200);
    margin-right: 5px;
}

.property-field {
    width: 60%; /* Adjust as needed */
    min-height: 24px; /* Ensure fields are clickable */
}

/* Section Dividers and Headers */
.section-divider {
    height: 1px;
    background-color: rgb(80, 80, 80);
    margin-top: 15px;
    margin-bottom: 10px;
}

.section-header {
    font-size: 14px;
    -unity-font-style: bold;
    color: rgb(190, 190, 190);
    margin-bottom: 12px;
}

/* Action Buttons */
.action-button {
     min-width: 80px;
     height: 24px;
}
.small-action-button {
     min-width: 30px;
     height: 24px;
     margin-right: 5px;
}

/* Default states for internal containers (controlled by C# now) */
/* No need for specific display rules here, C# handles it */
#no-component-selected {
     /* Styles like alignment, margin, etc. are fine */
     align-items: center; 
     margin-top: 20px;
}
#selected-component-fields {
     /* Styles like padding, direction are fine */
     padding: 5px;
     flex-direction: column; 
}

/* REMOVED - These are now controlled directly by C# in SetActive/ClearActive */
/*
.card-component-editor-container.component-selected #no-component-selected {
    display: none;
}
.card-component-editor-container.component-selected #selected-component-fields {
    display: flex;
    flex-direction: column; 
}
*/

/* Placeholder text styling */
.placeholder-text {
    color: rgb(180, 180, 180);
    -unity-font-style: italic;
    margin-left: 10px; 
    margin-bottom: 10px;
    font-size: 12px; /* Optional: make it slightly smaller */
} 
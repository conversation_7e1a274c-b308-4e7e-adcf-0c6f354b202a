<?xml version="1.0" encoding="utf-8"?>
<engine:UXML
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:engine="UnityEngine.UIElements"
    xmlns:editor="UnityEditor.UIElements"
    xmlns:appui="Unity.AppUI.UI"
    xmlns:workshop="Workshop.UXLab"
    xsi:noNamespaceSchemaLocation="../../../../../UIElementsSchema/UIElements.xsd">

    <appui:Panel name="card-layers-panel" theme="editor-dark" scale="small" layoutDirection="Ltr" style="width: 100%; height: 100%; background-color: rgba(0,0,0,0);" picking-mode="ignore">

        <workshop:DraggablePanel name="card-layers-container" edge-padding="15" class="card-layers-container">
            <engine:VisualElement name="panel-header">
                <appui:LocalizedTextElement text="Layers" class="card-layers-title" />
            </engine:VisualElement>

            <!-- List View for Card Components -->
            <engine:ListView name="layer-list" selection-type="Single" reorderable="false" fixed-item-height="28" style="flex-grow: 1; min-height: 100px; margin-bottom: 10px;"/>

            <!-- Action Buttons -->
            <engine:VisualElement name="layer-actions" style="flex-direction: row; justify-content: space-between; align-items: center; min-height: 30px;">
                <appui:Button name="add-component-button" title="Add" class="layer-button"/>
                <appui:Button name="remove-component-button" title="Remove" class="layer-button" disabled="true"/>
                <engine:VisualElement style="flex-grow: 1;" /> <!-- Spacer -->
                <appui:Button name="move-up-button" title="▲" class="layer-button layer-order-button" disabled="true"/>
                <appui:Button name="move-down-button" title="▼" class="layer-button layer-order-button" disabled="true"/>
            </engine:VisualElement>

        </workshop:DraggablePanel>
    </appui:Panel>

</engine:UXML>
/* Resources/UI/CardLayersEditorStyles.uss */
.card-layers-container {
    width: 280px; /* Slightly narrower */
    /* Start position (will be overridden by DraggablePanel persistence or default placement logic) */
    position: absolute;
    top: 20px;
    right: 370px; /* Position next to dimension editor */
    flex-direction: column; /* Ensure vertical layout */
    min-height: 200px; /* Minimum height */
    max-height: 500px; /* Max height before list scrolls */
}

.card-layers-container.visible {
    opacity: 1;
    display: flex;
}

.card-layers-title {
    font-size: 18px;
    margin-bottom: 10px; /* Reduced margin */
    color: rgb(220, 220, 220);
    -unity-font-style: bold;
    padding-bottom: 8px;
    border-bottom-width: 1px;
    border-bottom-color: rgb(80, 80, 80);
}

/* List View Styling */
#layer-list {
    /* Background or border if needed */
}

/* List Item Styling (Applied by control script to the template) */
.layer-list-item {
    flex-direction: row;
    align-items: center;
    padding: 4px 6px; /* Padding within the item */
    height: 100%;
}

.layer-list-item__visibility-toggle {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    /* Custom styling for the eye icon or checkmark could go here */
}

.layer-list-item__label {
    flex-grow: 1;
    color: rgb(200, 200, 200);
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

/* Selected Item Highlighting */
.unity-list-view__item--selected .layer-list-item {
    background-color: rgba(80, 120, 200, 0.5); /* Example selection color */
}
.unity-list-view__item--selected .layer-list-item__label {
    color: rgb(250, 250, 250);
}

/* Button Styling */
.layer-button {
    min-width: 50px;
    margin-left: 5px;
    margin-right: 5px;
}

.layer-order-button {
    min-width: 30px; /* Smaller buttons for up/down */
}

/* Styles for disabled state (AppUI handles basic disabling, but can customize) */
.layer-button:disabled {
    opacity: 0.5;
} 
<?xml version="1.0" encoding="utf-8"?>
<engine:UXML 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
    xmlns:engine="UnityEngine.UIElements" 
    xmlns:appui="Unity.AppUI.UI"
    xmlns:workshop="Workshop.UXLab"
    xsi:noNamespaceSchemaLocation="../../../../../UIElementsSchema/UIElements.xsd">
    
    <!-- Container for all panel content with padding for shadow - this will be the drag target by default -->
    <engine:VisualElement name="draggable-panel__content-container" class="draggable-panel__content-container panel-drag-handle" picking-mode="position">
    
    </engine:VisualElement>
    
</engine:UXML> 
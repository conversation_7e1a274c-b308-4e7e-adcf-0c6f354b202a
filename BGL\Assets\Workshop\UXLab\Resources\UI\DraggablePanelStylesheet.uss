.draggable-panel {
    position: absolute;

    border-radius: 8px;
    --box-shadow-offset-x: 4;
    --box-shadow-offset-y: 6;
    /*--box-shadow-spread: 0;*/
    --box-shadow-color: rgba(0, 0, 0, 0.651);
    transition: opacity 0.3s ease-in-out;
}

.panel-drag-handle {
    padding: 8px;
    -unity-text-align: middle-left;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    border-bottom-width: 1px;
    border-bottom-color: rgba(120, 120, 120, 0.6);
    cursor: move-arrow;
}

.draggable-panel__content-container {
    background-color: rgba(40, 40, 40, 0.9);
    border-radius: 8px;
    border-width: 1px;
    border-color: rgba(120, 120, 120, 0.6);
    padding: 12px;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.drag-handle {
    cursor: move-arrow;
}

.hidden {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    display: none;
    transition: display 0.3s linear 0.3s;
}
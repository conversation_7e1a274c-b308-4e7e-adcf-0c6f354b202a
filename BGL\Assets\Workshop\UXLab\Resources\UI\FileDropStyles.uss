/* File Drop Styles */

/* Base file drop zone styling */
.file-drop-zone {
    border-width: 2px;
    border-style: dashed;
    border-color: rgba(128, 128, 128, 0.5);
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.02);
    transition-property: border-color, background-color, transform;
    transition-duration: 0.2s;
    transition-timing-function: ease-out;
    min-height: 100px;
    min-width: 100px;
    justify-content: center;
    align-items: center;
}

/* Hover state when files are being dragged over */
.file-drop-hover {
    border-color: rgba(100, 150, 255, 0.8);
    background-color: rgba(100, 150, 255, 0.1);
    transform: scale(1.02);
    transition-duration: 0.1s;
}

/* Active state when files are being dropped */
.file-drop-active {
    border-color: rgba(50, 200, 50, 1.0);
    background-color: rgba(50, 200, 50, 0.15);
    transform: scale(1.05);
}

/* Error state for invalid file types */
.file-drop-error {
    border-color: rgba(255, 100, 100, 0.8);
    background-color: rgba(255, 100, 100, 0.1);
    animation-name: shake;
    animation-duration: 0.5s;
    animation-timing-function: ease-in-out;
}

/* Disabled state */
.file-drop-disabled {
    border-color: rgba(128, 128, 128, 0.2);
    background-color: rgba(128, 128, 128, 0.05);
    opacity: 0.5;
}

/* File drop zone with image preview */
.file-drop-zone-with-preview {
    flex-direction: column;
    padding: 10px;
}

.file-drop-zone-with-preview .file-drop-preview {
    max-width: 80%;
    max-height: 80%;
    margin-bottom: 10px;
}

.file-drop-zone-with-preview .file-drop-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
}

/* Small file drop zones */
.file-drop-zone-small {
    min-height: 60px;
    min-width: 60px;
    border-radius: 4px;
}

/* Large file drop zones */
.file-drop-zone-large {
    min-height: 200px;
    min-width: 200px;
    border-radius: 12px;
}

/* File drop overlay for full-screen drops */
.file-drop-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition-property: opacity, visibility;
    transition-duration: 0.3s;
    z-index: 1000;
}

.file-drop-overlay.visible {
    opacity: 1;
    visibility: visible;
}

.file-drop-overlay-content {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 40px;
    border-width: 3px;
    border-style: dashed;
    border-color: rgba(100, 150, 255, 0.8);
    text-align: center;
}

.file-drop-overlay-title {
    font-size: 24px;
    color: white;
    margin-bottom: 10px;
    font-weight: bold;
}

.file-drop-overlay-subtitle {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
}

/* File type indicators using CSS classes */
.file-drop-zone.image-files {
    border-color: rgba(255, 150, 50, 0.5);
}

.file-drop-zone.image-files.file-drop-hover {
    border-color: rgba(255, 150, 50, 0.8);
    background-color: rgba(255, 150, 50, 0.1);
}

.file-drop-zone.document-files {
    border-color: rgba(50, 150, 255, 0.5);
}

.file-drop-zone.document-files.file-drop-hover {
    border-color: rgba(50, 150, 255, 0.8);
    background-color: rgba(50, 150, 255, 0.1);
}

.file-drop-zone.any-files {
    border-color: rgba(150, 255, 150, 0.5);
}

.file-drop-zone.any-files.file-drop-hover {
    border-color: rgba(150, 255, 150, 0.8);
    background-color: rgba(150, 255, 150, 0.1);
}

.file-drop-zone.audio-files {
    border-color: rgba(150, 50, 255, 0.5);
}

.file-drop-zone.audio-files.file-drop-hover {
    border-color: rgba(150, 50, 255, 0.8);
    background-color: rgba(150, 50, 255, 0.1);
}

/* Animation keyframes */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Utility classes */
.file-drop-pulse {
    animation-name: pulse;
    animation-duration: 1s;
    animation-iteration-count: infinite;
    animation-timing-function: ease-in-out;
}

.file-drop-fade-in {
    animation-name: fadeIn;
    animation-duration: 0.3s;
    animation-timing-function: ease-out;
}

/* Dark theme adjustments */
.unity-theme-dark .file-drop-zone {
    border-color: rgba(200, 200, 200, 0.3);
    background-color: rgba(255, 255, 255, 0.02);
}

.unity-theme-dark .file-drop-hover {
    border-color: rgba(100, 150, 255, 0.8);
    background-color: rgba(100, 150, 255, 0.1);
}

/* Light theme adjustments */
.unity-theme-light .file-drop-zone {
    border-color: rgba(100, 100, 100, 0.5);
    background-color: rgba(0, 0, 0, 0.02);
}

.unity-theme-light .file-drop-hover {
    border-color: rgba(50, 100, 200, 0.8);
    background-color: rgba(50, 100, 200, 0.1);
} 
<?xml version="1.0" encoding="utf-8"?>
<engine:UXML
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:engine="UnityEngine.UIElements"
    xmlns:editor="UnityEditor.UIElements"
    xsi:noNamespaceSchemaLocation="../../../UIElementsSchema/UIElements.xsd">

    <engine:VisualElement name="screen-overlay" class="screen-overlay">
        <engine:VisualElement name="inner-shadow-left" class="inner-shadow left" />
        <engine:VisualElement name="inner-shadow-right" class="inner-shadow right" />
        <engine:VisualElement name="inner-shadow-top" class="inner-shadow top" />
        <engine:VisualElement name="inner-shadow-bottom" class="inner-shadow bottom" />
        <!--engine:VisualElement name="inner-shadow-corner-tl" class="inner-shadow corner top-left" />
        <engine:VisualElement name="inner-shadow-corner-tr" class="inner-shadow corner top-right" />
        <engine:VisualElement name="inner-shadow-corner-bl" class="inner-shadow corner bottom-left" />
        <engine:VisualElement name="inner-shadow-corner-br" class="inner-shadow corner bottom-right" /-->
    </engine:VisualElement>

</engine:UXML> 
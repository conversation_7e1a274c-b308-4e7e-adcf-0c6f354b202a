.screen-overlay {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    background-color: rgba(0, 0, 0, 0);
}

.inner-shadow {
    position: absolute;

    /*--box-shadow-spread: 0;
    --box-shadow-blur: 0;
    --box-shadow-color: rgba(0,0,0,.5);*/
    background-color: rgba(0,0,0,.5);
}

.inner-shadow.left {
    left: 0;
    /*top: 250px;
    bottom: 250px;*/
    top: 0px;
    bottom: 0px;
    width: 50px;
}

.inner-shadow.right {
    right: 0;
    top: 0px;
    bottom: 0px;
    width: 50px;
}

.inner-shadow.top {
    top: 0;
    left: 50px;
    right: 50px;
    height: 50px;
}

.inner-shadow.bottom {
    bottom: 0;
    left: 50px;
    right: 50px;
    height: 50px;
}

.inner-shadow.corner {
    width: 250px;
    height: 250px;
}

.inner-shadow.corner.top-left {
    left: 0;
    top: 0;
}

.inner-shadow.corner.top-right {
    right: 0;
    top: 0;
}

.inner-shadow.corner.bottom-left {
    left: 0;
    bottom: 0;
}

.inner-shadow.corner.bottom-right {
    right: 0;
    bottom: 0;
} 
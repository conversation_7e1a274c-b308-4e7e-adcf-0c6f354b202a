using UnityEngine;
using UnityEngine.UIElements;

namespace Workshop.UXLab
{
    [CreateAssetMenu(fileName = "ScreenOverlaySystem", menuName = "Systems/ScreenOverlaySystem")]
    public class ScreenOverlaySystem : TableSystem
    {
        private UIDocument m_Document;
        private GameObject m_HostGameObject;

        public VisualTreeAsset OverlayLayout;
        public StyleSheet OverlayStyleSheet;

        public override void Init(TableManager tableManager)
        {
            base.Init(tableManager);
            CreateUIHost();
        }

        private void CreateUIHost()
        {
            // Create a GameObject to host the UI
            m_HostGameObject = new GameObject("ScreenOverlay_UIHost");
            m_Document = m_HostGameObject.AddComponent<UIDocument>();

            // Load the UXML and USS
            if (OverlayLayout != null)
            {
                m_Document.visualTreeAsset = OverlayLayout;
                if (OverlayStyleSheet != null)
                {
                    m_Document.rootVisualElement.styleSheets.Add(OverlayStyleSheet);
                }
                else
                {
                    Debug.LogError("Failed to load ScreenOverlayStylesheet");
                }
            }
            else
            {
                Debug.LogError("Failed to load ScreenOverlay UXML");
            }

            // Set up the panel settings
            m_Document.panelSettings = m_TableManager.UIPanelSettings;
        }

        public override void Shutdown()
        {
            if (m_HostGameObject != null)
            {
                Object.Destroy(m_HostGameObject);
                m_HostGameObject = null;
            }

            base.Shutdown();
        }
    }
}
%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-1555468459129566335
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 473a86c9e274347dfbdde619584cebe9, type: 3}
  m_Name: HighlightPlusRenderPassFeature
  m_EditorClassIdentifier: 
  m_Active: 1
  renderPassEvent: 500
  clearStencil: 0
  previewInEditMode: 1
  showInPreviewCamera: 1
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: de640fe3d0db1804a85f9fc8f5cadab6, type: 3}
  m_Name: UX Lab Render Settings
  m_EditorClassIdentifier: 
  debugShaders:
    debugReplacementPS: {fileID: 4800000, guid: cf852408f2e174538bcd9b7fda1c5ae7, type: 3}
    hdrDebugViewPS: {fileID: 4800000, guid: 573620ae32aec764abd4d728906d2587, type: 3}
    probeVolumeSamplingDebugComputeShader: {fileID: 7200000, guid: 53626a513ea68ce47b59dc1299fe3959, type: 3}
  probeVolumeResources:
    probeVolumeDebugShader: {fileID: 0}
    probeVolumeFragmentationDebugShader: {fileID: 0}
    probeVolumeOffsetDebugShader: {fileID: 0}
    probeVolumeSamplingDebugShader: {fileID: 0}
    probeSamplingDebugMesh: {fileID: 0}
    probeSamplingDebugTexture: {fileID: 0}
    probeVolumeBlendStatesCS: {fileID: 0}
  m_RendererFeatures:
  - {fileID: 2123960991996572544}
  - {fileID: 7958167673557313567}
  - {fileID: -1555468459129566335}
  m_RendererFeatureMap: 80f3bddc44d3791d1f6861bd5517716e810b427aaddd69ea
  m_UseNativeRenderPass: 0
  xrSystemData: {fileID: 0}
  postProcessData: {fileID: 11400000, guid: 41439944d30ece34e96484bdb6645b55, type: 2}
  m_AssetVersion: 2
  m_OpaqueLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_TransparentLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_DefaultStencilState:
    overrideStencilState: 0
    stencilReference: 0
    stencilCompareFunction: 8
    passOperation: 2
    failOperation: 0
    zFailOperation: 0
  m_ShadowTransparentReceive: 1
  m_RenderingMode: 2
  m_DepthPrimingMode: 1
  m_CopyDepthMode: 0
  m_DepthAttachmentFormat: 94
  m_DepthTextureFormat: 93
  m_AccurateGbufferNormals: 0
  m_IntermediateTextureMode: 1
--- !u!114 &2123960991996572544
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a1614fc811f8f184697d9bee70ab9fe5, type: 3}
  m_Name: DecalRendererFeature
  m_EditorClassIdentifier: 
  m_Active: 1
  m_Settings:
    technique: 1
    maxDrawDistance: 1000
    decalLayers: 1
    dBufferSettings:
      surfaceData: 2
    screenSpaceSettings:
      normalBlend: 0
--- !u!114 &7958167673557313567
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e32e72b6a3f58a24c9033e2b9275eea0, type: 3}
  m_Name: ScreenSpaceShadows
  m_EditorClassIdentifier: 
  m_Active: 0
  m_Shader: {fileID: 4800000, guid: 0f854b35a0cf61a429bd5dcfea30eddd, type: 3}

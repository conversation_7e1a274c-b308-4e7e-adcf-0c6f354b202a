%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 2
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0.4471771, g: 0.4970975, b: 0.57515424, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &4
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 1
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 1
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 1024
    m_ReflectionCompression: 1
    m_MixedBakeMode: 1
    m_BakeBackend: 0
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 500
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 112000014, guid: 039bfaf33d1112a4d904b7d393a6e9bb,
    type: 2}
  m_UseShadowmask: 0
--- !u!196 &5
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &165230857
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 102722, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 165230858}
  m_Layer: 0
  m_Name: CalibrationWalls
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &165230858
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 431536, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 165230857}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1165184421}
  - {fileID: 181859161}
  - {fileID: 1435719952}
  - {fileID: 1525858954}
  m_Father: {fileID: 1694850532}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &181859160
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 125776, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 181859161}
  - component: {fileID: 181859164}
  - component: {fileID: 181859163}
  - component: {fileID: 181859162}
  m_Layer: 0
  m_Name: CalibrationWallRearRight
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 65
  m_IsActive: 1
--- !u!4 &181859161
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 494730, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 181859160}
  m_LocalRotation: {x: 0.5, y: 0.5, z: -0.5000001, w: 0.49999994}
  m_LocalPosition: {x: -2.5, y: 2.5, z: 0}
  m_LocalScale: {x: 0.5, y: 1, z: 0.49999997}
  m_Children: []
  m_Father: {fileID: 165230858}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &181859162
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2304408, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 181859160}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 0062779a7c303451ab69ea6fc7893dc8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &181859163
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 6442406, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 181859160}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!33 &181859164
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3378452, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 181859160}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &271039439
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 271039440}
  m_Layer: 0
  m_Name: --- Other Scene Stuff ---
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &271039440
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 271039439}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -4.729038, y: -3.3103254, z: -4.729038}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &472769855
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100000, guid: e11f662626928b14e8f57acb029360b9,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 472769856}
  - component: {fileID: 472769857}
  m_Layer: 0
  m_Name: DirectionalLight
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &472769856
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400000, guid: e11f662626928b14e8f57acb029360b9,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 472769855}
  m_LocalRotation: {x: 0.4144551, y: -0.7420336, z: 0.5253307, w: -0.040507175}
  m_LocalPosition: {x: 0, y: 2, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!108 &472769857
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 10800000, guid: e11f662626928b14e8f57acb029360b9,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 472769855}
  m_Enabled: 1
  serializedVersion: 10
  m_Type: 1
  m_Shape: 0
  m_Color: {r: 1, g: 0.9885849, b: 0.95686275, a: 1}
  m_Intensity: 1
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.802082
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.01
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
--- !u!1 &567592204
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 567592205}
  - component: {fileID: 567592208}
  - component: {fileID: 567592207}
  - component: {fileID: 567592206}
  m_Layer: 0
  m_Name: SmallWall
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &567592205
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 567592204}
  m_LocalRotation: {x: 0, y: 0.34513432, z: 0, w: 0.93855333}
  m_LocalPosition: {x: 1.42, y: 0.34, z: -0.77}
  m_LocalScale: {x: 2, y: 1, z: 0.2}
  m_Children: []
  m_Father: {fileID: 1694850532}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 40.38, z: 0}
--- !u!23 &567592206
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 567592204}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 0062779a7c303451ab69ea6fc7893dc8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!65 &567592207
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 567592204}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!33 &567592208
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 567592204}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &613470776
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 613470777}
  - component: {fileID: 613470779}
  - component: {fileID: 613470778}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &613470777
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 613470776}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1605869163}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.1}
  m_AnchorMax: {x: 1, y: 0.9}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &613470778
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 613470776}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 14
    m_FontStyle: 0
    m_BestFit: 1
    m_MinSize: 10
    m_MaxSize: 90
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: '<color=yellow><b>HIGHLIGHT PLUS</b> HIT FX DEMO</color>

    Click on
    any capsule to trigger the effect.'
--- !u!222 &613470779
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 613470776}
  m_CullTransparentMesh: 0
--- !u!1 &617211756
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 195550, guid: d7720c2417695eb45bd70f6d10a17068,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 617211757}
  - component: {fileID: 617211762}
  - component: {fileID: 617211760}
  - component: {fileID: 617211759}
  - component: {fileID: 617211758}
  - component: {fileID: 617211761}
  m_Layer: 1
  m_Name: Target 3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &617211757
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 403562, guid: d7720c2417695eb45bd70f6d10a17068,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 617211756}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.97, y: 0.65, z: -0.97}
  m_LocalScale: {x: 0.65, y: 0.65, z: 0.65}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &617211758
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 617211756}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 888380afc233049ce9e618f9f36c8ba8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  profile: {fileID: 0}
  profileSync: 1
  previewInEditor: 1
  camerasLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  effectGroup: 0
  effectGroupLayer:
    serializedVersion: 2
    m_Bits: 4294967295
  effectNameFilter: 
  combineMeshes: 0
  alphaCutOff: 0
  cullBackFaces: 1
  ignoreObjectVisibility: 0
  reflectionProbes: 0
  GPUInstancing: 1
  normalsOption: 0
  ignore: 0
  _highlighted: 0
  fadeInDuration: 0
  fadeOutDuration: 0
  flipY: 0
  constantWidth: 1
  subMeshMask: -1
  overlay: 0.5
  overlayColor: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
  overlayAnimationSpeed: 1
  overlayMinIntensity: 0.5
  overlayBlending: 1
  overlayTexture: {fileID: 0}
  overlayTextureScale: 1
  outline: 1
  outlineColor: {r: 0, g: 0, b: 0, a: 1}
  outlineWidth: 0.5
  outlineQuality: 0
  outlineDownsampling: 2
  outlineVisibility: 0
  outlineBlitDebug: 0
  outlineIndependent: 0
  glow: 1
  glowWidth: 0.89
  glowQuality: 2
  glowDownsampling: 2
  glowHQColor: {r: 1, g: 0.52205884, b: 0.52205884, a: 1}
  glowDithering: 1
  glowMagicNumber1: 0.75
  glowMagicNumber2: 0.5
  glowAnimationSpeed: 1
  glowVisibility: 0
  glowBlitDebug: 0
  glowBlendPasses: 1
  glowPasses:
  - offset: 4
    alpha: 0.1
    color: {r: 0.64, g: 1, b: 0, a: 1}
  - offset: 3
    alpha: 0.2
    color: {r: 0.64, g: 1, b: 0, a: 1}
  - offset: 2
    alpha: 0.3
    color: {r: 0.64, g: 1, b: 0, a: 1}
  - offset: 1
    alpha: 0.4
    color: {r: 0.64, g: 1, b: 0, a: 1}
  innerGlow: 0.79
  innerGlowWidth: 1
  innerGlowColor: {r: 1, g: 1, b: 1, a: 1}
  innerGlowVisibility: 0
  targetFX: 0
  targetFXTexture: {fileID: 0}
  targetFXColor: {r: 1, g: 1, b: 1, a: 1}
  targetFXCenter: {fileID: 0}
  targetFXRotationSpeed: 30
  targetFXInitialScale: 2
  targetFXEndScale: 1.1
  targetFXScaleToRenderBounds: 1
  targetFXAlignToGround: 0
  targetFXFadePower: 32
  targetFXGroundMaxDistance: 10
  targetFXGroundLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  targetFXTransitionDuration: 0.5
  targetFXStayDuration: 2
  targetFXVisibility: 1
  seeThrough: 1
  seeThroughOccluderMask:
    serializedVersion: 2
    m_Bits: 4294967295
  seeThroughOccluderThreshold: 0.3
  seeThroughOccluderMaskAccurate: 0
  seeThroughOccluderCheckInterval: 1
  seeThroughOccluderCheckIndividualObjects: 0
  seeThroughDepthOffset: 0
  seeThroughMaxDepth: 0
  seeThroughIntensity: 0.8
  seeThroughTintAlpha: 0.5
  seeThroughTintColor: {r: 1, g: 0, b: 0, a: 1}
  seeThroughNoise: 1
  seeThroughBorder: 0
  seeThroughBorderColor: {r: 0, g: 0, b: 0, a: 1}
  seeThroughBorderWidth: 0.45
  seeThroughOrdered: 0
  rmsCount: 1
  hitFxInitialIntensity: 1
  hitFxMode: 0
  hitFxFadeOutDuration: 0.25
  hitFxColor: {r: 1, g: 0.83026266, b: 0, a: 1}
  hitFxRadius: 0.5
--- !u!114 &617211759
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 617211756}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5009cbb7e54994bb586cde7a70f34e6b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  highlightOnHover: 1
  triggerMode: 1
  raycastCamera: {fileID: 965526243}
  raycastSource: 0
  minDistance: 0
  maxDistance: 0
  respectUI: 1
  volumeLayerMask:
    serializedVersion: 2
    m_Bits: 0
  selectOnClick: 0
  selectedProfile: {fileID: 0}
  selectedAndHighlightedProfile: {fileID: 0}
  singleSelection: 0
  toggle: 0
--- !u!23 &617211760
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2319362, guid: d7720c2417695eb45bd70f6d10a17068,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 617211756}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 3
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3134ae80e55101b468fe9ea362327f28, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 617211757}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!136 &617211761
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 617211756}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  m_Radius: 0.5
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0, y: 0, z: 0}
--- !u!33 &617211762
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3336432, guid: d7720c2417695eb45bd70f6d10a17068,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 617211756}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &640476129
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 640476130}
  m_Layer: 0
  m_Name: --- Sample Targets ---
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &640476130
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 640476129}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -4.729038, y: -3.3103254, z: -4.729038}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &656694097
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 158430, guid: d7720c2417695eb45bd70f6d10a17068,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 656694098}
  - component: {fileID: 656694104}
  - component: {fileID: 656694102}
  - component: {fileID: 656694101}
  - component: {fileID: 656694100}
  - component: {fileID: 656694099}
  - component: {fileID: 656694103}
  m_Layer: 1
  m_Name: Target 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &656694098
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 494476, guid: d7720c2417695eb45bd70f6d10a17068,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 656694097}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.85, y: 0.5, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &656694099
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 656694097}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 36aa3934b8f65413188fabe723ded4da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &656694100
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 656694097}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 888380afc233049ce9e618f9f36c8ba8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  profile: {fileID: 0}
  profileSync: 1
  previewInEditor: 1
  camerasLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  effectGroup: 1
  effectGroupLayer:
    serializedVersion: 2
    m_Bits: 18
  effectNameFilter: 
  combineMeshes: 0
  alphaCutOff: 0
  cullBackFaces: 1
  ignoreObjectVisibility: 0
  reflectionProbes: 0
  GPUInstancing: 1
  normalsOption: 0
  ignore: 0
  _highlighted: 0
  fadeInDuration: 0
  fadeOutDuration: 0.5
  flipY: 0
  constantWidth: 1
  subMeshMask: -1
  overlay: 0.05
  overlayColor: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
  overlayAnimationSpeed: 0
  overlayMinIntensity: 0.5
  overlayBlending: 1
  overlayTexture: {fileID: 2800000, guid: aeb7aff1c8c1241ee8bfdfc05e9a9e92, type: 3}
  overlayTextureScale: 1
  outline: 1
  outlineColor: {r: 0, g: 0, b: 0, a: 1}
  outlineWidth: 0.4
  outlineQuality: 0
  outlineDownsampling: 2
  outlineVisibility: 0
  outlineBlitDebug: 0
  outlineIndependent: 0
  glow: 1.78
  glowWidth: 3.42
  glowQuality: 2
  glowDownsampling: 2
  glowHQColor: {r: 0.64, g: 1, b: 0, a: 1}
  glowDithering: 1
  glowMagicNumber1: 0.75
  glowMagicNumber2: 0.5
  glowAnimationSpeed: 0.2
  glowVisibility: 0
  glowBlitDebug: 0
  glowBlendPasses: 1
  glowPasses:
  - offset: 4
    alpha: 0.1
    color: {r: 0.64, g: 1, b: 0, a: 1}
  - offset: 3
    alpha: 0.2
    color: {r: 0.64, g: 1, b: 0, a: 1}
  - offset: 2
    alpha: 0.3
    color: {r: 0.64, g: 1, b: 0, a: 1}
  - offset: 1
    alpha: 0.4
    color: {r: 0.64, g: 1, b: 0, a: 1}
  innerGlow: 0
  innerGlowWidth: 1
  innerGlowColor: {r: 1, g: 1, b: 1, a: 1}
  innerGlowVisibility: 0
  targetFX: 0
  targetFXTexture: {fileID: 2800000, guid: 1de3c566a6c8c405b9f6f453137273ec, type: 3}
  targetFXColor: {r: 1, g: 1, b: 1, a: 1}
  targetFXCenter: {fileID: 0}
  targetFXRotationSpeed: 30
  targetFXInitialScale: 4
  targetFXEndScale: 1.5
  targetFXScaleToRenderBounds: 1
  targetFXAlignToGround: 0
  targetFXFadePower: 32
  targetFXGroundMaxDistance: 10
  targetFXGroundLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  targetFXTransitionDuration: 0.5
  targetFXStayDuration: 1.5
  targetFXVisibility: 1
  seeThrough: 1
  seeThroughOccluderMask:
    serializedVersion: 2
    m_Bits: 4294967295
  seeThroughOccluderThreshold: 0.3
  seeThroughOccluderMaskAccurate: 0
  seeThroughOccluderCheckInterval: 1
  seeThroughOccluderCheckIndividualObjects: 0
  seeThroughDepthOffset: 0
  seeThroughMaxDepth: 0
  seeThroughIntensity: 0.8
  seeThroughTintAlpha: 0.5
  seeThroughTintColor: {r: 1, g: 0, b: 0, a: 1}
  seeThroughNoise: 1
  seeThroughBorder: 0
  seeThroughBorderColor: {r: 0, g: 0, b: 0, a: 1}
  seeThroughBorderWidth: 0.45
  seeThroughOrdered: 0
  rmsCount: 1
  hitFxInitialIntensity: 1
  hitFxMode: 2
  hitFxFadeOutDuration: 0.25
  hitFxColor: {r: 0.93333334, g: 1.1058824, b: 2, a: 1}
  hitFxRadius: 0.5
--- !u!114 &656694101
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 656694097}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5009cbb7e54994bb586cde7a70f34e6b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  highlightOnHover: 1
  triggerMode: 1
  raycastCamera: {fileID: 965526243}
  raycastSource: 0
  minDistance: 0
  maxDistance: 0
  respectUI: 1
  volumeLayerMask:
    serializedVersion: 2
    m_Bits: 0
  selectOnClick: 0
  selectedProfile: {fileID: 0}
  selectedAndHighlightedProfile: {fileID: 0}
  singleSelection: 0
  toggle: 0
--- !u!23 &656694102
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2319392, guid: d7720c2417695eb45bd70f6d10a17068,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 656694097}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 3
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 5b12cf4be3e7c5149a5f24108ee6a551, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!136 &656694103
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 656694097}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  m_Radius: 0.5
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0, y: 0, z: 0}
--- !u!33 &656694104
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3351936, guid: d7720c2417695eb45bd70f6d10a17068,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 656694097}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &965526239
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100000, guid: 97b34cadc45c6d94abd78dfc533fc3b2,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 965526244}
  - component: {fileID: 965526243}
  - component: {fileID: 965526240}
  - component: {fileID: 965526241}
  m_Layer: 0
  m_Name: Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &965526240
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 8100000, guid: 97b34cadc45c6d94abd78dfc533fc3b2,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 965526239}
  m_Enabled: 1
--- !u!114 &965526241
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 965526239}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a79441f348de89743a2939f4d699eac1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_RenderShadows: 1
  m_RequiresDepthTextureOption: 2
  m_RequiresOpaqueTextureOption: 2
  m_CameraType: 0
  m_Cameras: []
  m_RendererIndex: -1
  m_VolumeLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_VolumeTrigger: {fileID: 0}
  m_RenderPostProcessing: 0
  m_Antialiasing: 0
  m_AntialiasingQuality: 2
  m_StopNaN: 0
  m_Dithering: 0
  m_ClearDepth: 1
  m_RequiresDepthTexture: 0
  m_RequiresColorTexture: 0
  m_Version: 2
--- !u!20 &965526243
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2000000, guid: 97b34cadc45c6d94abd78dfc533fc3b2,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 965526239}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.25, g: 0.25, b: 0.25, a: 0.003921569}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.1
  far clip plane: 20
  field of view: 40
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294963199
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 0
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &965526244
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400000, guid: 97b34cadc45c6d94abd78dfc533fc3b2,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 965526239}
  m_LocalRotation: {x: 0.0010074005, y: 0.91790915, z: 0.0024223588, w: -0.39678213}
  m_LocalPosition: {x: 2.4200273, y: 0.72167146, z: 2.4644732}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1165184420
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 182018, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1165184421}
  - component: {fileID: 1165184424}
  - component: {fileID: 1165184423}
  - component: {fileID: 1165184422}
  m_Layer: 0
  m_Name: CalibrationWallRearLeft
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 65
  m_IsActive: 1
--- !u!4 &1165184421
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 464034, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1165184420}
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071067}
  m_LocalPosition: {x: 0, y: 2.5, z: -2.5}
  m_LocalScale: {x: 0.5, y: 1, z: 0.5}
  m_Children: []
  m_Father: {fileID: 165230858}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &1165184422
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2391912, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1165184420}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 0062779a7c303451ab69ea6fc7893dc8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &1165184423
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 6494426, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1165184420}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!33 &1165184424
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3305810, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1165184420}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1179472412
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100006, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1179472413}
  - component: {fileID: 1179472416}
  - component: {fileID: 1179472415}
  - component: {fileID: 1179472414}
  m_Layer: 0
  m_Name: CalibrationFloor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 65
  m_IsActive: 1
--- !u!4 &1179472413
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400006, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1179472412}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.5, y: 1, z: 0.5}
  m_Children: []
  m_Father: {fileID: 1694850532}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &1179472414
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2300004, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1179472412}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: d681c1d72c3c16149abd2f0f25ca628c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &1179472415
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 6400004, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1179472412}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!33 &1179472416
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3300004, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1179472412}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1392572690
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 145214, guid: d7720c2417695eb45bd70f6d10a17068,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1392572691}
  - component: {fileID: 1392572696}
  - component: {fileID: 1392572694}
  - component: {fileID: 1392572693}
  - component: {fileID: 1392572692}
  - component: {fileID: 1392572695}
  m_Layer: 1
  m_Name: Target 2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1392572691
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 473706, guid: d7720c2417695eb45bd70f6d10a17068,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1392572690}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0.49, z: 1}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1392572692
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1392572690}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 888380afc233049ce9e618f9f36c8ba8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  profile: {fileID: 0}
  profileSync: 1
  previewInEditor: 1
  camerasLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  effectGroup: 0
  effectGroupLayer:
    serializedVersion: 2
    m_Bits: 4294967295
  effectNameFilter: 
  combineMeshes: 0
  alphaCutOff: 0
  cullBackFaces: 1
  ignoreObjectVisibility: 0
  reflectionProbes: 0
  GPUInstancing: 1
  normalsOption: 0
  ignore: 0
  _highlighted: 0
  fadeInDuration: 1
  fadeOutDuration: 1
  flipY: 0
  constantWidth: 1
  subMeshMask: -1
  overlay: 0
  overlayColor: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
  overlayAnimationSpeed: 0
  overlayMinIntensity: 0
  overlayBlending: 1
  overlayTexture: {fileID: 0}
  overlayTextureScale: 1
  outline: 1
  outlineColor: {r: 1, g: 1, b: 1, a: 1}
  outlineWidth: 0.4
  outlineQuality: 0
  outlineDownsampling: 2
  outlineVisibility: 0
  outlineBlitDebug: 0
  outlineIndependent: 0
  glow: 1.32
  glowWidth: 3
  glowQuality: 2
  glowDownsampling: 2
  glowHQColor: {r: 0.19117647, g: 0.36409733, b: 1, a: 1}
  glowDithering: 1
  glowMagicNumber1: 0.75
  glowMagicNumber2: 0.5
  glowAnimationSpeed: 1
  glowVisibility: 0
  glowBlitDebug: 0
  glowBlendPasses: 1
  glowPasses:
  - offset: 4
    alpha: 0.1
    color: {r: 0.64, g: 1, b: 0, a: 1}
  - offset: 3
    alpha: 0.2
    color: {r: 0.64, g: 1, b: 0, a: 1}
  - offset: 2
    alpha: 0.3
    color: {r: 0.64, g: 1, b: 0, a: 1}
  - offset: 1
    alpha: 0.4
    color: {r: 0.64, g: 1, b: 0, a: 1}
  innerGlow: 0
  innerGlowWidth: 1
  innerGlowColor: {r: 1, g: 1, b: 1, a: 1}
  innerGlowVisibility: 0
  targetFX: 0
  targetFXTexture: {fileID: 2800000, guid: 1de3c566a6c8c405b9f6f453137273ec, type: 3}
  targetFXColor: {r: 1, g: 1, b: 1, a: 1}
  targetFXCenter: {fileID: 0}
  targetFXRotationSpeed: 50
  targetFXInitialScale: 2
  targetFXEndScale: 1.5
  targetFXScaleToRenderBounds: 1
  targetFXAlignToGround: 0
  targetFXFadePower: 32
  targetFXGroundMaxDistance: 10
  targetFXGroundLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  targetFXTransitionDuration: 0.5
  targetFXStayDuration: 2
  targetFXVisibility: 1
  seeThrough: 0
  seeThroughOccluderMask:
    serializedVersion: 2
    m_Bits: 4294967295
  seeThroughOccluderThreshold: 0.3
  seeThroughOccluderMaskAccurate: 0
  seeThroughOccluderCheckInterval: 1
  seeThroughOccluderCheckIndividualObjects: 0
  seeThroughDepthOffset: 0
  seeThroughMaxDepth: 0
  seeThroughIntensity: 0.8
  seeThroughTintAlpha: 0.5
  seeThroughTintColor: {r: 1, g: 0, b: 0, a: 1}
  seeThroughNoise: 1
  seeThroughBorder: 0
  seeThroughBorderColor: {r: 0, g: 0, b: 0, a: 1}
  seeThroughBorderWidth: 0.45
  seeThroughOrdered: 0
  rmsCount: 1
  hitFxInitialIntensity: 1
  hitFxMode: 2
  hitFxFadeOutDuration: 0.25
  hitFxColor: {r: 1, g: 1, b: 1, a: 1}
  hitFxRadius: 0.5
--- !u!114 &1392572693
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1392572690}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5009cbb7e54994bb586cde7a70f34e6b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  highlightOnHover: 1
  triggerMode: 1
  raycastCamera: {fileID: 965526243}
  raycastSource: 0
  minDistance: 0
  maxDistance: 0
  respectUI: 1
  volumeLayerMask:
    serializedVersion: 2
    m_Bits: 0
  selectOnClick: 0
  selectedProfile: {fileID: 0}
  selectedAndHighlightedProfile: {fileID: 0}
  singleSelection: 0
  toggle: 0
--- !u!23 &1392572694
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2375834, guid: d7720c2417695eb45bd70f6d10a17068,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1392572690}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 3
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 958abb01761e1dc4ebf9ffdf8252d20e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!136 &1392572695
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1392572690}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  m_Radius: 0.5
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0, y: 0, z: 0}
--- !u!33 &1392572696
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3335484, guid: d7720c2417695eb45bd70f6d10a17068,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1392572690}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1435719951
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100002, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1435719952}
  - component: {fileID: 1435719955}
  - component: {fileID: 1435719954}
  - component: {fileID: 1435719953}
  m_Layer: 0
  m_Name: CalibrationWallFrontLeft
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 65
  m_IsActive: 1
--- !u!4 &1435719952
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400002, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1435719951}
  m_LocalRotation: {x: 0.5000003, y: -0.5000001, z: -0.49999964, w: -0.50000006}
  m_LocalPosition: {x: 2.4999998, y: 2.5, z: 0}
  m_LocalScale: {x: 0.4999999, y: 1, z: 0.4999999}
  m_Children: []
  m_Father: {fileID: 165230858}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &1435719953
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2300002, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1435719951}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 0062779a7c303451ab69ea6fc7893dc8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &1435719954
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 6400002, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1435719951}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!33 &1435719955
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3300002, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1435719951}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1525858953
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100000, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1525858954}
  - component: {fileID: 1525858957}
  - component: {fileID: 1525858956}
  - component: {fileID: 1525858955}
  m_Layer: 0
  m_Name: CalibrationWallFrontRight
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 65
  m_IsActive: 1
--- !u!4 &1525858954
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400000, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1525858953}
  m_LocalRotation: {x: 0.70710695, y: 0.0000002682209, z: 0.00000021584746, w: -0.7071066}
  m_LocalPosition: {x: 0, y: 2.500002, z: 2.5000014}
  m_LocalScale: {x: 0.49999997, y: 1, z: 0.5}
  m_Children: []
  m_Father: {fileID: 165230858}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &1525858955
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2300000, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1525858953}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 0062779a7c303451ab69ea6fc7893dc8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &1525858956
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 6400000, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1525858953}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!33 &1525858957
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3300000, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1525858953}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1605869162
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1605869163}
  - component: {fileID: 1605869165}
  - component: {fileID: 1605869164}
  m_Layer: 5
  m_Name: Panel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1605869163
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1605869162}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 613470777}
  m_Father: {fileID: 1819248545}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 0.1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 1}
--- !u!114 &1605869164
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1605869162}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0, b: 0, a: 0.641}
  m_RaycastTarget: 1
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1605869165
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1605869162}
  m_CullTransparentMesh: 0
--- !u!1 &1694850531
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 100004, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1694850532}
  m_Layer: 0
  m_Name: StaticGeometry
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 4294967295
  m_IsActive: 1
--- !u!4 &1694850532
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 400004, guid: a3244c08ab4d16f45b564401c17e84d4,
    type: 2}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1694850531}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1179472413}
  - {fileID: 165230858}
  - {fileID: 567592205}
  m_Father: {fileID: 0}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1819248541
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1819248545}
  - component: {fileID: 1819248544}
  - component: {fileID: 1819248543}
  - component: {fileID: 1819248542}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1819248542
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1819248541}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1819248543
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1819248541}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
--- !u!223 &1819248544
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1819248541}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!224 &1819248545
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1819248541}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_Children:
  - {fileID: 1605869163}
  m_Father: {fileID: 0}
  m_RootOrder: 9
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!1 &2118005892
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2118005894}
  - component: {fileID: 2118005893}
  m_Layer: 0
  m_Name: HitFx Script
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2118005893
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2118005892}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2533fb71c5d747c58192bcd7d6cd276, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hitSound: {fileID: 8300000, guid: 9c2f7decce97e409a8ecfc1181535dc2, type: 3}
--- !u!4 &2118005894
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2118005892}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -4.729038, y: -3.3103254, z: -4.729038}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}

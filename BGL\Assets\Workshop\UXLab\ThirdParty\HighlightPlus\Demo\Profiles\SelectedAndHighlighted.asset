%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8e9253636bf2648bd813257f451f8486, type: 3}
  m_Name: SelectedAndHighlighted
  m_EditorClassIdentifier: 
  effectGroup: 0
  effectGroupLayer:
    serializedVersion: 2
    m_Bits: 4294967295
  effectNameFilter: 
  combineMeshes: 0
  alphaCutOff: 0
  cullBackFaces: 1
  depthClip: 0
  normalsOption: 0
  fadeInDuration: 0
  fadeOutDuration: 0
  constantWidth: 1
  overlay: 0.5
  overlayColor: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
  overlayAnimationSpeed: 1
  overlayMinIntensity: 0.5
  overlayBlending: 1
  outline: 1
  outlineColor: {r: 0, g: 0, b: 0, a: 1}
  outlineWidth: 1.5
  outlineQuality: 1
  outlineDownsampling: 2
  outlineOptimalBlit: 1
  outlineVisibility: 0
  outlineIndependent: 0
  glow: 1
  glowWidth: 0.5
  glowQuality: 1
  glowDownsampling: 2
  glowHQColor: {r: 0.64, g: 1, b: 0, a: 1}
  glowDithering: 1
  glowOptimalBlit: 1
  glowMagicNumber1: 0.75
  glowMagicNumber2: 0.5
  glowAnimationSpeed: 1
  glowVisibility: 0
  glowBlendPasses: 1
  glowPasses:
  - offset: 4
    alpha: 0.1
    color: {r: 0.64, g: 1, b: 0, a: 1}
  - offset: 3
    alpha: 0.2
    color: {r: 0.64, g: 1, b: 0, a: 1}
  - offset: 2
    alpha: 0.3
    color: {r: 0.64, g: 1, b: 0, a: 1}
  - offset: 1
    alpha: 0.4
    color: {r: 0.64, g: 1, b: 0, a: 1}
  innerGlow: 0.5
  innerGlowWidth: 1
  innerGlowColor: {r: 1, g: 1, b: 1, a: 1}
  innerGlowVisibility: 0
  targetFX: 0
  targetFXTexture: {fileID: 0}
  targetFXColor: {r: 1, g: 1, b: 1, a: 1}
  targetFXRotationSpeed: 50
  targetFXInitialScale: 4
  targetFXEndScale: 1.5
  targetFXTransitionDuration: 0.5
  targetFXStayDuration: 1.5
  targetFXVisibility: 1
  seeThrough: 2
  seeThroughOccluderMask:
    serializedVersion: 2
    m_Bits: 4294967295
  seeThroughOccluderThreshold: 0.4
  seeThroughOccluderCheckInterval: 1
  seeThroughIntensity: 0.8
  seeThroughTintAlpha: 0.5
  seeThroughTintColor: {r: 1, g: 0, b: 0, a: 1}
  seeThroughNoise: 1
  seeThroughBorder: 0
  seeThroughBorderColor: {r: 0, g: 0, b: 0, a: 1}
  seeThroughBorderWidth: 0.45
